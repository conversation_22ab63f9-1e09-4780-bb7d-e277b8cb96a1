package com.coupon.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coupon.dto.UserDto;
import com.coupon.entity.MUserEntity;
import com.coupon.vo.AreaRegionSelectVo;
import com.coupon.vo.BdVo;
import com.coupon.vo.UserInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("coupon")
public interface MUserMapper extends BaseMapper<MUserEntity> {

    List<AreaRegionSelectVo> getAreaRegionSelect(@Param("fkCompanyId")Long fkCompanyId);

    List<BdVo> getBDByAreaRegion(@Param("fkAreaRegionId")Long fkAreaRegionId);

    int checkUserExists(@Param("userDto") UserDto userDto);


    UserInfoVo getDetail(@Param("userDto") UserDto userDto);
}
