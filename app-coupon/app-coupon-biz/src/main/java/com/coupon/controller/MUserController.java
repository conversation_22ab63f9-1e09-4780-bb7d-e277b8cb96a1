package com.coupon.controller;

import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.coupon.dto.UserDto;
import com.coupon.service.MUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequiredArgsConstructor
@RequestMapping("/user" )
@Tag(description = "user" , name = "用户管理" )
//@Inner(value = false)
public class MUserController {

    @Resource
    private MUserService mUserService;

//    @ApiOperation(value = "大区下拉", notes = "")
    @Operation(summary = "大区下拉" , description = "大区下拉" )
    @GetMapping("getAreaRegionSelect")
    @Inner(false)
    public R getAreaRegionSelect(@RequestParam(value = "fkCompanyId", required = false)Long fkCompanyId) {
        return R.ok(mUserService.getAreaRegionSelect(fkCompanyId));
    }

    @Operation(summary = "根据大区选择BD" , description = "根据大区选择BD" )
    @GetMapping("getBDByAreaRegion")
    @Inner(false)
    public R getBDByAreaRegion(@RequestParam(value = "fkAreaRegionId")Long fkAreaRegionId) {
        return R.ok(mUserService.getBDByAreaRegion(fkAreaRegionId));
    }


    @Operation(summary = "员工是否存在" , description = "员工是否存在" )
    @PostMapping("checkUserExists")
    @Inner(false)
    public R checkUserExists(@RequestBody UserDto userDto) {
        return R.ok(mUserService.userExists(userDto));
    }

    @Operation(summary = "获取用户信息" , description = "获取用户信息" )
    @GetMapping ("/getDetail")
    public R getDetail() {
        return mUserService.getDetail();
    }

    @Operation(summary = "修改用户信息" , description = "修改用户信息" )
    @PostMapping ("/update")
    public R updateUser(@RequestBody @Valid UserDto userDto) {
        return mUserService.updateUser(userDto);
    }


}
