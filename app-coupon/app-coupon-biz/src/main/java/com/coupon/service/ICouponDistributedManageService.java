package com.coupon.service;

import com.common.core.util.R;
import com.coupon.dto.AddCouponDto;
import com.coupon.dto.ImportCouponDto;
import com.coupon.dto.VerificationCouponDto;
import com.coupon.entity.MAppIntroductionEntity;
import com.coupon.vo.CouponImport;
import com.coupon.vo.ImportCouponVo;
import com.coupon.vo.VerifyCationData;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ICouponDistributedManageService {
    R importCoupon(Long couponTypeId, MultipartFile[] files) throws Exception;

    R addCoupon(AddCouponDto addCouponDto) throws Exception;

    R importVerification(Long couponTypeId, MultipartFile[] files) throws Exception;

    R verificationCoupon(VerificationCouponDto verificationCouponDto) throws Exception;

    R updateIntroduction(MAppIntroductionEntity mAppIntroductionEntity) throws Exception;
}
