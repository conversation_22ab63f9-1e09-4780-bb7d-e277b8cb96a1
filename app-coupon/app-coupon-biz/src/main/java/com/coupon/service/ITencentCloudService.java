package com.coupon.service;


import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface ITencentCloudService {

    /**
     * @describe 上传文件的方法
     * @methods uploadObject 方法名
     * @parameter fileUrl 上传文件地址
     * @parameter fileKey 文件对象名称
     * @parameter @return 对象列表
     */
    Boolean uploadObject(boolean isPub, String bucketName, MultipartFile file, String fileKey);


   /* *//**
     * 下载对象,isPub:是否从公开桶下载（下载不同的桶区分）
     *//*
    void downLoadObject(MFilePartnerDto fileVo, HttpServletResponse response, Boolean isPub, Boolean isShare, Boolean isHtiPub);*/

}
