package com.coupon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AddCouponTypeDto {
//    private String title;
//    private String subTitle;
//    private String description;
//    private String price;
//    private boolean isActive;
//    private int recommendedType;
//    private int discountMethod;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime validPeriodStart;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime validPeriodEnd;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime redeemPeriodStart;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime redeemPeriodEnd;
//    // 是否置顶
//    private Boolean isTop;
//    private List<String> fileGuids;
//    private String codeImage;

    @Schema(description = "优惠券标题")
    private String title;

    @Schema(description = "优惠券说明/副标题")
    private String subTitle;

    @Schema(description = "优惠券使用规则")
    private String description;

    @Schema(description = "优惠金额（字符串格式，单位元）")
    private String price;

    @Schema(description = "是否上架：true为上架，false为下架")
    private boolean isActive;

    @Schema(description = "推荐类型：0=不做推荐，1=新优惠券，2=热门优惠券")
    private int recommendedType;

    @Schema(description = "优惠方式：1=满减，2=折扣券")
    private int discountMethod;

    @Schema(description = "优惠券有效期开始时间，格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime validPeriodStart;

    @Schema(description = "优惠券有效期结束时间，格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime validPeriodEnd;

    @Schema(description = "优惠券兑换开始时间（可为空），格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime redeemPeriodStart;

    @Schema(description = "优惠券兑换结束时间（可为空），格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime redeemPeriodEnd;

    @Schema(description = "是否置顶，用于排序显示：true为置顶")
    private Boolean isTop;

    @Schema(description = "已上传的二维码图片UUID集合")
    private List<String> fileGuids;

    @Schema(description = "二维码图片UUID（与 code_image 字段对应）")
    private String codeImage;

    @Schema(description = "网页规则标题（rule_title）")
    private String ruleTitle;

    @Schema(description = "排序权重，倒序排列，数值越大越靠前")
    private Integer viewOrder;
}
