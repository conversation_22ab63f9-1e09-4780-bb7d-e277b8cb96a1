package com.coupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("m_coupon")
@Schema(description = "优惠卷")
public class MCouponEntity {
    @TableId(type = IdType.AUTO)
    @Schema(description = "优惠卷Id")
    private Long id;

    @Schema(description = "外键，优惠卷类型Id")
    private Long fkCouponTypeId;

    @Schema(description = "兑换码")
    private String code;

    @Schema(description = "是否已领取：0否/1是")
    private Boolean isTaken;

    @Schema(description = "是否已兑换：0否/1是")
    private Boolean isUsed;

    @Schema(description = "是否激活：0否/1是")
    private Boolean isActive;

    @Schema(description = "备用字段1")
    private String reservedField1;

    @Schema(description = "备用字段2")
    private String reservedField2;

    @Schema(description = "备用字段3")
    private String reservedField3;

    @Schema(description = "备用字段4")
    private String reservedField4;

    @Schema(description = "备用字段5")
    private String reservedField5;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;
}
