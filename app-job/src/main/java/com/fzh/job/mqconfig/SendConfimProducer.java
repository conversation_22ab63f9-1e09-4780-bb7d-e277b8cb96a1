package com.fzh.job.mqconfig;

import com.partner.mqmessage.MQCommissionMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SendConfimProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.producer.groupConfim:send_confim_topic}")
    private String TOPIC;


    /**
     * 发送异步消息
     *
     * @param -消息体
     */
    public void sendCommissionMessage(MQCommissionMessage messageParams) {
        log.info("SendConfimProducer发送确认名单信息，messageParams={},topic={}", messageParams, TOPIC);

        rocketMQTemplate.asyncSend(TOPIC, messageParams, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("SendConfimProducer发送确认名单信息发送成功，messageParams={}，消息ID={}", messageParams, sendResult.getMsgId());
            }
            @Override
            public void onException(Throwable throwable) {
                log.error("SendConfimProducer发送确认名单信息发送失败，messageParams={}，异常信息={}", messageParams, throwable.getMessage());
                // 这里可以做失败告警、日志记录等处理
            }
        });
    }

}
