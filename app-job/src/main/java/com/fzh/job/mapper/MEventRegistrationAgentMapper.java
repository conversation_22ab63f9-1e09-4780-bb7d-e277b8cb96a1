package com.fzh.job.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.EventEntity;
import com.partner.entity.MEventRegistrationAgentEntity;
import com.partner.vo.job.UserMEventVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【m_event_registration_agent】的数据库操作Mapper
 * @createDate 2025-05-06 17:05:04
 * @Entity com.partner.entity.MEventRegistrationAgent
 */
@Mapper
@DS("saledb")
public interface MEventRegistrationAgentMapper extends BaseMapper<MEventRegistrationAgentEntity> {

    List<EventEntity> selectDetail();


    List<UserMEventVo> selectDetailList(@Param("meventId")  Long meventId);

}




