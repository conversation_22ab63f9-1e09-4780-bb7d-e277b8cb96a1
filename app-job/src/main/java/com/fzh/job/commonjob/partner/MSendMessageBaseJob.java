package com.fzh.job.commonjob.partner;

import com.alibaba.fastjson.JSONObject;
import com.fzh.job.dto.BaseParamDto;
import com.fzh.job.mqconfig.SendFactory;
import com.fzh.job.service.MEventMessageSendService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.xxl.job.core.biz.model.ReturnT.FAIL;
import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

@Slf4j
@Component
public class MSendMessageBaseJob {

    @Resource
    SendFactory sendFactory;

    @XxlJob("MSendMessageBaseJob")
    public ReturnT<String> createJob(String paramDto) {
        log.error("Start MSendMessageBaseJob createJob paramDto:{}", paramDto);
        String paramDtotmp= XxlJobHelper.getJobParam();

        BaseParamDto params = JSONObject.parseObject(paramDtotmp, BaseParamDto.class);
        Boolean flag=sendFactory.getMessageObject(params.getBaseCode()).sendMQMessage(params);
        XxlJobHelper.log("This is a MSendMessageBaseJob." );
        if(flag){
            return SUCCESS;
        }else {
            return FAIL;
        }
    }






}
