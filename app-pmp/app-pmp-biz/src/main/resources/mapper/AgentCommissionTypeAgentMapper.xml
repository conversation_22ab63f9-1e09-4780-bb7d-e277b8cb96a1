<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pmp.mapper.AgentCommissionTypeAgentMapper">

    <select id="getAgentCommissionTypeAgentById" resultType="com.pmp.entity.AgentCommissionTypeAgent">
        select ta.*,
               t.is_show_only
        from r_agent_commission_type_agent ta
                 inner join u_agent_commission_type t on t.id = ta.fk_agent_commission_type_id and t.is_active = 1
        where ta.fk_agent_id = #{agentId}
        order by ta.id desc
        limit 1
    </select>
</mapper>