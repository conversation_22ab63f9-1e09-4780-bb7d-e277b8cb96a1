<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pmp.mapper.InstitutionCommissionLabelMapper">

    <select id="selectCustomizeLabel" resultType="com.pmp.vo.institution.CustomizeLabelVo">
        select l.id as labelId,
        l.label_name as labelName,
        l.fk_label_type_id as labelTypeId,
        l.label_key as labelKey,
        l.color as color,
        l.remark as remark,
        l.icon_name as iconName,
        l.view_order as viewOrder,
        t.type_name as labelTypeName,
        t.type_key as labelTypeKey
        from ais_platform_center.u_label l
        left join ais_platform_center.u_label_type t on l.fk_label_type_id = t.id
        where l.id in
        <foreach collection="labelIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by l.view_order desc
    </select>
</mapper>