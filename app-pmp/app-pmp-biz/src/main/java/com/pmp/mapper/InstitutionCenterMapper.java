package com.pmp.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pmp.dto.InstitutionDto;
import com.pmp.vo.institution.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * ais_institution_center
 */
@Mapper
@DS("institution")
public interface InstitutionCenterMapper {

    List<CountryVo> queryCountryList(@Param("institutionIds") List<Long> institutionIds);

    /**
     * 学校列表
     *
     * @param page
     * @param param
     * @return
     */
    IPage<InstitutionVo> institutionList(@Param("page") Page page,
                                         @Param("param") InstitutionDto param,
                                         @Param("institutionIds") List<Long> institutionIds,
                                         @Param("year") Integer year,
                                         @Param("sortType") Integer sortType);

    /**
     * 学校类型列表
     *
     * @return
     */
    List<InstitutionTypeVo> queryInstitutionTypeList();


    /**
     * 集团列表
     *
     * @param providerIds
     * @return
     */
    List<GroupVo> queryGroupList(@Param("providerIds") List<Long> providerIds);

    /**
     * 学校详情
     *
     * @param institutionId
     * @return
     */
    InstitutionVo institutionDetail(@Param("institutionId") Long institutionId, @Param("year") Integer year);


    /**
     * 提供商集团map
     *
     * @param providerIds
     * @return
     */
    List<ProviderGroupVo> providerGroupMap(@Param("providerIds") List<Long> providerIds);

    /**
     * 国家名称列表
     *
     * @param countryIds
     * @return
     */
    String countryNameList(@Param("countryIds") List<Long> countryIds);


    /**
     * 根据国家ID查询国家名称
     *
     * @param countryIds
     * @return
     */
    List<CountryVo> queryCountryListByIds(@Param("countryIds") List<Long> countryIds);

    /**
     * 州省列表
     *
     * @param countryIds
     * @return
     */
    List<AreaStateVo> queryAreaStateList(@Param("countryIds") List<Long> countryIds);

    /**
     * 适用地区列表
     *
     * @param countryIds
     * @return
     */
    List<CountryVo> territoryList(@Param("countryIds") List<Long> countryIds);

    /**
     * 高佣金学校
     *
     * @param date
     * @param institutionIds
     * @return
     */
    List<InstitutionVo> selectHighCommissionInstitution(@Param("date") String date,
                                                        @Param("institutionIds") List<Long> institutionIds,
                                                        @Param("platformCode") String platformCode);

}
