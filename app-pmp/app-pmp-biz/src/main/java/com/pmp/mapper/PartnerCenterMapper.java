package com.pmp.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.pmp.vo.partner.PartnerUserVo;
import com.pmp.vo.partner.SystemUserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * ais_institution_center
 */
@Mapper
@DS("partner")
public interface PartnerCenterMapper {

    /**
     * 根据用户id查询伙伴用户信息
     *
     * @param userId
     * @return
     */
    PartnerUserVo getPartnerUser(@Param("userId") Long userId);

    /**
     * 根据账户查询分公司ID
     *
     * @param account
     * @return
     */
    Long getCompanyIdByAccount(@Param("account") String account);

    /**
     * 根据账户查询系统用户信息
     *
     * @param account
     */
    SystemUserVo getSystemUserByAccount(@Param("account") String account);


}
