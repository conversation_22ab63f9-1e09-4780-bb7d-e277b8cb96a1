package com.pmp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/4  17:41
 * @Version 1.0
 */
@Data
public class PageDto {

    @Schema(description = "当前页")
    @NotNull(message = "当前页不能为空")
    private Integer current;

    @Schema(description = "每页大小")
    @NotNull(message = "每页大小不能为空")
    private Integer pageSize;
}
