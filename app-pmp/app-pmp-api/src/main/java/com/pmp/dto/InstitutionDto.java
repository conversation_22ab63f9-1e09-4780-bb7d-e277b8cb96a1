package com.pmp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/4  15:48
 * @Version 1.0
 */
@Data
public class InstitutionDto extends PageDto {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "开始日期")
    private Date startDate;

    @Schema(description = "国家ID")
    private Long countryId;

    @Schema(description = "州省ID集合")
    private List<Long> stateIdList;

    @Schema(description = "学校类型ID集合")
    private List<Long> institutionTypeIdList;

    @Schema(description = "集团ID集合")
    private List<Long> institutionGroupIdList;

    @Schema(description = "课程等级ID集合")
    private List<Long> majorLevelIdList;

    @Schema(description = "适用国家ID集合")
    private List<Long> applyCountryIdList;

    @Schema(description = "学校名称")
    private String institutionName;

    @Schema(description = "佣金标题")
    private String commissionTitle;

    @Schema(description = "学校排名排序")
    private String qsSort;

    @Schema(description = "学校排序")
    private String institutionSort;

    @Schema(description = "代理ID")
    private Long agentId;

    @Schema(description = "分公司ID")
    private Long companyId;

    @Schema(description = "是否查询高佣学校")
    private Boolean isHighCommission;

    @Schema(description = "关键字-院校名称/佣金标题")
    private String keyword;

    @Schema(description = "高佣学校平台Code")
    private String highCommissionCode;

}
