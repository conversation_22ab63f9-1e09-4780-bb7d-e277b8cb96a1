package com.pmp.vo.commission;

import com.pmp.vo.institution.InstitutionVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/4
 * @Version 1.0
 * @apiNote:佣金方案明细-多方案
 */
@Data
public class CommissionDetail {

    @Schema(description = "集团名称")
    private String group;

    @Schema(description = "集团中文名称")
    private String groupChn;

    @Schema(description = "佣金方案摘要及说明")
    private TerritoryInfoVo territoryInfos;

    @Schema(description = "学校详情")
    private InstitutionVo institutionDetail;

    @Schema(description = "佣金明细")
    private AgentCommissionListVo commissions;

    public static List<CommissionDetail> initCommissionDetail(InstitutionVo institutionDetail) {
        CommissionDetail detail = new CommissionDetail();
        detail.setInstitutionDetail(institutionDetail);
        detail.setCommissions(null);
        detail.setGroup(Strings.EMPTY);
        detail.setGroupChn(Strings.EMPTY);
        detail.setTerritoryInfos(null);
        ArrayList list = new ArrayList();
        list.add(detail);
        return list;
    }
}
