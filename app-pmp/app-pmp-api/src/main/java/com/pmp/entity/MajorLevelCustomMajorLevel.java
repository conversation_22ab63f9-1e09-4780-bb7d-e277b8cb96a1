package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_major_level_custom_major_level")
public class MajorLevelCustomMajorLevel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户自定义课程等级Id")
    private Long fkMajorLevelCustomId;

    @Schema(description = "系统定义课程等级Id")
    private Long fkMajorLevelId;
}
