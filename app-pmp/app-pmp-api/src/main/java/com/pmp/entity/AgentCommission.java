package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("m_agent_commission")
public class AgentCommission extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     **/
    @Schema(description = "学校提供商Id")
    private Long fkInstitutionProviderId;

    /**
     * 代理佣金方案Id
     **/
    @Schema(description = "代理佣金方案Id")
    private Long fkAgentCommissionPlanId;

    /**
     * 佣金类型
     **/
    @Schema(description = "佣金类型：1课程/2阶梯/3组合")
    private Integer commissionType;

    /**
     * 标题
     **/
    @Schema(description = "标题")
    private String title;

    /**
     * 学校提供商Id
     **/
    @Schema(description = "标题（本地语言）")
    private String titleNative;
    /**
     * 阶梯起始学生数
     */
    @Schema(description = "阶梯起始学生数")
    private Integer studentCountMin;
    /**
     * 阶梯结束学生数
     */
    @Schema(description = "阶梯结束学生数")
    private Integer studentCountMax;
    /**
     * 阶梯统计类型：0不追加/1从第1个学生开始计算
     */
    @Schema(description = "阶梯统计类型：0不追加/1从第1个学生开始计算")
    private Integer studentCountType;
    /**
     * 佣金
     */
    @Schema(description = "佣金")
    private BigDecimal commission;
    /**
     * 佣金单位：%/CNY/等货币编号
     */
    @Schema(description = "佣金单位：%/CNY/等货币编号")
    private String commissionUnit;

    @Schema(description = "后续学校提供商Id（不一致才需要选择）")
    private Long fkInstitutionProviderIdFollow;
    /**
     * 后续佣金
     */
    @Schema(description = "后续佣金")
    private BigDecimal followCommission;
    /**
     * 后续佣金单位：%/CNY/等货币编号
     */
    @Schema(description = "后续佣金单位：%/CNY/等货币编号")
    private String followCommissionUnit;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remarkNote;
    /**
     * 备注（本地语言）
     */
    @Schema(description = "备注（本地语言）")
    private String remarkNoteNative;
    /**
     * 组合名称（同组相同）
     */
    @Schema(description = "组合名称（同组相同）")
    private String packageName;
    /**
     * 组合名称（本地语言）（同组相同）
     */
    @Schema(description = "组合名称（本地语言）（同组相同）")
    private String packageNameNative;
    /**
     * 组合key（同组相同，且唯一）
     */
    @Schema(description = "组合key（同组相同，且唯一）")
    private String packageKey;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
    /**
     * 是否激活：0否/1是
     */
    @Schema(description = "是否激活：0否/1是")
    private Integer isActive;

    @Schema(description = "后续备注")
    private String followRemarkNote;

    @Schema(description = "后续备注（中文）")
    private String followRemarkNoteNative;

    @Schema(description = "学校提供商佣金Id（继承，自己添加没有，用处：佣金提醒，学校提供商佣金明细删除时会统一删除继承）")
    private Long fkInstitutionProviderCommissionId;

    @Schema(description = "课程")
    private String course;

}
