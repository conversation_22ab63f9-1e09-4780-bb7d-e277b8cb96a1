package com.payment.rocketmq.producer;

import com.alibaba.fastjson.JSONObject;
import com.payment.rocketmq.msg.OrderMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author:Oliver
 * @Date: 2025/4/30
 * @Version 1.0
 * @apiNote:
 */
@Slf4j
@Component
public class MqProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    public void sendPaySuccessMsg(String topic, String orderNo) {
        log.info("发送微信支付成功mq信息-订单号:{},topic:{}", orderNo, topic);
        OrderMsg orderMsg = OrderMsg.builder().orderNo(orderNo).build();
        rocketMQTemplate.asyncSend(topic, orderMsg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送请求中信息消息发送成功,orderMsg={}，消息ID={},topic:{}", JSONObject.toJSONString(orderMsg), sendResult.getMsgId(), topic);
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送请求中信息消息发送失败，orderMsg={}，异常信息={},topic:{}", JSONObject.toJSONString(orderMsg), throwable.getMessage(), topic);
            }
        });
    }
}
