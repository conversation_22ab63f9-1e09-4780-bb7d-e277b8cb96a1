package com.payment.rocketmq;

/**
 * @Author:<PERSON>
 * @Date: 2025/3/20
 * @Version 1.0
 * @apiNote:
 */

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@Slf4j
@ConfigurationProperties(prefix = "rocketmq")
public class RocketMQConfigCheck {

    @Value("${rocketmq.name-server}")
    private String nameServer;

    @PostConstruct
    public void checkProperties() {
        log.info("RocketMQ配置加载: name-server = {}", nameServer);
    }
}
