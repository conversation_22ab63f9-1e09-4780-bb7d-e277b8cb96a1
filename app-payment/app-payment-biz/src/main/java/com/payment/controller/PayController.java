package com.payment.controller;

import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.payment.dto.common.PayRequest;
import com.payment.dto.common.RefundRequestDto;
import com.payment.enums.PayTypeEnum;
import com.payment.service.PayService;
import com.payment.service.wx.WxPayRefundService;
import com.payment.service.wx.WxPayService;
import com.payment.vo.PayResponse;
import com.wechat.pay.java.service.refund.model.Refund;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:
 */
@RestController
@Slf4j
@RequestMapping("/pay")
@Tag(description = "支付管理", name = "支付管理")
public class PayController {

    @Autowired
    private PayService payService;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private WxPayRefundService wxPayRefundService;


    @Operation(summary = "测试接口")
    @PostMapping("/test")
    @Inner(false)
    public R test() {
        return R.ok("测试信息");
    }

    @Operation(summary = "统一支付")
    @PostMapping("/unifiedOrder")
    @Inner(false)
    public R<PayResponse> unifiedOrder(@RequestBody PayRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getOrderNo())) {
            request = PayRequest.builder()
                    .orderNo("20250715101117988638835")
                    .amount(BigDecimal.ONE)
                    .type(PayTypeEnum.WX_JSAPI.getCode())
                    .subject("测试下单")
                    .openId("odHP2631Wz8RUKEl6cxdg-KjO7Ec")
                    .description("测试下单")
                    .appId("wx562db31530336561")
                    .orderId(100L)
                    .build();
        }
        return R.ok(payService.unifiedOrder(request));
    }

    @Operation(summary = "测试微信退款")
    @PostMapping("/testRefund")
    @Inner(false)
    public R<Refund> testRefund(@RequestBody RefundRequestDto requestDto) {
        return R.ok(wxPayRefundService.refund(requestDto));
    }

//    @Operation(summary = "测试微信下单")
//    @PostMapping("/testWxCreateOrder")
//    @Inner(false)
//    public R<Map<String, String>> testWxCreateOrder(@RequestBody WxOrderRequestDto requestDto) {
//        if (Objects.isNull(requestDto) || Objects.isNull(requestDto.getOutTradeNo())) {
//            requestDto = WxOrderRequestDto.builder()
//                    .outTradeNo(UUID.randomUUID().toString().replaceAll("-", ""))
//                    .amount(1)
//                    .openid("odHP2631Wz8RUKEl6cxdg-KjO7Ec")
//                    .description("测试下单")
//                    .build();
//        }
//        return R.ok(wxPayService.createWxOrder(requestDto, null));
//    }
}
