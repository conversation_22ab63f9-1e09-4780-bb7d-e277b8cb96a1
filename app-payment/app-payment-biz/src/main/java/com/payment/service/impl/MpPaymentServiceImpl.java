package com.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payment.entity.InsuranceOrderMpPayment;
import com.payment.mapper.InsuranceOrderMpPaymentMapper;
import com.payment.service.MpPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/7/17
 * @Version 1.0
 * @apiNote: 微信支付服务实现类
 */
@Service
@Slf4j
public class MpPaymentServiceImpl extends ServiceImpl<InsuranceOrderMpPaymentMapper, InsuranceOrderMpPayment> implements MpPaymentService {

    @Autowired
    private InsuranceOrderMpPaymentMapper insuranceOrderMpPaymentMapper;

    @Override
    public void createMpPayment(InsuranceOrderMpPayment mpPayment) {
        this.save(mpPayment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMpPayment(InsuranceOrderMpPayment mpPayment) {
        Long orderId = insuranceOrderMpPaymentMapper.selectInsuranceOrderIdByOrderNo(mpPayment.getOrderNo());
        if (Objects.isNull(orderId)) {
            log.error("订单不存在,订单号:{}", mpPayment.getOrderNo());
            return;
        }
        InsuranceOrderMpPayment orderMpPayment = insuranceOrderMpPaymentMapper.selectList(new LambdaQueryWrapper<InsuranceOrderMpPayment>()
                        .eq(InsuranceOrderMpPayment::getFkInsuranceOrderId, orderId)
                        .orderByDesc(InsuranceOrderMpPayment::getGmtCreate))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(orderMpPayment)) {
            log.error("订单支付信息不存在，订单号：{},订单ID:{}", orderMpPayment.getOrderNo(), orderId);
            return;
        }
        orderMpPayment.setMpPaymentStatus(mpPayment.getMpPaymentStatus());
        orderMpPayment.setGmtModified(new Date());
        orderMpPayment.setMpPaymentOrderNum(mpPayment.getMpPaymentOrderNum());
        insuranceOrderMpPaymentMapper.updateById(orderMpPayment);
        // 更新保险订单状态
        insuranceOrderMpPaymentMapper.updateInsuranceOrderIdByOrderNo(mpPayment.getOrderNo(), mpPayment.getMpPaymentStatus());
    }
}
