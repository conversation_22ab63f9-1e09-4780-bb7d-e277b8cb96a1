package com.payment.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.payment.config.PayConfig;
import com.payment.config.PayProperties;
import com.payment.dto.common.PayRequest;
import com.payment.factory.PayStrategyFactory;
import com.payment.factory.strategy.PayStrategy;
import com.payment.service.PayService;
import com.payment.vo.PayResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PayServiceImpl implements PayService {

    private final PayProperties payProperties;
    private final PayStrategyFactory strategyFactory;
    @Override
    public PayResponse unifiedOrder(PayRequest request) {
        // 找出该 appId 下的所有配置
        List<PayConfig> configs = payProperties.getConfigs().stream()
                .filter(c -> c.getAppId().equals(request.getAppId()))
                .collect(Collectors.toList());

        if (configs.isEmpty()) {
            return PayResponse.fail("500", "未找到appId对应的支付配置");
        }

        Optional<PayConfig> configToUseOpt = Optional.empty();

        if (StringUtils.isNotBlank(request.getType())) {
            configToUseOpt = configs.stream()
                    .filter(c -> request.getType().equals(c.getType()))
                    .findFirst();
        } else if (configs.size() == 1) {
            configToUseOpt = Optional.of(configs.get(0));
        }
        //找不到配置报错
        if (!configToUseOpt.isPresent()) {
            String msg = StringUtils.isBlank(request.getType())
                    ? "该appId配置了多种支付方式,请明确指定支付类型"
                    : "该appId未配置支付类型: " + request.getType();
            return PayResponse.fail("500", msg);
        }

        //根据不同的策略下单
        PayConfig configToUse = configToUseOpt.get();
        PayStrategy strategy = strategyFactory.getStrategy(configToUse.getType());
        return strategy.unifiedOrder(request, configToUse);
    }
}
