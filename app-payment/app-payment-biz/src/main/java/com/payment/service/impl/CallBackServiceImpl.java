package com.payment.service.impl;

import com.payment.service.CallBackService;
import com.payment.service.wx.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class CallBackServiceImpl implements CallBackService {

    @Autowired
    private WxPayService wxPayService;

    @Override
    public String wxCallBack(HttpServletRequest request) {
        log.info("====================>微信支付回调");
        return wxPayService.payNotify( request);
    }
}
