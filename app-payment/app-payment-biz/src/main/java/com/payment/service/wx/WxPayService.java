package com.payment.service.wx;

import com.alibaba.fastjson.JSONObject;
import com.payment.config.PayConfig;
import com.payment.dto.wechat.WxOrderRequestDto;
import com.payment.entity.InsuranceOrderMpPayment;
import com.payment.enums.PayStatusEnum;
import com.payment.rocketmq.producer.MqProducer;
import com.payment.service.MpPaymentService;
import com.payment.service.PayConfigService;
import com.payment.util.PemUtil;
import com.payment.util.WeChatPayUtils;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.partnerpayments.jsapi.model.Transaction;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Map;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/7/15
 * @Version 1.0
 * @apiNote:微信支付相关service
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WxPayService {
    @Autowired
    private PayConfigService payConfigService;
    @Autowired
    private MpPaymentService mpPaymentService;
    @Autowired
    private MqProducer mqProducer;

    /**
     * 创建订单-微信
     *
     * @param orderRequest
     * @return
     */
    @SneakyThrows
    public Map<String, String> createWxOrder(WxOrderRequestDto orderRequest, PayConfig payConfig) {
        try {
            //创建微信支付配置
            Config config = WeChatPayUtils.createWxPayConfig(payConfig);
            JsapiService jsapiService = WeChatPayUtils.createJsapiService(config);

            //构造基本请求参数-appid、商户号、商户订单号、金额、回调地址
            PrepayRequest request = new PrepayRequest();
            request.setAppid(payConfig.getAppId());
            request.setMchid(payConfig.getMchId());
            request.setDescription(orderRequest.getDescription());
            request.setOutTradeNo(orderRequest.getOutTradeNo());
            request.setNotifyUrl(payConfig.getNotifyUrl());
            request.setAttach(orderRequest.getNotifyTopic());

            //订单金额-单位是:分
            Amount amount = new Amount();
            amount.setTotal(orderRequest.getAmount());
            request.setAmount(amount);

            //用户信息
            Payer payer = new Payer();
            payer.setOpenid(orderRequest.getOpenid());
            request.setPayer(payer);

            log.info("微信下单请求参数:{}", JSONObject.toJSONString(request));
            PrepayResponse response = jsapiService.prepay(request);
            log.info("微信下单返回参数:{}", JSONObject.toJSONString(response));

            // 构造支付参数给小程序端
            String prepayId = response.getPrepayId();
            Map<String, String> payParams = WeChatPayUtils.buildPayParams(prepayId, payConfig.getAppId(), payConfig.getWxPrivateKeyPath());
            // 返回前端
            payParams.put("prepayId", prepayId);
            return payParams;
        } catch (Exception e) {
            log.error("微信支付下单异常", e);
            throw new RuntimeException("微信支付下单异常");
        }
    }


    /**
     * 微信支付回调
     *
     * @param request
     * @return
     */
    @SneakyThrows
    public String payNotify(HttpServletRequest request) {
        log.info("=======================>微信支付回调,request:{}", request);

        String body = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
        log.info("微信支付回调,body:{}", body);
        // 获取请求头
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        //微信平台公钥证书ID
        String serial = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");

        log.info("【微信支付回调】头信息 serial={}, signature={},nonce={},timestamp={}", serial, signature, nonce, timestamp);
        try {
            //1. 获取当前平台证书序列号对应配置
            PayConfig payConfig = payConfigService.getByPlatformCertId(serial);
            if (Objects.isNull(payConfig)) {
                log.error("找不到匹配的支付配置，serial={}", serial);
                return "FAIL";
            }

            // 构建 RequestParam（SDK 会自动验签）
            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(serial)
                    .nonce(nonce)
                    .signature(signature)
                    .timestamp(timestamp)
                    .body(body)
                    .build();

            // 使用 SDK 的 NotificationParser 进行验签 + 解密
            // 使用本地加载微信平台公钥，则使用 RSAAutoCertificateConfig是需要上传的证书的
            // 从 classpath 加载私钥 PEM 文件
            ClassPathResource resource = new ClassPathResource(payConfig.getWxPrivateKeyPath());
            InputStream inputStream = resource.getInputStream();

            //使用PemUtil解析出 PrivateKey 对象
            PrivateKey privateKey = PemUtil.loadPrivateKey(inputStream);
            // 加载公钥
            ClassPathResource publicKeyResource = new ClassPathResource(payConfig.getWxPublicKeyPath());
            InputStream publicKeyInputStream = publicKeyResource.getInputStream();
            PublicKey publicKey = PemUtil.loadPublicKey(publicKeyInputStream);

            NotificationConfig config = new RSAPublicKeyConfig.Builder()
                    .merchantId(payConfig.getMchId())
                    .merchantSerialNumber(payConfig.getMchSerialNo())
                    .privateKey(privateKey)
                    .apiV3Key(payConfig.getApiV3Key())
                    .publicKey(publicKey)
                    .publicKeyId(payConfig.getWxPlatformCertId())
                    .build();

            // 初始化 NotificationParser
            NotificationParser parser = new NotificationParser(config);

            // 以支付通知回调为例，验签、解密并转换成 Transaction
            Transaction transaction = parser.parse(requestParam, Transaction.class);
            log.info("微信支付回调解密后参数:{}", JSONObject.toJSONString(transaction));
            log.info("=======================>微信回调解密成功=====>");

            //更新支付记录
            InsuranceOrderMpPayment mpPayment = InsuranceOrderMpPayment.builder()
                    .orderNo(transaction.getOutTradeNo())
                    .mpPaymentOrderNum(transaction.getTransactionId())
                    .mpPaymentStatus(Transaction.TradeStateEnum.SUCCESS.equals(transaction.getTradeState()) ?
                            PayStatusEnum.PAY_SUCCESS.getCode() : PayStatusEnum.PAY_FAIL.getCode())
                    .build();
            mpPaymentService.updateMpPayment(mpPayment);
            //通知业务处理
            String attach = transaction.getAttach();
            if (StringUtils.isNotBlank(attach)) {
                log.info("====================>微信支付回调，通知业务处理,topic:{},订单号:{}", attach, transaction.getOutTradeNo());
                mqProducer.sendPaySuccessMsg(attach, transaction.getOutTradeNo());
            }

        } catch (Exception e) {
            log.error("微信验签失败:{}", e.getMessage());
            log.error("sign verification failed:{}", e);
            return "FAIL";
        }
        return "SUCCESS";
    }

}
