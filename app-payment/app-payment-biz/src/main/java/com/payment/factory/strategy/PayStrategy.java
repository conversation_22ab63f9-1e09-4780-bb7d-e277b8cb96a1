package com.payment.factory.strategy;

import com.payment.config.PayConfig;
import com.payment.dto.common.PayRequest;
import com.payment.vo.PayResponse;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:下单策略
 */
public interface PayStrategy {


    /**
     * 统一下单
     * @param request
     * @param config
     * @return
     */
    PayResponse unifiedOrder(PayRequest request, PayConfig config);
}
