package com.payment.factory;

import com.payment.factory.strategy.PayStrategy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author:<PERSON>
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:下单策略工厂
 */
@Component
public class PayStrategyFactory {

    private final Map<String, PayStrategy> strategyMap;

    public PayStrategyFactory(Map<String, PayStrategy> strategyMap) {
        this.strategyMap = strategyMap;
    }

    public PayStrategy getStrategy(String type) {
        PayStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的支付类型: " + type);
        }
        return strategy;
    }
}
