package com.payment.factory.impl;

import com.payment.config.PayConfig;
import com.payment.dto.common.PayRequest;
import com.payment.factory.strategy.PayStrategy;
import com.payment.vo.PayResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote: 企业微信支付策略
 */
@Component("ALI")
@Slf4j
public class AliPayStrategy implements PayStrategy {
    @Override
    public PayResponse unifiedOrder(PayRequest request, PayConfig config) {
        log.info("=======================支付宝支付");
        return PayResponse.success("ALI", request.getOrderNo(), null);
    }
}
