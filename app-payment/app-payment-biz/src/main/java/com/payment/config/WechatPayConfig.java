//package com.payment.config;
//
//import com.payment.util.PemUtil;
//import com.wechat.pay.java.core.Config;
//import com.wechat.pay.java.core.RSAPublicKeyConfig;
//import com.wechat.pay.java.service.payments.jsapi.JsapiService;
//import lombok.Data;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.ClassPathResource;
//
//import java.io.InputStream;
//import java.security.PrivateKey;
//import java.security.PublicKey;
//
///**
// * @Author:Oliver
// * @Date: 2025/7/15
// * @Version 1.0
// * @apiNote:微信支付客户端（配置类）
// */
//@Data
//@Configuration
//public class WechatPayConfig {
//
//    @Bean
//    public Config wechatPaySdkConfig(WechatPayProperties properties) throws Exception {
//        // 加载私钥
//        ClassPathResource privateKeyResource = new ClassPathResource(properties.getPrivateKeyPath());
//        PrivateKey privateKey;
//        try (InputStream inputStream = privateKeyResource.getInputStream()) {
//            privateKey = PemUtil.loadPrivateKey(inputStream);
//        }
//
//        // 加载公钥
//        ClassPathResource publicKeyResource = new ClassPathResource(properties.getPublicKeyPath());
//        PublicKey publicKey;
//        try (InputStream inputStream = publicKeyResource.getInputStream()) {
//            publicKey = PemUtil.loadPublicKey(inputStream);
//        }
//
//        // 构建 Config
//        return new RSAPublicKeyConfig.Builder()
//                .merchantId(properties.getMchId())
//                .merchantSerialNumber(properties.getMchSerialNo())
//                .privateKey(privateKey)
//                .apiV3Key(properties.getApiV3Key())
//                .publicKey(publicKey)
//                .publicKeyId(properties.getPlatformCertId())
//                .build();
//    }
//
//    @Bean
//    public JsapiService jsapiService(Config config) {
//        return new JsapiService.Builder().config(config).build();
//    }
//}
