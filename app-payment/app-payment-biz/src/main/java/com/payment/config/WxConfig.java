package com.payment.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author:Oliver
 * @Date: 2025/5/7
 * @Version 1.0
 * @apiNote:
 */
@Configuration
@ConfigurationProperties(prefix = "wx")
@Data
public class WxConfig {

    @Schema(description = "微信小程序appId")
    private String appId;

    @Schema(description = "微信小程序appSecret")
    private String appSecret;
}
