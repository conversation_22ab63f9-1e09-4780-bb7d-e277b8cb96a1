package com.payment.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:支付配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pay")
public class PayProperties {

    @Schema(description = "支付渠道配置")
    private List<PayConfig> configs;
}
