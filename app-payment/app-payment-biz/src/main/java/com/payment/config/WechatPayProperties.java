//package com.payment.config;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
///**
// * @Author:Oliver
// * @Date: 2025/7/15
// * @Version 1.0
// * @apiNote:
// */
//@Configuration
//@ConfigurationProperties(prefix = "wechatpay")
//@Data
//public class WechatPayProperties {
//
//    @Schema(description = "商户号")
//    private String mchId;
//
//    @Schema(description = "商户证书序列号")
//    private String mchSerialNo;
//
//    @Schema(description = "商户API私钥")
//    private String apiV3Key;
//
//    @Schema(description = "商户私钥文件路径")
//    private String privateKeyPath;
//
//    @Schema(description = "商户appid")
//    private String appid;
//
//    @Schema(description = "回调地址")
//    private String notifyUrl;
//
//    @Schema(description = "微信平台公钥文件路径")
//    private String publicKeyPath;
//
//    @Schema(description = "微信平台公钥证书ID")
//    private String platformCertId;
//}
