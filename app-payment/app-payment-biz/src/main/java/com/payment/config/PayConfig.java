package com.payment.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote: 支付商户配置类
 */
@Data
public class PayConfig {

    @Schema(description = "微信小程序appId")
    private String appId;

    @Schema(description = "微信小程序secret")
    private String appSecret;

    @Schema(description = "应用名称")
    private String name;

    @Schema(description = "回调地址")
    private String notifyUrl;

    @Schema(description = "退款回调地址")
    private String refundNotifyUrl;

    @Schema(description = "支付方式")
    private String type;

    @Schema(description = "mq主题-用于支付成功的回调通过mq消息通知相关的服务处理各自的下单成功后的逻辑")
    private String topic;

    /**
     * --------------------------------------------微信支付--------------------------------------
     **/
    @Schema(description = "商户号")
    private String mchId;

    @Schema(description = "商户证书序列号")
    private String mchSerialNo;

    @Schema(description = "商户API私钥")
    private String apiV3Key;

    @Schema(description = "商户私钥文件路径")
    private String wxPrivateKeyPath;

    @Schema(description = "微信平台公钥文件路径")
    private String wxPublicKeyPath;

    @Schema(description = "微信平台公钥证书ID")
    private String wxPlatformCertId;

    /**
     * --------------------------------------------支付宝--------------------------------------
     **/
    @Schema(description = "支付宝应用私钥")
    private String aliAppPrivateKey;

    @Schema(description = "支付宝公钥")
    private String aliPayPublicKey;

    @Schema(description = "支付宝应用公钥")
    private String aliAppPublicCert;
}
