package com.payment.dto.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote: 支付请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayRequest {

    @Schema(description = "appId")
    @NotBlank(message = "appId不能为空")
    private String appId;

    @Schema(description = "支付方式")
    private String type;

    @Schema(description = "商户订单号")
    @NotBlank(message = "商户订单号不能为空")
    private String orderNo;

    @Schema(description = "商户订单ID")
    @NotBlank(message = "商户订单ID不能为空")
    private Long orderId;

    @Schema(description = "金额")
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    @Schema(description = "标题")
    private String subject;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "用户openId")
    private String openId;

    @Schema(description = "下单成功需要通知的topic-用于业务处理自己的逻辑")
    private String notifyTopic;

}
