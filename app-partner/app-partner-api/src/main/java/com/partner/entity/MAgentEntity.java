package com.partner.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-02-21 12:23:37
 */

@Data
@TableName("m_agent")
public class MAgentEntity extends Model<MAgentEntity>{

  @Schema(description = "学生代理Id")
  private Long id;
 

  @Schema(description = "学生代理父Id")
  private Long fkParentAgentId;
 

  @Schema(description = "国家Id")
  private Long fkAreaCountryId;
 

  @Schema(description = "州省Id")
  private Long fkAreaStateId;
 

  @Schema(description = "城市Id")
  private Long fkAreaCityId;
 

  @Schema(description = "代理编号")
  private String num;
 

  @Schema(description = "代理名称")
  private String name;
 

  @Schema(description = "名称备注")
  private String nameNote;
 

  @Schema(description = "个人姓名")
  private String personalName;
 

  @Schema(description = "代理昵称")
  private String nickName;
 

  @Schema(description = "性质：公司/个人/工作室/国际学校/其他")
  private String nature;
 

  @Schema(description = "性质备注")
  private String natureNote;
 

  @Schema(description = "法定代表人")
  private String legalPerson;
 

  @Schema(description = "税号/统一社会信用代码（公司）")
  private String taxCode;
 

  @Schema(description = "身份证号（个人）")
  private String idCardNum;
 

  @Schema(description = "联系地址")
  private String address;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "邀请码，8位数字或字母随机数")
  private String invitationCode;
 

  @Schema(description = "是否结算口，0否/1是")
  private Boolean isSettlementPort;
 

  @Schema(description = "是否关键代理：0否/1是")
  private Boolean isKeyAgent;
 

  @Schema(description = "关键代理失效时间")
  private LocalDateTime keyAgentFailureTime;
 

  @Schema(description = "是否拒收系统邮件：0否/1是")
  private Boolean isRejectEmail;
 

  @Schema(description = "是否渠道代理：0否/1是")
  private Boolean isCustomerChannel;
 

  @Schema(description = "是否激活：0否/1是")
  private Boolean isActive;
 

  @Schema(description = "旧数据id(gea)")
  private String idGea;
 

  @Schema(description = "旧数据id(iae)")
  private String idIae;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
