package com.partner.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_partner_role_menu")
@Schema(description = "伙伴角色和菜单权限关系")
public class PartnerRoleMenu extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "租户Id")
    private Long fkTenantId;

    @Schema(description = "伙伴角色Id")
    private Long fkPartnerRoleId;

    @Schema(description = "系统菜单Id")
    private Long fkMenuId;

    @Schema(description = "权限：0禁止/1允许，禁止权限大于允许权限")
    private Integer permission;
}
