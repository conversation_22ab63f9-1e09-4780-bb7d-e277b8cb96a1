package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-27 15:20:19
 */

@Data
@TableName("u_area_country")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" u_area_country ")
public class UAreaCountryEntity extends Model<UAreaCountryEntity>{

  @Schema(description = "国家Id")
  private Long id;
 

  @Schema(description = "币种编号")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "国家编号")
  private String num;
 

  @Schema(description = "国家名称")
  private String name;
 

  @Schema(description = "国家中文名称")
  private String nameChn;
 

  @Schema(description = "国家区号")
  private String areaCode;
 

  @Schema(description = "首都")
  private String capital;
 

  @Schema(description = "人口")
  private String population;
 

  @Schema(description = "面积")
  private String area;
 

  @Schema(description = "语言")
  private String language;
 

  @Schema(description = "宗教")
  private String religion;
 

  @Schema(description = "时差")
  private String timeDifference;
 

  @Schema(description = "现任国家元首")
  private String president;
 

  @Schema(description = "国旗意义")
  private String flagMeaning;
 

  @Schema(description = "国徽意义")
  private String emblemMeaning;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "业务区域，枚举（US/UK/ANZ/CAN/EUASIA）")
  private String businessAreaKey;
 

  @Schema(description = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
  private String publicLevel;
 

  @Schema(description = "排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

  @Schema(description = "旧数据id(gea)")
  private String idGea;
 

  @Schema(description = "旧数据id(iae)")
  private String idIae;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
