package com.partner.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * partner
 *
 * <AUTHOR>
 * @date 2024-11-22 10:48:25
 */
@Data
@TableName("u_news_type")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "partner")
public class UNewsTypeEntity extends Model<UNewsTypeEntity> {


	/**
	* 新闻类型Id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="新闻类型Id")
    private Long id;

	/**
	* 类型名称
	*/
    @Schema(description="类型名称")
    private String typeName;

	/**
	* 排序，倒序：数字由大到小排列
	*/
    @Schema(description="排序，倒序：数字由大到小排列")
    private Integer viewOrder;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

	/**
	* 创建用户(登录账号)
	*/
    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime gmtModified;

	/**
	* 修改用户(登录账号)
	*/
    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;
}