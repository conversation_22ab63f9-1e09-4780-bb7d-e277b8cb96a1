package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-10 16:43:04
 */

@Data
@TableName("r_payable_plan_settlement_status")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_payable_plan_settlement_status ")
public class RPayablePlanSettlementStatusEntity extends Model<RPayablePlanSettlementStatusEntity>{

  @Schema(description = "关系Id")
  private Long id;
 

  @Schema(description = "财务结算汇总批次号")
  private String numSettlementBatch;
 

  @Schema(description = "应付计划Id")
  private Long fkPayablePlanId;
 

  @Schema(description = "应付计划结算分期表Id")
  private Long fkPayablePlanSettlementInstallmentId;
 

  @Schema(description = "结算状态：0未结算/1结算中/2代理确认/3财务确认")
  private Integer statusSettlement;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
