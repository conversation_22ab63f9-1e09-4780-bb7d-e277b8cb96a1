package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-03 10:46:12
 */

@Data
@TableName("r_student_offer_item_uuid")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_student_offer_item_uuid ")
public class RStudentOfferItemUuidEntity extends Model<RStudentOfferItemUuidEntity>{

  @Schema(description = "学生申请计划UUID关系Id")
  private Long id;
 

  @Schema(description = "学生申请计划Id")
  private Long fkStudentOfferItemId;
 

  @Schema(description = "学生申请计划UUID")
  private String fkStudentOfferItemUuid;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
