package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-12-18 09:43:56
 */

@Data
@TableName("m_message")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_message ")
public class MMessageEntity extends Model<MMessageEntity>{

  @Schema(description = "消息主键Id")
  private Long id;
 

  @Schema(description = "公司Id")
  private Long fkCompanyId;
 

  @Schema(description = "平台应用Id")
  private Long fkPlatformId;
 

  @Schema(description = "平台应用CODE")
  private String fkPlatformCode;
 

  @Schema(description = "标题")
  private String title;
 

  @Schema(description = "权重")
  private Integer weight;
 

  @Schema(description = "跳转方式 1自定义页面/2小程序页面")
  private Integer jumpMode;
 

  @Schema(description = "跳转url")
  private String jumpUrl;
 

  @Schema(description = "网页标题")
  private String webTitle;
 

  @Schema(description = "网页内容")
  private String webMetaDescription;
 

  @Schema(description = "开始时间")
  private LocalDateTime startTime;
 

  @Schema(description = "结束时间")
  private LocalDateTime endTime;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "上架状态 0已下架/1待上架/2已上架")
  private Integer status;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
