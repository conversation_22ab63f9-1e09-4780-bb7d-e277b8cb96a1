package com.partner.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date: 2025-06-25 15:15:02
 */

@Data
@TableName("m_agent_contract")
@EqualsAndHashCode(callSuper = true)
@Schema(description = " m_agent_contract ")
public class MAgentContractEntity extends Model<MAgentContractEntity> {

    @Schema(description = "学生代理合同Id")
    private Long id;


    @Schema(description = "学生代理Id")
    private Long fkAgentId;


    @Schema(description = "学生代理合同类型Id")
    private Long fkAgentContractTypeId;


    @Schema(description = "关联撤销合同Id")
    private Long fkAgentContractIdRevoke;


    @Schema(description = "合同审批模式：定义下拉枚举：0普通/1特殊")
    private Integer contractApprovalMode;


    @Schema(description = "合同编号")
    private String contractNum;


    @Schema(description = "合同开始时间")
    private LocalDateTime startTime;


    @Schema(description = "合同结束时间")
    private LocalDateTime endTime;


    @Schema(description = "合同模板：0=MPS主合同/1=PMP主合同/2=PMP附加合同")
    private Integer contractTemplateMode;


    @Schema(description = "附加协议内容")
    private String additional;


    @Schema(description = "返佣比例备注")
    private String returnCommissionRateNote;


    @Schema(description = "合同备注")
    private String remark;


    @Schema(description = "是否激活：0否/1是")
    private Boolean isActive;


    @Schema(description = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer status;


    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;


    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;


    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;


    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;

    /**
     * <p> Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:合同审批状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中/6生效中/7已过期（6和7为逻辑生成，非数据库字段） &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @Schema(description = "合同审批状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中")
    @JsonProperty("contractApprovalStatus")
    private Integer contractApprovalStatus;

}
