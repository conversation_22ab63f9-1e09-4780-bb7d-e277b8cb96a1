package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-11-28 11:17:27
 */

@Data
@TableName("m_event_incentive")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_event_incentive ")
public class MEventIncentiveEntity extends Model<MEventIncentiveEntity>{

  @Schema(description = "奖励推广活动Id")
  private Long id;
 

  @Schema(description = "公司Id")
  private Long fkCompanyId;
 

  @Schema(description = "学校提供商Id")
  private Long fkInstitutionProviderId;
 

  @Schema(description = "活动编号")
  private String num;
 

  @Schema(description = "收到奖励时间")
  private LocalDateTime receivingRewardTime;
 

  @Schema(description = "实际宣传时间")
  private LocalDateTime actualPublicityTime;
 

  @Schema(description = "活动开始时间")
  private LocalDateTime eventStartTime;
 

  @Schema(description = "活动结束时间")
  private LocalDateTime eventEndTime;
 

  @Schema(description = "活动标题")
  private String eventTitle;
 

  @Schema(description = "符合奖励intake")
  private String accordWithIncentiveIntake;
 

  @Schema(description = "激励政策")
  private String incentivePolicy;
 

  @Schema(description = "建议核对时间")
  private LocalDateTime suggestCheckTime;
 

  @Schema(description = "预计完成学生数")
  private Integer expectTargetCount;
 

  @Schema(description = "实际完成学生数")
  private Integer actualTargetCount;
 

  @Schema(description = "币种编号")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "实际支付奖励金额")
  private BigDecimal actualPayAmount;
 

  @Schema(description = "是否下发奖励：0否/1是")
  private Boolean isDistributed;
 

  @Schema(description = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
  private String publicLevel;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "状态：0计划/1结束/2取消/3延期")
  private Integer status;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
