package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-07 19:14:39
 */

@Data
@TableName("m_institution_course")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_institution_course ")
public class MInstitutionCourseEntity extends Model<MInstitutionCourseEntity>{

  @Schema(description = "课程Id")
  private Long id;
 

  @Schema(description = "学校Id")
  private Long fkInstitutionId;
 

  @Schema(description = "课程code（校方编号）")
  private String code;
 

  @Schema(description = "课程编号")
  private String num;
 

  @Schema(description = "课程名称")
  private String name;
 

  @Schema(description = "课程中文名称")
  private String nameChn;
 

  @Schema(description = "显示名称")
  private String nameDisplay;
 

  @Schema(description = "开始月份")
  private String startMonth;
 

  @Schema(description = "开学时间描述")
  private String startDateNote;
 

  @Schema(description = "申请月份，多月逗号分隔：9")
  private String applyMonth;
 

  @Schema(description = "申请时间描述")
  private String applyDateNote;
 

  @Schema(description = "课程学费")
  private BigDecimal fee;
 

  @Schema(description = "课程学费（最高）")
  private BigDecimal feeMax;
 

  @Schema(description = "课程学费说明")
  private String feeNote;
 

  @Schema(description = "课程学费（统一为人民币，主要是学费筛选使用）")
  private BigDecimal feeCny;
 

  @Schema(description = "课程总时长（年）")
  private BigDecimal durationYear;
 

  @Schema(description = "课程总时长说明")
  private String durationNote;
 

  @Schema(description = "是否直录：0否/1是")
  private Boolean isDirect;
 

  @Schema(description = "简介")
  private String introduction;
 

  @Schema(description = "简介（原文）")
  private String introductionSource;
 

  @Schema(description = "核心课程")
  private String coreCourse;
 

  @Schema(description = "录取标准")
  private String entryStandards;
 

  @Schema(description = "职业发展")
  private String occupationDevelopment;
 

  @Schema(description = "课程备注")
  private String remark;
 

  @Schema(description = "是否激活：0否/1是")
  private Boolean isActive;
 

  @Schema(description = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
  private String publicLevel;
 

  @Schema(description = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
  private Integer dataLevel;
 

  @Schema(description = "排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

  @Schema(description = "旧数据id(gea)")
  private String idGea;
 

  @Schema(description = "旧数据id(iae)")
  private String idIae;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
