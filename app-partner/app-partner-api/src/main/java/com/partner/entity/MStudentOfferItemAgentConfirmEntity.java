package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-03 09:45:07
 */

@Data
@TableName("m_student_offer_item_agent_confirm")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_student_offer_item_agent_confirm ")
public class MStudentOfferItemAgentConfirmEntity extends Model<MStudentOfferItemAgentConfirmEntity>{

  @Schema(description = "申请计划名单确认Id")
  private Long id;
 

  @Schema(description = "租户Id")
  private Long fkTenantId;
 

  @Schema(description = "学生代理Id")
  private Long fkAgentId;
 

  @Schema(description = "学生申请计划Id")
  private Long fkStudentOfferItemId;
 

  @Schema(description = "伙伴用户Id（确认人Id）")
  private Long fkPartnerUserId;
 

  @Schema(description = "是否系统自动确认：0否/1是")
  private Boolean isSystemConfirmed;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
