package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-09 11:34:59
 */

@Data
@TableName("m_agent_contract_account")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_agent_contract_account ")
public class MAgentContractAccountEntity extends Model<MAgentContractAccountEntity>{

  @Schema(description = "学生代理合同账户Id")
  private Long id;
 

  @Schema(description = "学生代理Id")
  private Long fkAgentId;
 

  @Schema(description = "币种编号")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "账户卡类型，枚举：借记卡1,存折2,信用卡3,准贷记卡4,预付卡费5,境外卡6")
  private Integer accountCardType;
 

  @Schema(description = "银行账户名称")
  private String bankAccount;
 

  @Schema(description = "银行账号")
  private String bankAccountNum;
 

  @Schema(description = "银行名称")
  private String bankName;
 

  @Schema(description = "银行支行名称")
  private String bankBranchName;
 

  @Schema(description = "银行地址国家Id")
  private Long fkAreaCountryId;
 

  @Schema(description = "银行地址州省Id")
  private Long fkAreaStateId;
 

  @Schema(description = "银行地址城市Id")
  private Long fkAreaCityId;
 

  @Schema(description = "银行地址城市区域Id")
  private Long fkAreaCityDivisionId;
 

  @Schema(description = "银行地址")
  private String bankAddress;
 

  @Schema(description = "银行编号类型：SwiftCode/BSB")
  private String bankCodeType;
 

  @Schema(description = "银行编号")
  private String bankCode;
 

  @Schema(description = "国家编码")
  private String areaCountryCode;
 

  @Schema(description = "是否默认首选：0否/1是")
  private Boolean isDefault;
 

  @Schema(description = "是否激活：0否/1是")
  private Boolean isActive;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
