package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-10 18:09:28
 */

@Data
@TableName("r_payable_plan_settlement_flag")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_payable_plan_settlement_flag ")
public class RPayablePlanSettlementFlagEntity extends Model<RPayablePlanSettlementFlagEntity>{

  @Schema(description = "关系Id")
  private Long id;
 

  @Schema(description = "财务结算汇总批次号")
  private String numSettlementBatch;
 

  @Schema(description = "应付计划类型，枚举：m_student_offer_item留学申请计划/m_student_insurance留学保险/m_student_accommodation留学住宿")
  private String fkTypeKey;
 

  @Schema(description = "学生代理Id")
  private Long fkAgentId;
 

  @Schema(description = "支付币种")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "帐号币种")
  private String fkCurrencyTypeNumAccount;
 

  @Schema(description = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
  private Integer statusSettlement;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
