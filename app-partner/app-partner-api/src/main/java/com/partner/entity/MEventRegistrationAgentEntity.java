package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-06 15:13:22
 */

@Data
@TableName("m_event_registration_agent")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_event_registration_agent ")
public class MEventRegistrationAgentEntity extends Model<MEventRegistrationAgentEntity>{

  @Schema(description = "活动汇总代理报名名册Id")
  private Long id;
 

  @Schema(description = "活动Id")
  private Long fkEventId;
 

  @Schema(description = "学生代理Id")
  private Long fkAgentId;
 

  @Schema(description = "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @Schema(description = "姓名")
  private String name;
 

  @Schema(description = "手机区号")
  private String mobileAreaCode;
 

  @Schema(description = "移动电话")
  private String mobile;
 

  @Schema(description = "参加人数")
  private Integer peopleCount;
 

  @Schema(description = "枚举状态：0待定/1参加/2不参加")
  private Integer status;
 

  @Schema(description = "备注")
  private String remark;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
