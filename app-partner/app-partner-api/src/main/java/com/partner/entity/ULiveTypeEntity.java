package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2024-11-27 11:09:52
 */

@Data
@TableName("u_live_type")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" u_live_type ")
public class ULiveTypeEntity extends Model<ULiveTypeEntity>{

  @Schema(description = "直播类型id")
  private Long id;
 

  @Schema(description = "类型名称英文(拼音)名")
  private String typeName;
 

  @Schema(description = "类型中文名称")
  private String typeNameChn;
 

  @Schema(description = "类型key,枚举类型key")
  private String typeKey;
 

  @Schema(description = "排序,倒序,数字由大小排列")
  private Integer viewOrder;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
