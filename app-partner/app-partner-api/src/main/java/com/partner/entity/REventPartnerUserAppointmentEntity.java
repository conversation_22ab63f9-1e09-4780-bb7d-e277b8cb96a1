package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-08 17:00:43
 */

@Data
@TableName("r_event_partner_user_appointment")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_event_partner_user_appointment ")
public class REventPartnerUserAppointmentEntity extends Model<REventPartnerUserAppointmentEntity>{

  @Schema(description = "热门活动报名预约Id")
  private Long id;
 

  @Schema(description = "活动汇总代理报名名册Id")
  private Long fkEventRegistrationAgentId;
 

  @Schema(description = "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @Schema(description = "状态：0未发送预约信息/1发送成功/2发送失败")
  private Integer status;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
