package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-30 10:02:09
 */

@Data
@TableName("m_help")
@EqualsAndHashCode(callSuper = true)
@Schema(description="m_help")
public class MHelpEntity extends Model<MHelpEntity>{

  @Schema(description = "帮助Id")
  private Long id;
 

  @Schema(description = "帮助父Id")
  private Long fkParentHelpId;
 

  @Schema(description = "帮助类型Id")
  private Long fkHelpTypeId;
 

  @Schema(description = "Key（前端引用时使用）")
  private String keyCode;
 

  @Schema(description = "显示类型：0直接显示/1小图层纯文本显示/2大图层富文本显示")
  private Integer showType;
 

  @Schema(description = "标题（内部备注使用）")
  private String title;
 

  @Schema(description = "描述富文本内容")
  private String description;
 

  @Schema(description = "关键信息名称，填写后会进行收集关键信息名称和用户选择的答案，如这里填写：感兴趣国家")
  private String keyInfoName;
 

  @Schema(description = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
  private String publicLevel;
 

  @Schema(description = "排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
