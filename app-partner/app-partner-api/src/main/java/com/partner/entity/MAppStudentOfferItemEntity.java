package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-30 20:59:56
 */

@Data
@TableName("m_app_student_offer_item")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_app_student_offer_item ")
public class MAppStudentOfferItemEntity extends Model<MAppStudentOfferItemEntity>{

  @Schema(description = "申请学生申请计划Id")
  private Long id;
 

  @Schema(description = "申请学生Id")
  private Long fkAppStudentId;
 

  @Schema(description = "国家Id")
  private Long fkAreaCountryId;
 

  @Schema(description = "学校Id")
  private Long fkInstitutionId;
 

  @Schema(description = "课程Id")
  private Long fkInstitutionCourseId;
 

  @Schema(description = "开学时间")
  private LocalDate openingTime;
 

  @Schema(description = "学生申请方案项目Id（审批通过后需要回填Id，建立关系）")
  private Long fkStudentOfferItemId;
 

  @Schema(description = "是否加申：0否/1是")
  private Boolean isAdditional;

  @Schema(description = "加申状态：-1作废/0未提交(草稿)/1已提交(待确认处理)/2正式生效")
  private Integer statusAdditional;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
