package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-03-17 17:19:51
 */

@Data
@TableName("r_event_incentive_partner_user_read")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_event_incentive_partner_user_read ")
public class REventIncentivePartnerUserReadEntity extends Model<REventIncentivePartnerUserReadEntity>{

  @Schema(description = "奖励推广活动阅读关系Id")
  private Long id;
 

  @Schema(description = "奖励推广活动Id")
  private Long fkEventIncentiveId;
 

  @Schema(description = "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
