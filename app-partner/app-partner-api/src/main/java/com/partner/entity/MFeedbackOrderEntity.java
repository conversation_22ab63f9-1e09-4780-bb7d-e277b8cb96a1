package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-23 10:05:29
 */

@Data
@TableName("m_feedback_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_feedback_order ")
public class MFeedbackOrderEntity extends Model<MFeedbackOrderEntity>{

  @Schema(description = "反馈工单Id")
  private Long id;
 

  @Schema(description = "公司Id")
  private Long fkCompanyId;
 

  @Schema(description = "平台应用Id")
  private Long fkPlatformId;
 

  @Schema(description = "平台应用CODE")
  private String fkPlatformCode;
 

  @Schema(description = "平台应用对应的创建用户Id，PARTNER=fk_partner_user_id，AIS=fk_staff_id")
  private Long fkPlatformCreateUserId;
 

  @Schema(description = "系统资源Key")
  private String fkResourceKey;
 

  @Schema(description = "反馈工单号")
  private String num;
 

  @Schema(description = "反馈工单类型Id")
  private Long fkFeedbackOrderTypeId;
 

  @Schema(description = "标题")
  private String title;
 

  @Schema(description = "内容")
  private String message;
 

  @Schema(description = "状态：0待处理/1已回复/2已关闭/3系统自动关闭")
  private Integer status;
 

  @Schema(description = "关闭时间")
  private LocalDateTime closeTime;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
