package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-14 20:58:46
 */

@Data
@TableName("r_partner_user_superior")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_partner_user_superior ")
public class RPartnerUserSuperiorEntity extends Model<RPartnerUserSuperiorEntity>{

  @Schema(description = "伙伴用户上下级关系Id")
  private Long id;
 

  @Schema(description = "租户Id")
  private Long fkTenantId;
 

  @Schema(description = "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @Schema(description = "伙伴用户业务上司Id")
  private Long fkPartnerUserIdSuperior;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
