package com.partner.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName m_receivable_plan
 */
@TableName(value ="m_receivable_plan")
public class MReceivablePlanEntity  extends Model<MPayablePlanEntity> {
    /**
     * 应收计划Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 公司Id
     */
    private Long fkCompanyId;

    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
    private String fkTypeKey;

    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    private Long fkTypeTargetId;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 币种编号
     */
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    private BigDecimal tuitionAmount;

    /**
     * 费率%
     */
    private BigDecimal commissionRate;

    /**
     * 渠道费率%
     */
    private BigDecimal netRate;

    /**
     * 佣金金额
     */
    private BigDecimal commissionAmount;

    /**
     * 定额金额
     */
    private BigDecimal fixedAmount;

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    private Integer bonusType;

    /**
     * 其他金额
     */
    private BigDecimal bonusAmount;

    /**
     * 应收金额
     */
    private BigDecimal receivableAmount;

    /**
     * 计划收款时间
     */
    private Date receivablePlanDate;

    /**
     * 应收计划额外原因Id
     */
    private Integer fkReceivableReasonId;

    /**
     * 状态：0关闭/1打开/2完成
     */
    private Integer status;

    /**
     * 旧数据财务id(gea)
     */
    private String idGeaFinance;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 修改用户(登录账号)
     */
    private String gmtModifiedUser;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 应收计划Id
     */
    public Long getId() {
        return id;
    }

    /**
     * 应收计划Id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 公司Id
     */
    public Long getFkCompanyId() {
        return fkCompanyId;
    }

    /**
     * 公司Id
     */
    public void setFkCompanyId(Long fkCompanyId) {
        this.fkCompanyId = fkCompanyId;
    }

    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
    public String getFkTypeKey() {
        return fkTypeKey;
    }

    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
    public void setFkTypeKey(String fkTypeKey) {
        this.fkTypeKey = fkTypeKey;
    }

    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    public Long getFkTypeTargetId() {
        return fkTypeTargetId;
    }

    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    public void setFkTypeTargetId(Long fkTypeTargetId) {
        this.fkTypeTargetId = fkTypeTargetId;
    }

    /**
     * 摘要
     */
    public String getSummary() {
        return summary;
    }

    /**
     * 摘要
     */
    public void setSummary(String summary) {
        this.summary = summary;
    }

    /**
     * 币种编号
     */
    public String getFkCurrencyTypeNum() {
        return fkCurrencyTypeNum;
    }

    /**
     * 币种编号
     */
    public void setFkCurrencyTypeNum(String fkCurrencyTypeNum) {
        this.fkCurrencyTypeNum = fkCurrencyTypeNum;
    }

    /**
     * 学费金额
     */
    public BigDecimal getTuitionAmount() {
        return tuitionAmount;
    }

    /**
     * 学费金额
     */
    public void setTuitionAmount(BigDecimal tuitionAmount) {
        this.tuitionAmount = tuitionAmount;
    }

    /**
     * 费率%
     */
    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    /**
     * 费率%
     */
    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    /**
     * 渠道费率%
     */
    public BigDecimal getNetRate() {
        return netRate;
    }

    /**
     * 渠道费率%
     */
    public void setNetRate(BigDecimal netRate) {
        this.netRate = netRate;
    }

    /**
     * 佣金金额
     */
    public BigDecimal getCommissionAmount() {
        return commissionAmount;
    }

    /**
     * 佣金金额
     */
    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    /**
     * 定额金额
     */
    public BigDecimal getFixedAmount() {
        return fixedAmount;
    }

    /**
     * 定额金额
     */
    public void setFixedAmount(BigDecimal fixedAmount) {
        this.fixedAmount = fixedAmount;
    }

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    public Integer getBonusType() {
        return bonusType;
    }

    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    public void setBonusType(Integer bonusType) {
        this.bonusType = bonusType;
    }

    /**
     * 其他金额
     */
    public BigDecimal getBonusAmount() {
        return bonusAmount;
    }

    /**
     * 其他金额
     */
    public void setBonusAmount(BigDecimal bonusAmount) {
        this.bonusAmount = bonusAmount;
    }

    /**
     * 应收金额
     */
    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    /**
     * 应收金额
     */
    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    /**
     * 计划收款时间
     */
    public Date getReceivablePlanDate() {
        return receivablePlanDate;
    }

    /**
     * 计划收款时间
     */
    public void setReceivablePlanDate(Date receivablePlanDate) {
        this.receivablePlanDate = receivablePlanDate;
    }

    /**
     * 应收计划额外原因Id
     */
    public Integer getFkReceivableReasonId() {
        return fkReceivableReasonId;
    }

    /**
     * 应收计划额外原因Id
     */
    public void setFkReceivableReasonId(Integer fkReceivableReasonId) {
        this.fkReceivableReasonId = fkReceivableReasonId;
    }

    /**
     * 状态：0关闭/1打开/2完成
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态：0关闭/1打开/2完成
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 旧数据财务id(gea)
     */
    public String getIdGeaFinance() {
        return idGeaFinance;
    }

    /**
     * 旧数据财务id(gea)
     */
    public void setIdGeaFinance(String idGeaFinance) {
        this.idGeaFinance = idGeaFinance;
    }

    /**
     * 创建时间
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * 创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * 创建用户(登录账号)
     */
    public String getGmtCreateUser() {
        return gmtCreateUser;
    }

    /**
     * 创建用户(登录账号)
     */
    public void setGmtCreateUser(String gmtCreateUser) {
        this.gmtCreateUser = gmtCreateUser;
    }

    /**
     * 修改时间
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * 修改时间
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * 修改用户(登录账号)
     */
    public String getGmtModifiedUser() {
        return gmtModifiedUser;
    }

    /**
     * 修改用户(登录账号)
     */
    public void setGmtModifiedUser(String gmtModifiedUser) {
        this.gmtModifiedUser = gmtModifiedUser;
    }


}