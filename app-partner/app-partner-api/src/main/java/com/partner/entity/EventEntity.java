package com.partner.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("m_event")
@Schema(description = "活动")
public class EventEntity extends Model<EventEntity> {
    private static final long serialVersionUID = 1L;

    /**
     * 新闻Id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="活动Id")
    private Long id;
    /**
     * 公司Id
     */
    @Schema(description = "公司Id")
    private Long fkCompanyId;
    /**
     * 活动类型Id
     */
    @Schema(description = "活动类型Id")
    private Long fkEventTypeId;
    /**
     * 活动编号
     */
    @Schema(description = "活动编号")
    private String num;
    /**
     * 活动时间
     */
    @Schema(description = "活动时间")
    private LocalDateTime eventTime;
    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    private LocalDateTime eventTimeEnd;
    /**
     * 活动主题
     */
    @Schema(description = "活动主题")
    private String eventTheme;
    /**
     * 活动目标对象
     */
    @Schema(description = "活动目标对象")
    private String eventTarget;
    /**
     * 活动目标对象负责人
     */
    @Schema(description = "活动目标对象负责人")
    private String eventTargetLeader;
    /**
     * 活动举办国家Id
     */
    @Schema(description = "活动举办国家Id")
    private Long fkAreaCountryIdHold;
    /**
     * 活动举办州省Id
     */
    @Schema(description = "活动举办州省Id")
    private Long fkAreaStateIdHold;
    /**
     * 活动举办城市Id
     */
    @Schema(description = "活动举办城市Id")
    private Long fkAreaCityIdHold;
    /**
     * 员工Id（负责人1）
     */
    @Schema(description = "员工Id（负责人1）")
    private Long fkStaffIdLeader1;
    /**
     * 员工Id（负责人2）
     */
    @Schema(description = "员工Id（负责人2）")
    private Long fkStaffIdLeader2;
    /**
     * 币种编号
     */
    @Schema(description = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * BD预算场地费用
     */
    @Schema(description = "BD预算场地费用")
    private BigDecimal bdVenueAmount;

    /**
     * BD预算餐饮费用
     */
    @Schema(description = "BD预算餐饮费用")
    private BigDecimal bdFoodAmount;

    /**
     * BD预算奖品费用
     */
    @Schema(description = "BD预算奖品费用")
    private BigDecimal bdPrizeAmount;

    /**
     * BD预算其他费用
     */
    @Schema(description = "BD预算其他费用")
    private BigDecimal bdOtherAmount;

    /**
     * 预算金额
     */
    @Schema(description = "预算金额")
    private BigDecimal budgetAmount;
    /**
     * 实际金额
     */
    @Schema(description = "实际金额")
    private BigDecimal actualAmount;
    /**
     * 预算人数
     */
    @Schema(description = "预算人数")
    private Integer budgetCount;
    /**
     * 参加人数
     */
    @Schema(description = "参加人数")
    private Integer attendedCount;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
    /**
     * 状态：0计划/1结束/2取消
     */
    @Schema(description = "状态：0计划/1结束/2取消")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    @Schema(description="修改时间")
    private LocalDateTime gmtModified;

    /**
     * 修改用户(登录账号)
     */
    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}