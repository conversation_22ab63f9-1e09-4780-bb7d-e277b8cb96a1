package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-08 16:58:47
 */

@Data
@TableName("log_event_partner_user_appointment_sender")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" log_event_partner_user_appointment_sender ")
public class LogEventPartnerUserAppointmentSenderEntity extends Model<LogEventPartnerUserAppointmentSenderEntity>{

  @Schema(description = "热门活动报名发送预约信息日志Id")
  private Long id;
 

  @Schema(description = "活动汇总代理报名名册Id")
  private Long fkEventRegistrationAgentId;
 

  @Schema(description = "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @Schema(description = "状态：1发送成功/2发送失败")
  private Integer status;
 

  @Schema(description = "发送日志备注")
  private String remark;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
