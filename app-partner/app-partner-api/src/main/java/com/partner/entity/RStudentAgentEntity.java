package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-02-14 09:48:45
 */

@Data
@TableName("r_student_agent")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" r_student_agent ")
public class RStudentAgentEntity extends Model<RStudentAgentEntity>{

  @Schema(description = "关系Id")
  private Long id;
 

  @Schema(description = "学生Id")
  private Long fkStudentId;
 

  @Schema(description = "代理Id")
  private Long fkAgentId;
 

  @Schema(description = "是否激活：0否/1是")
  private Boolean isActive;
 

  @Schema(description = "绑定时间")
  private LocalDateTime activeDate;
 

  @Schema(description = "取消绑定时间（下次绑定时，需要重新建立记录）")
  private LocalDateTime unactiveDate;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
