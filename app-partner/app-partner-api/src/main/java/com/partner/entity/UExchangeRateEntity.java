package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-20 14:07:20
 */

@Data
@TableName("u_exchange_rate")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" u_exchange_rate ")
public class UExchangeRateEntity extends Model<UExchangeRateEntity>{

  @Schema(description = "日汇率Id")
  private Long id;
 

  @Schema(description = "参照币种编号")
  private String fkCurrencyTypeNumFrom;
 

  @Schema(description = "目标币种编号")
  private String fkCurrencyTypeNumTo;
 

  @Schema(description = "获取日期")
  private LocalDate getDate;
 

  @Schema(description = "汇率")
  private BigDecimal exchangeRate;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
