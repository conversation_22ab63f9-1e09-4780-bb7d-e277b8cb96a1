package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-08 14:04:29
 */

@Data
@TableName("m_institution")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_institution ")
public class MInstitutionEntity extends Model<MInstitutionEntity>{

  @Schema(description = "学校Id")
  private Long id;
 

  @Schema(description = "学校类型Id")
  private Long fkInstitutionTypeId;
 

  @Schema(description = "国家Id")
  private Long fkAreaCountryId;
 

  @Schema(description = "州省Id")
  private Long fkAreaStateId;
 

  @Schema(description = "城市Id")
  private Long fkAreaCityId;
 

  @Schema(description = "币种编号")
  private String fkCurrencyTypeNum;
 

  @Schema(description = "学校编号")
  private String num;
 

  @Schema(description = "学校名称")
  private String name;
 

  @Schema(description = "学校中文名称")
  private String nameChn;
 

  @Schema(description = "显示名称")
  private String nameDisplay;
 

  @Schema(description = "学校简称")
  private String shortName;
 

  @Schema(description = "学校中文简称")
  private String shortNameChn;
 

  @Schema(description = "学校性质：公立/私立")
  private String nature;
 

  @Schema(description = "成立时间")
  private String establishedDate;
 

  @Schema(description = "入学申请时间")
  private String applyDate;
 

  @Schema(description = "学费下限")
  private BigDecimal applyFeeMin;
 

  @Schema(description = "学费上限")
  private BigDecimal applyFeeMax;
 

  @Schema(description = "学费参考（显示）")
  private BigDecimal applyFeeRef;
 

  @Schema(description = "学费（统一为人民币，主要是学费筛选使用）")
  private BigDecimal applyFeeCny;
 

  @Schema(description = "官网")
  private String website;
 

  @Schema(description = "邮编")
  private String zipCode;
 

  @Schema(description = "地址")
  private String address;
 

  @Schema(description = "详细描述")
  private String detail;
 

  @Schema(description = "地图坐标（google）")
  private String mapXyGg;
 

  @Schema(description = "地图坐标（baidu）")
  private String mapXyBd;
 

  @Schema(description = "排名类型：985/211，枚举")
  private String rankingType;
 

  @Schema(description = "是否含非英语课程：0否/1是")
  private Boolean isIncludingNonEnglish;
 

  @Schema(description = "重点推荐的学校：0否/1是")
  private Boolean isKpi;
 

  @Schema(description = "重点推荐的学校等级：1小推荐/2中推荐/3大推荐")
  private Integer kpiLevel;
 

  @Schema(description = "是否激活：0否/1是")
  private Boolean isActive;
 

  @Schema(description = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
  private String publicLevel;
 

  @Schema(description = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
  private Integer dataLevel;
 

  @Schema(description = "旧数据id(gea)")
  private String idGea;
 

  @Schema(description = "旧数据id(iae)")
  private String idIae;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
