package com.partner.entity;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-23 17:18:21
 */

@Data
@TableName("m_company")
@EqualsAndHashCode(callSuper = true)
@Schema(description=" m_company ")
public class MCompanyEntity extends Model<MCompanyEntity>{

  @Schema(description = "公司Id")
  private Long id;
 

  @Schema(description = "父公司Id")
  private Long fkParentCompanyId;
 

  @Schema(description = "公司编号")
  private String num;
 

  @Schema(description = "公司名称")
  private String name;
 

  @Schema(description = "公司中文名称")
  private String nameChn;
 

  @Schema(description = "公司简称")
  private String shortName;
 

  @Schema(description = "公司中文简称")
  private String shortNameChn;
 

  @Schema(description = "排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

  @Schema(description = "创建时间")
  private LocalDateTime gmtCreate;
 

  @Schema(description = "创建用户(登录账号)")
  private String gmtCreateUser;
 

  @Schema(description = "修改时间")
  private LocalDateTime gmtModified;
 

  @Schema(description = "修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
