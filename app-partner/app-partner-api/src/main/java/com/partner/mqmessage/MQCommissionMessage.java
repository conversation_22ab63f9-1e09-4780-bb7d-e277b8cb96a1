package com.partner.mqmessage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MQCommissionMessage {

    private Long agentId;
    private Long fkCompanyId;

    private Long studentId;

    @Schema(description="用户编号，U00100000001，3位平台Id8位用户Id")
    private String num;
    @Schema(description=" 姓名（中文）")
    private String name;

    @Schema(description="邮箱地址")
    private String email;




}
