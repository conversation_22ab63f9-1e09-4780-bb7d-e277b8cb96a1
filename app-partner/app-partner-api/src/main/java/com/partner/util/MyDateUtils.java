package com.partner.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

public class MyDateUtils {
    public static  String DATE_FORMAT = "yyyy-MM-dd";

    public static String formatDate(LocalDate date) {

        if(date == null) {
            return "";
        }
        String resultValue=date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        return resultValue;
    }
    public static String formatDate(LocalDateTime date) {

        if(date == null) {
            return "";
        }
        String resultValue=date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));


        return resultValue;
    }
    public static String formatDate(LocalDateTime date,String format) {

        if(date == null) {
            return "";
        }
        String resultValue=date.format(DateTimeFormatter.ofPattern(format));


        return resultValue;
    }

    /**
     * date2比date1多的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);


        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);


        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) // 同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) // 闰年
                {
                    timeDistance += 366;
                } else // 不是闰年
                {
                    timeDistance += 365;
                }
            }


            return timeDistance + (day2 - day1);
        } else // 不同年
        {
            return day2 - day1;
        }
    }

    /**
     * 获取日期的年份
     *
     * @param date
     * @return 日期的年份
     */
    public static int getYear(Date date) {
        return getCalendar(date).get(Calendar.YEAR);
    }

    public static int getMonth(Date date) {
        return getCalendar(date).get(Calendar.MONTH)+1;
    }

    /**
     * 获取day对应的Calendar对象
     *
     * @param day
     * @return 返回date对应的Calendar对象
     */
    public static Calendar getCalendar(Date day) {
        Calendar c = Calendar.getInstance();
        c.clear();
        if (day != null) {
            c.setTime(day);
        }
        return c;
    }

    /**
     * 下个月
     *
     * @return 下个月
     * @since 3.0.1
     */
    public static DateTime nextMonth() {
        return offsetMonth(new DateTime(), 1);
    }
    /**
     * 偏移月
     *
     * @param date   日期
     * @param offset 偏移月数，正数向未来偏移，负数向历史偏移
     * @return 偏移后的日期
     */
    public static DateTime offsetMonth(Date date, int offset) {
        return offset(date, DateField.MONTH, offset);
    }
    /**
     * 获取指定日期偏移指定时间后的时间，生成的偏移日期不影响原日期
     *
     * @param date      基准日期
     * @param dateField 偏移的粒度大小（小时、天、月等）{@link DateField}
     * @param offset    偏移量，正数为向后偏移，负数为向前偏移
     * @return 偏移后的日期
     */
    public static DateTime offset(Date date, DateField dateField, int offset) {
        return dateNew(date).offset(dateField, offset);
    }
    /**
     * 根据已有{@link Date} 产生新的{@link DateTime}对象
     *
     * @param date Date对象
     * @return {@link DateTime}对象
     * @since 4.3.1
     */
    public static DateTime dateNew(Date date) {
        return new DateTime(date);
    }

}
