package com.partner.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;

public class GetStringUtils {

    /**
     * 学生编号
     *
     * @param num
     * @return
     */
    public static String getStudentNum(Long num) {
        String code = String.valueOf(num);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy");
        String year = formatter.format(calendar.getTime());

        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "ST" + year + code;
    }


    /**
     * 工单编号
     *
     * @param num
     * @return
     */
    public static String getWorkOrderNum(Long num) {
        String code = String.valueOf(num);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String yearMonthDay = formatter.format(calendar.getTime());

        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "W" + yearMonthDay + code;
    }
}
