package com.partner.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "直播日历参数")
public class MLivecalendarParamsDto {
    @Schema(description = "直播日历年份")
    @NotNull(message = "年份不能为空")
    private Integer year;

    @Schema(description = "直播日历月份")
    @NotNull(message = "月份不能为空")
    private Integer month;


    @Schema(description = "后端使用(前端不需要处理) 直播列表 0最新直播/1往期直播")
    private Integer liveType;

    @Schema(description = "后端使用(前端不需要处理)")
    private String mMageAddress;

}
