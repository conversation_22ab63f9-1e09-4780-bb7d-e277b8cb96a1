package com.partner.dto.work;

import com.partner.dto.base.UserInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "反馈单条件")
public class MFeedbackOrderDto extends UserInfoParams {

    @Schema(description = "状态：0最近三天/1最近一周/2最近一个月/3最近三个月/4最近半年/5最近一年")
    private Integer orderTimeType;

    @Schema(description = "状态：0待处理/1已回复/2已关闭/3系统自动关闭")
    private Integer status;

}
