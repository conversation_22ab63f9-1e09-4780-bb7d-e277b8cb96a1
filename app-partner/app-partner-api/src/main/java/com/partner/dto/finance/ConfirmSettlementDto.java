package com.partner.dto.finance;

import com.partner.entity.MFilePartnerEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "提交结算申请(包含代理账户信息)")
public class ConfirmSettlementDto {
    /*@NotBlank(message = "代理UUID不能为空")
    @Schema(description = "代理UUID")
    private String agentUUID;*/

    /*@NotBlank(message = "结算总金额")
    @Schema(description = "结算总金额")
    private BigDecimal totalAmount;*/

    @NotNull(message = "结算目标账户ID")
    @Schema(description = "结算目标账户ID")
    private Long targetAgentContractAccountId;

    @NotBlank(message = "结算目标账户币种")
    @Schema(description = "结算目标账户币种")
    private String targetAgentContractAccountNum;


    @Schema(description = "提交结算信息")
    @Valid
    @NotNull(message = "提交结算不能为空")
    List<ConfirmSettlementInfo> confirmSettlementInfoList;

    //@Valid
    //@NotNull(message = "签字文件信息不能为空")
    @Schema(description = "签字文件信息")
    private MFileSettlementInfo fileEntity;


    @Schema(description = "签名文件")
    @NotBlank(message = "签字文件信息不能为空")
    private String signature;




}
