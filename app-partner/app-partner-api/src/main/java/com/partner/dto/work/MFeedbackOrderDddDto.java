package com.partner.dto.work;


import com.partner.entity.MFeedbackOrderEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "反馈单-新增")
public class MFeedbackOrderDddDto  extends MFeedbackOrderEntity {


    @Schema(description = "反馈工单类型Id")
    @NotNull(message = "反馈工单类型Id为空")
    private Long fkFeedbackOrderTypeId;


    @Schema(description = "标题")
    @NotBlank(message = "标题为空")
    private String title;


    @Schema(description = "内容")
    @NotBlank(message = "内容为空")
    private String message;

    @Schema(description = "多附件")
    private String[] fileGuidArray;





}
