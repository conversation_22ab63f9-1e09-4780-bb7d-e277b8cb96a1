package com.partner.dto.attach;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/7/11
 * @Version 1.0
 * @apiNote:附件信息保存请求参数-ais
 */
@Data
public class UploadMediaAndAttachedRequestDto {

    @Schema(description = "文件对应的微服务名称, 如：销售中心,华通伙伴中心")
    private String serviceName;

    @Schema(description = "表名")
    private String fkTableName;

    @Schema(description = "表Id")
    private Long fkTableId;

    @Schema(description = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;

    @Schema(description = "创建人")
    private String gmtCreateUser;

    @Schema(description = "文件guid")
    private String fileGuid;
}
