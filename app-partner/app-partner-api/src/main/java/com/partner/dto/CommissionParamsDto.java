package com.partner.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "院校佣金参数")
public class CommissionParamsDto {
    @Schema(description = "年度")
    private int year;
    @Schema(description = "公司ID")
    private Long companyId;


    @Schema(description = "国家ID")
    private Long areAcountryId;
    @Schema(description = "院校ID")
    private Long institutionId;
    @Schema(description = "院校名称")
    private String institutionName;



    @Schema(description = "学校类型")
    private Long institutionTypeId;
    @Schema(description = "所属集团")
    private Long institutionGroupId;
    @Schema(description = "课程等级")
    private Long majorLevelId;
    @Schema(description = "适用国籍")
    private Long applyCountryId;




    @Schema(description = "高佣查询:0首页/1查看全部")
    private int type;

    private String mMageAddress;

}
