package com.partner.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "奖励政策")
public class MEventIncentiveParamsDto {

    @Schema(description = "国家ID")
    private Long areaCountryId;
    @Schema(description = "标题")
    private String eventIncentiveTitle;
    @Schema(description="学校提供商名称")
    private String institutionProviderName;


    @Schema(description = "公司ID")
    private Long companyId;
    @Schema(description = "图片桶域名")
    private String mMageAddress;


}
