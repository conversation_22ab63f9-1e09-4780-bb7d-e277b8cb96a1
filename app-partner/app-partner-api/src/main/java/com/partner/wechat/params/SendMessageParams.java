package com.partner.wechat.params;

import com.partner.wechat.Template.BaseTemplate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SendMessageParams {
    @Schema(description = "接口调用凭证")
    private String access_token;

    @Schema(description = "订阅模板id")
    private String template_id;

    @Schema(description = "跳转页面")
    private String page;

    @Schema(description = "接收者（用户）的 openid")
    private String touser;

    @Schema(description = "模板内容")
    private BaseTemplate data;

}
