package com.partner.wechat.Template;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "热门活动公共模板参数")
public class MeventMessageTemplate  extends  BaseTemplate{
    @Schema(description = "活动名称")
    BaseValue thing1;

    @Schema(description = "开始时间")
    BaseValue time2;

    @Schema(description = "活动")
    BaseValue thing3;
    @Schema(description = "活动")
    BaseValue thing4;


}
