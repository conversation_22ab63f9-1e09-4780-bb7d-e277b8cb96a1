package com.partner.eunms;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 代理合同审批状态枚举
 *
 * <AUTHOR>
 * @Date 2025-01-08
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AgentContractApprovalStatusEnum {

    /**
     * 无合同
     */
    NO_CONTRACT(0, "无合同"),

    /**
     * 有合同
     */
    HAS_CONTRACT(1, "有合同"),

    /**
     * 未签署
     */
    UNSIGNED(2, "未签署"),

    /**
     * 待审核
     */
    PENDING_APPROVAL(3, "待审核"),

    /**
     * 审核通过
     */
    APPROVED(4, "审核通过"),

    /**
     * 审核驳回
     */
    REJECTED(-4, "审核驳回"),

    /**
     * 续约中
     */
    RENEWING(5, "续约中"),

    /**
     * 生效中
     */
    EFFECTIVE(6, "生效中"),

    /**
     * 已过期
     */
    EXPIRED(7, "已过期");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String msg;

    /**
     * 代理合同审批状态映射表
     */
    private static final Map<Integer, AgentContractApprovalStatusEnum> AGENT_CONTRACT_APPROVAL_STATUS_MAP = new HashMap<>();

    static {
        for (AgentContractApprovalStatusEnum agentContractApprovalStatusEnum : AgentContractApprovalStatusEnum
                .values()) {
            AGENT_CONTRACT_APPROVAL_STATUS_MAP.put(agentContractApprovalStatusEnum.getCode(),
                    agentContractApprovalStatusEnum);
        }
    }

    /**
     * 根据状态码获取对应的代理合同审批状态枚举实例
     *
     * @param code 状态码
     * @return 对应的代理合同审批状态枚举实例，如果找不到则返回null
     */
    public static AgentContractApprovalStatusEnum getAgentContractApprovalStatusByCode(Integer code) {
        return AGENT_CONTRACT_APPROVAL_STATUS_MAP.get(code);
    }

}