package com.partner.eunms;

import com.partner.entity.PartnerRole;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 角色code枚举
 */

@Getter
@AllArgsConstructor
public enum PartnerDefaultCodeEnum {

    ADMIN("ADMIN", "管理员", "admin"),
    CHIEF("CHIEF", "企业负责人", "chief"),
    DIRECTOR("DIRECTOR", "总监", "director"),
    MANAGER("DIRECTOR", "经理", "manager"),
    FINANCE("FINANCE", "佣金结算负责人", "Commission Settlement Manager"),
    COUNSELOR("COUNSELOR", "顾问", "consultant"),
    DOCUMENT("DOCUMENT", "文案专员", "Copywriting Specialist"),
    ;
    private String code;
    private String msg;
    private String enMsg;

    public static PartnerDefaultCodeEnum getEnumByCode(String code) {
        for (PartnerDefaultCodeEnum value : PartnerDefaultCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


    public static List<PartnerRole> buildDefaultPartnerRoles(Long fkTenantId, Long fkCompanyId, Long fkAgentId, String loginId) {
        Date now = new Date();
        return Arrays.stream(PartnerDefaultCodeEnum.values())
                .map(enumVal -> {
                    PartnerRole role = new PartnerRole();
                    role.setFkTenantId(fkTenantId);
                    role.setFkCompanyId(fkCompanyId);
                    role.setFkAgentId(fkAgentId);
                    role.setRoleCode(enumVal.getCode());
                    role.setRoleName(enumVal.getMsg());
                    role.setRoleNameEn(enumVal.getEnMsg());
                    role.setRoleDesc(enumVal.getMsg());
                    role.setIsActive(1); // 默认激活
                    role.setGmtCreate(now);
                    role.setGmtCreateUser(loginId);
                    role.setGmtModified(now);
                    role.setGmtModifiedUser(loginId);
                    return role;
                })
                .collect(Collectors.toList());
    }

}
