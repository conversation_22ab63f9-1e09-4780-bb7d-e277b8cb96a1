package com.partner.eunms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 角色code枚举
 */

@Getter
@AllArgsConstructor
public enum FileUploadEnum {

    SALE_CENTER_CONTRACT_ATTACH("SALE_CENTER_CONTRACT_ATTACH", "销售中心-代理合同附件", "AIS", "ais_file_center.m_file_sale", "ais_sale_center.s_media_and_attached", Boolean.TRUE, Boolean.FALSE),
    SALE_CENTER_STUDENT_DRAFT_ATTACH("SALE_CENTER_STUDENT_DRAFT_ATTACH", "销售中心-学生草稿附件", "AIS", "ais_file_center.m_file_sale", "ais_sale_center.s_media_and_attached", Boolean.TRUE, Boolean.FALSE),
    PLATFORM_CENTER_FEEDBACK_ATTACH("PLATFORM_CENTER_FEEDBACK_ATTACH", "平台中心-反馈工单附件", "AIS", "ais_file_center.m_file_platform", "ais_platform_center.s_media_and_attached", Boolean.TRUE, Boolean.FALSE),
    PARTNER_USER_PROFILE("PARTNER_USER_PROFILE", "用户头像", "APP", "app_file_center.m_file_partner", "", Boolean.FALSE, Boolean.FALSE),
    APP_PARTNER_CENTER_SETTLEMENT_ATTACH("APP_PARTNER_CENTER_SETTLEMENT_ATTACH", "APP华通伙伴-对账单信息", "APP", "app_file_center.m_file_partner", "app_partner_center.s_media_and_attached", Boolean.TRUE, Boolean.FALSE),
    ;
    // 文件类型枚举
    private String code;
    // 文件类型描述
    private String msg;
    // 文件所属平台
    private String platform;
    // 文件中心表名-库名+表名
    private String fileCenter;
    // 媒体附件表名-库名+表名,无值表示不用保存到媒体库
    private String mediaTable;
    // 是否私有-决定上传到公有桶还是私有桶
    private Boolean isPrivate;
    // 是否文件库和媒体库一并保存-true：同时保存文件库和媒体库 false：只保存文件库，媒体库由业务逻辑保存
    private Boolean uploadTogether;

    public static FileUploadEnum getEnumByCode(String code) {
        for (FileUploadEnum value : FileUploadEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
