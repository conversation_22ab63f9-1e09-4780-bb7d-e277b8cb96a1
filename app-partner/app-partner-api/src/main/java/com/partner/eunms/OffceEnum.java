package com.partner.eunms;

public enum OffceEnum {

    STEP_NEW_APP(1l, "新申请"),
    STEP_SUBMITTED(2l, "提交完成"),
    STEP_ADMITTED(4l, "已录取"),
    STEP_DEPOSIT_PAID(5l, "已付押金"),
    STEP_TUITION_PAID(17l, "已付学费"),
    STEP_OFFER_SELECTION(6l, "收到签证函"),
    STEP_ENROLLED(8l, "入学登记完成"),
    STEP_FAILURE(9l, "入学失败");


    public long key;
    public String value;

    OffceEnum(long key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getMessage(long key) {
        OffceEnum[] typeEnums = values();
        for (OffceEnum typeEnum : typeEnums) {
            if (typeEnum.key == key) {
                return typeEnum.value;
            }
        }
        return "";
    }
}
