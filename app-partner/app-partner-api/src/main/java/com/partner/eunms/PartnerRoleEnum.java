package com.partner.eunms;

import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 华通伙伴角色枚举
 *
 * <AUTHOR>
 * @Date 2025/6/16 下午8:15
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum PartnerRoleEnum {

    ADMIN(2L, "管理员"),

    COUNSELOR(4L, "顾问"),

    FINANCE(6L, "佣金结算负责人"),

    COPYWRINTING(16L, "文案专员"),

    DIRECTOR(18L, "企业负责人");

    private final Long code;

    private final String msg;

    private static final Map<Long, PartnerRoleEnum> PARTNER_ROLE_ENUM = new HashMap<>();

    static {
        for (PartnerRoleEnum partnerRoleEnum : PartnerRoleEnum.values()) {
            PARTNER_ROLE_ENUM.put(partnerRoleEnum.getCode(), partnerRoleEnum);
        }
    }

    public static PartnerRoleEnum getPartnerRoleEnum(Long code) {
        return PARTNER_ROLE_ENUM.get(code);
    }

    public static boolean isCounselorOrCopywriter(Long roleId) {
        if (ObjectUtil.isNull(roleId)) {
            return false;
        }
        return COUNSELOR.code.equals(roleId) || COPYWRINTING.code.equals(roleId);
    }
}
