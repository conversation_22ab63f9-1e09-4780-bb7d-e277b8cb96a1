package com.partner.eunms;

public enum PartnerErrorEnum {

    CHECK_EXCEPTION(10086, "校验通知信息"),
    FILEEMPTY_EXCEPTION(60001, "文件为空"),
    FILETYPE_EXCEPTION(60002, "文件类型不允许上传"),
    REGISTER_USER_ERROR(50001, "注册用户失败"),
    GET_CACHE_USER_ERROR(50002, "获取用户缓存信息失败"),
    FEIGN_SERVICE_ERROR(500, "服务调用失败"),
    USER_INFO_ERROR(50003, "用户信息有误"),
    RESET_USER_PASSWORD_ERROR(50004, "重置密码失败")
    ;

    public int errorCode;
    public String errorMessage;

    PartnerErrorEnum(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }


}
