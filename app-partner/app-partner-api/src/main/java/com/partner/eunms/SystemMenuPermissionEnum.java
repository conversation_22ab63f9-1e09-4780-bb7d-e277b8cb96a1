package com.partner.eunms;

import lombok.Getter;

/**
 * 系统菜单权限枚举
 *
 * <AUTHOR>
 */

@Getter
public enum SystemMenuPermissionEnum {

    // 一级权限：主要模块
    COMMISSION("COMMISSION", "佣金权限", "Commission Authority"),
    STUDENT("STUDENT", "学生权限", "Student Authority"),
    TEAM("TEAM", "团队管理权限", "Team Authority"),

    // 二级权限：佣金相关
    COMMISSION_VIEW("COMMISSION_VIEW", "查看权限", "Commission View Authority"),
    COMMISSION_OPERATE("COMMISSION_OPERATE", "结算申请权限", "Commission Operate Authority"),

    // 二级权限：学生相关
    STUDENT_VIEW("STUDENT_VIEW", "查看权限", "Student View Authority"),
    STUDENT_OPERATE("STUDENT_OPERATE", "操作权限", "Student Operate Authority"),

    // 三级权限：学生查看范围
    STUDENT_VIEW_PERSON("STUDENT_VIEW_PERSON", "个人", "Student View Person Authority"),
    STUDENT_VIEW_ALL("STUDENT_VIEW_ALL", "全部", "Student View All Authority"),

    // 三级权限：学生操作范围
    STUDENT_OPERATE_PERSON("STUDENT_OPERATE_PERSON", "个人", "Student Operate Person Authority"),
    STUDENT_OPERATE_ALL("STUDENT_OPERATE_ALL", "全部", "Student Operate All Authority");

    private final String code;
    private final String name;
    private final String nameEn;

    SystemMenuPermissionEnum(String code, String name, String nameEn) {
        this.code = code;
        this.name = name;
        this.nameEn = nameEn;
    }

    /**
     * 根据权限代码获取枚举
     *
     * @param code 权限代码
     * @return 对应的枚举，如果不存在返回null
     */
    public static SystemMenuPermissionEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SystemMenuPermissionEnum permission : values()) {
            if (permission.getCode().equals(code)) {
                return permission;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的权限代码
     *
     * @param code 权限代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为一级权限（主模块权限）
     *
     * @return 是否为一级权限
     */
    public boolean isPrimaryPermission() {
        return this == COMMISSION || this == STUDENT || this == TEAM;
    }

    /**
     * 判断是否为学生相关权限
     *
     * @return 是否为学生权限
     */
    public boolean isStudentPermission() {
        return code.startsWith("STUDENT");
    }

    /**
     * 判断是否为佣金相关权限
     *
     * @return 是否为佣金权限
     */
    public boolean isCommissionPermission() {
        return code.startsWith("COMMISSION");
    }

    /**
     * 判断是否为团队管理权限
     *
     * @return 是否为团队权限
     */
    public boolean isTeamPermission() {
        return code.startsWith("TEAM");
    }
}