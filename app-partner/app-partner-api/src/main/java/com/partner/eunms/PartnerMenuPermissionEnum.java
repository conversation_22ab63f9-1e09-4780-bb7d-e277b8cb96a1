package com.partner.eunms;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 合作伙伴菜单权限枚举
 */
@Getter
@AllArgsConstructor
public enum PartnerMenuPermissionEnum {

    COMMISSION("COMMISSION", "佣金权限"),
    STUDENT("STUDENT", "学生权限"),
    TEAM("TEAM", "团队管理权限"),
    COMMISSION_VIEW("COMMISSION_VIEW", "查看权限"),
    COMMISSION_OPERATE("COMMISSION_OPERATE", "结算申请权限"),
    STUDENT_OPERATE("STUDENT_OPERATE", "操作权限"),
    STUDENT_VIEW("STUDENT_VIEW", "查看权限"),
    STUDENT_VIEW_PERSON("STUDENT_VIEW_PERSON", "个人"),
    STUDENT_VIEW_ALL("STUDENT_VIEW_ALL", "全部"),
    STUDENT_OPERATE_PERSON("STUDENT_OPERATE_PERSON", "个人"),
    STUDENT_OPERATE_ALL("STUDENT_OPERATE_ALL", "全部");

    private final String code;
    private final String msg;

    /**
     * 静态Map缓存，提高查找效率从O(n)到O(1)
     */
    private static final Map<String, PartnerMenuPermissionEnum> CODE_MAP = 
            Arrays.stream(values())
                  .collect(Collectors.toMap(PartnerMenuPermissionEnum::getCode, Function.identity()));

    /**
     * 根据code获取枚举值（优化版本）
     * @param code 权限编码
     * @return 对应的枚举值，未找到时返回null
     */
    public static PartnerMenuPermissionEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    /**
     * 检查权限码是否存在
     * @param code 权限编码
     * @return true-存在，false-不存在
     */
    public static boolean containsCode(String code) {
        return code != null && CODE_MAP.containsKey(code);
    }

    /**
     * 检查是否为学生相关权限
     * @return true-是学生权限，false-不是
     */
    public boolean isStudentPermission() {
        return this.code.startsWith("STUDENT");
    }

    /**
     * 检查是否为查看类权限
     * @return true-是查看权限，false-不是
     */
    public boolean isViewPermission() {
        return this.code.contains("VIEW");
    }

    /**
     * 检查是否为操作类权限
     * @return true-是操作权限，false-不是
     */
    public boolean isOperatePermission() {
        return this.code.contains("OPERATE");
    }
}
