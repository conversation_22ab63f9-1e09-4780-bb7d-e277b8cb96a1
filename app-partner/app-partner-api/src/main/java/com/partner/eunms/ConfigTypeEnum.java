package com.partner.eunms;

public enum ConfigTypeEnum {

    U_NEWS_TYPE(100l, "华通伙伴 >> 留学资讯","U_NEWS_TYPE"),
    U_EVENT_TYPE(100l,"华通伙伴(热门活动)","U_EVENT_TYPE"),
    PARTNER_PLATFORM_ID(2l, "华通伙伴 >> partner平台ID","FK_PLATFORM_ID"),


    R_LIVE_USER_APPOINTMENT_ZSET(0l,"直播预约","R_LIVE_USER_APPOINTMENT_ZSET"),
    R_LIVE_USER_APPOINTMENT_HASH(0l,"直播预约","R_LIVE_USER_APPOINTMENT_HASH"),
    M_EVENT_INCENTIVE_HASH(0l,"奖励政策详情","M_EVENT_INCENTIVE_HASH"),
    M_APP_STUDENT_HASH(0l,"学生草稿新增标识","M_APP_STUDENT_HASH"),
    M_APP_STUDENT_DRAFT(0l,"学生草稿标识类型","M_APP_STUDENT_DRAFT"),
    M_APP_STUDENT_CHECK(0l,"学生审核中标识类型","M_APP_STUDENT_CHECK"),



    M_EXCHANGERATE_HASH(0l,"汇率存储","M_EXCHANGERATE_HASH"),


    M_COMPANY(3l,"默认公司ID,后续修改根据权限获取","M_COMPANY"),
    /*M_MAGE_ADDRESS(0l,"图片桶地址","https://hti-public-image-prd-1301376564.cos.ap-shanghai.myqcloud.com"),*/
    M_MAGE_ADDRESS(0l,"图片桶地址","https://hti-ais-images-dev-1301376564.cos.ap-shanghai.myqcloud.com"),
    SYSTEM_USER_ROLE(4l,"顾问角色ID","SYSTEM_USER_ROLE");



    public Long key;

    public String val;

    public String uNewsType;


    ConfigTypeEnum(Long key, String val, String uNewsType) {
        this.key = key;
        this.val = val;
        this.uNewsType=uNewsType;
    }


}
