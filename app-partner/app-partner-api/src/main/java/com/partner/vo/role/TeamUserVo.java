package com.partner.vo.role;

import com.partner.entity.PartnerRole;
import com.partner.vo.TeamMemberVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/27
 * @Version 1.0
 * @apiNote:
 */
@Data
public class TeamUserVo {

    @Schema(description = "角色成员列表")
    private List<TeamMemberVo> teamMemberList;

    @Schema(description = "角色详情")
    private PartnerRole role;

    public TeamUserVo(){
        teamMemberList = new ArrayList<>();
        role = null;
    }
}
