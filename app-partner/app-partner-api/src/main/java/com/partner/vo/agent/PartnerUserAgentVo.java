package com.partner.vo.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 伙伴用户代理信息
 */
@Data
public class PartnerUserAgentVo {

    @Schema(description = "代理名称")
    private String agentName;

    @Schema(description = "伙伴用户名称")
    private String partnerUserName;

    @Schema(description = "伙伴用户id")
    private Long partnerUserId;

    @Schema(description = "代理id")
    private Long agentId;

    @Schema(description = "伙伴用户名称")
    private List<String> partnerRoleNames;

    @Schema(description = "伙伴用户角色ID")
    private List<Long> partnerRoleIds;
}
