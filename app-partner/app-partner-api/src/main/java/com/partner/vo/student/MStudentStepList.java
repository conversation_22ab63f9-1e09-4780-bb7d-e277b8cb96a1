package com.partner.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class MStudentStepList {
    @Schema(description = "学生UUID")
    private String studentUUID;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "跟进人")
    private String followName;

    private String countryName;

    private Date itemStepTime;

    private Long studentOfferId;

    private Long institutionId;

    private String institutionNameChn;

    private String institutionName;

    private String courseName;

    private Long stepid;

    private int stepOrder;

    private String stepName;

    private String offerItemUUID;

    private String partnerUserName;

    private String partnerUserNameEn;

    public String getStepName() {
        if(stepName!=null && stepName.length()>0 && stepName.indexOf("（")!=-1){
            stepName=stepName.substring(0,stepName.indexOf("（"));
        }
        return stepName;
    }

    public String getFollowName() {
        if(partnerUserNameEn!=null && partnerUserNameEn.length()>0){
            followName=partnerUserNameEn;
        } else if (partnerUserName!=null && partnerUserName.length()>0) {
            followName=partnerUserName;
        }


        return followName;
    }
}
