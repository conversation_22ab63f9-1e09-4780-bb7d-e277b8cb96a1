package com.partner.vo.contract;

import cn.hutool.core.util.ObjectUtil;
import com.partner.entity.MAgentContractEntity;
import com.partner.entity.MFilePartnerEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
public class AgentContractVo extends MAgentContractEntity{

    @Schema(description = "判断查看合同类型:0不存在电子合同  1存在电子合同 2存在公司上传盖章合同")
    private Integer contractType;
    @Schema(description = "性质：1公司/2个人/3工作室/4国际学校/其他")
    private Integer nature;
    @Schema(description = "性质：1未签名  2已签名")
    private Integer singeType;

    @Schema(description="合同文件信息")
    MFilePartnerEntity file;
    @Schema(description="盖章合同文件信息")
    MFilePartnerEntity sealFile;


    @Schema(description = "代理合同类型名称")
    private String agentContractTypeName;

    @Schema(description = "代理绑定BD编号")
    private String num;
    @Schema(description = "代理绑定BD名称")
    private String bdName;
    @Schema(description = "代理绑定BD英文名称")
    private String bdNameEn;

    @Schema(description = "是否佣金结算口:0否 1是")
    private Integer isSsettlementPort;

    @Schema(description = "名称")
    private String name;



    @Schema(description = "代理合同状态名称:0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private String statusName;

    @Schema(description = "合同状态")
    private String contractStatusName;

    @Schema(description = "合同签署id")
    private Long signatureId;
    @Schema(description = "盖章合同sattacheid")
    private Long sattacheid;


    public String getStatusName() {
        if(Objects.nonNull(getStatus())){
            if(getStatus().intValue() == 0){
                statusName="待发起";
            }else if(getStatus().intValue() == 1){
                statusName="审批结束";
            }else if(getStatus().intValue() == 2){
                statusName="审批中";
            }else if(getStatus().intValue() == 3){
                statusName="审批拒绝";
            }else if(getStatus().intValue() == 4){
                statusName="申请放弃";
            }else if(getStatus().intValue() == 5){
                statusName="作废";
            }else if(getStatus().intValue() == 6){
                statusName="撤销";
            }

        }

        return statusName;
    }

    public String getContractStatusName() {
        LocalDateTime now=LocalDateTime.now();

        if(ObjectUtil.isEmpty(signatureId)){
            contractStatusName="未签署";
        }else if(getFkAgentContractTypeId().longValue()==1 && ObjectUtil.isEmpty(getSattacheid())){
            contractStatusName="未签署";
        }else if( ObjectUtil.isNotEmpty(getStartTime()) && ObjectUtil.isNotEmpty(getStartTime()) &&
                getStartTime().isBefore(now) && getEndTime().isAfter(now)){
            contractStatusName="生效中";
        } else if(   ObjectUtil.isNotEmpty(getStartTime()) &&
                 getEndTime().isBefore(now)){
            contractStatusName="已过期";
        }else {
            contractStatusName="已过期";
        }

        return contractStatusName;
    }
}
