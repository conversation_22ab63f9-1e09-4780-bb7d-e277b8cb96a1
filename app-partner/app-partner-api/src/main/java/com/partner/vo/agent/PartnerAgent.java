package com.partner.vo.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/7/7
 * @Version 1.0
 * @apiNote:伙伴用户代理关系缓存值对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PartnerAgent implements Serializable {

    @Schema(description = "代理id")
    private Long agentId;

    @Schema(description = "伙伴用户id")
    private Long partnerUserId;

    @Schema(description = "系统用户id")
    private Long systemUserId;
}
