package com.partner.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "学生分配")
public class StudentApportionVo {
    @Schema(description = "partner信息")
    private String partnerUserId;
    @Schema(description = "用户名称")
    private String name;
    @Schema(description = "用户账号")
    private String email;

    @Schema(description = "用户类型:")
    private String userType;


}
