package com.partner.vo.job;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.partner.entity.MEventRegistrationAgentEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "需要发送直播提醒openUser")
public class UserMEventVo extends MEventRegistrationAgentEntity {

    @Schema(description = "接收者（用户）的 openid")
    private String touser;

    @Schema(description = "活动主题")
    private String title;



    @Schema(description = "活动开始时间")
    private LocalDateTime eventTime;





    @Schema(description = "活动举办国家名称")
    private String areaCountryName;
    @Schema(description = "活动举办州省名称")
    private String areaStateName;
    @Schema(description = "活动举办城市名称")
    private String areaCityName;


    public String getAreaCountryName() {
        if(StringUtils.isEmpty(areaCountryName)) {
            areaCountryName="";
        }
        return areaCountryName;
    }

    public String getAreaStateName() {
        if(StringUtils.isEmpty(areaStateName)) {
            areaStateName="";
        }
        return areaStateName;
    }

    public String getAreaCityName() {
        if(StringUtils.isEmpty(areaCityName)) {
            areaCityName="";
        }
        return areaCityName;
    }
}
