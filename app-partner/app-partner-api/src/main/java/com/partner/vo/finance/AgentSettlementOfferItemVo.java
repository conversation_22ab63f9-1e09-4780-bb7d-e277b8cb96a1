package com.partner.vo.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
public class AgentSettlementOfferItemVo {
    @Schema(description="应付计划ID")
    private Long payablePlanId;
    /*@Schema(description="代理ID")
    private Long agentId;*/
    @Schema(description="代理UUID")
    private String agentUUID;
    @Schema(description="代理Name")
    private String agentName;

    @Schema(description = "学生UUID")
    private String studentUUID;
    @Schema(description="学生名称")
    private String studentName;
    @Schema(description = "学生生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;


    @Schema(description = "代理合同账户Id")
    private Long   fkAgentContractAccountId;
    @Schema(description = "币种编号（代理账户）")
    private String accountCurrencyTypeNum;
    @Schema(description="币种名（代理账户）")
    private String accountCurrencyName;

    @Schema(description="是否回滚，0否/1是")
    private Boolean rollBack;


    @Schema(description="国家名称")
    private String  countryName;

    @Schema(description="学校ID")
    private Long institutionId;
    @Schema(description="学校名称")
    private String institutionName;

    @Schema(description="学校名称中文")
    private String institutionNameChn;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    @Schema(description = "最后开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    @Schema(description = "获得签证函日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date COETime;



    @Schema(description = "应付币种")
    private String fkCurrencyTypeNum;

    @Schema(description = "应付金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal payableAmount;
    @Schema(description = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    @Schema(description = "实际支付金额(本期应付金额)")
    private BigDecimal amountActual;

    @Schema(description = "已付金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal paidAmount;

    @Schema(description = "汇率")
    private BigDecimal exchangeRate;
    @Schema(description = "兑换支付金额")
    private BigDecimal amountExchange;
    @Schema(description = "兑换手续费金额")
    private BigDecimal serviceFeeExchange;

    /*@Schema(description = "应付差额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal differenceAmount;*/

    @Schema(description = "收款状态  0:未收 1：部分已收 2：已收齐")
    private Integer status;

    @Schema(description = "收款状态  0:未收 1：部分已收 2：已收齐")
    private String statusName;

    @Schema(description = "申请状态")
    private String stepName;
    @Schema(description = "当前结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    private Integer statusSettlement;



    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @Schema(description = "创建时间")
    private Date gmtCreate;

    @Schema(description = "预付/新增结算设置时间")
    private Date settlementCreateTime;


    @Schema(description = "学费")
    private BigDecimal tuitionAmount;
    @Schema(description = "支付学费币种")
    private String tuitionCurrencyTypeNum;
    @Schema(description = "费率%(代理)")
    private BigDecimal commissionRate;

    @Schema(description = "定额金额")
    private BigDecimal fixedAmount;

    private Long offerItemId;


    /*public BigDecimal getDifferenceAmount() {

        differenceAmount=payableAmount.subtract(paidAmount);//应付差额
        //这里是否减去  分期付款表中的  实际支付？
        return differenceAmount;
    }*/

    public String getStatusName() {

        if(status!=null){
            if(status.intValue()==0){
                statusName="未收";
            } else if (status.intValue()==1) {
                statusName="部分已收";
            }else if (status.intValue()==2) {
                statusName="已收齐";
            }

        }

        return statusName;
    }

    public Date getOpeningTime() {

        if(Objects.nonNull(deferOpeningTime)){
            openingTime=deferOpeningTime;
        }


        return openingTime;
    }
}
