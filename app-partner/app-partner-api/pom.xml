<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.fzh</groupId>
		<artifactId>app-partner</artifactId>
		<version>3.8.1</version>
	</parent>

	<artifactId>app-partner-api</artifactId>
	<packaging>jar</packaging>

	<dependencies>
		<!--core 工具类-->
		<dependency>
			<groupId>com.fzh</groupId>
			<artifactId>common-core</artifactId>
		</dependency>
		<!--feign 注解依赖-->
		<dependency>
			<groupId>com.fzh</groupId>
			<artifactId>common-feign</artifactId>
		</dependency>
		<!--mybatis 依赖-->
		<dependency>
			<groupId>com.fzh</groupId>
			<artifactId>common-mybatis</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pig4cloud.excel</groupId>
			<artifactId>excel-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fzh</groupId>
			<artifactId>apps-api</artifactId>
			<version>3.8.1</version>
		</dependency>

		<dependency>
			<groupId>com.fzh</groupId>
			<artifactId>app-pmp-api</artifactId>
			<version>3.8.1</version>
		</dependency>

	</dependencies>
</project>
