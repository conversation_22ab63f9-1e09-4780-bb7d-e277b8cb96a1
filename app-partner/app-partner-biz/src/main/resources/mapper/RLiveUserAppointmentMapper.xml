<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.RLiveUserAppointmentMapper">
    <select id="getAppointmentList" resultType="com.partner.vo.AppointmentVo">

        SELECT
               rLivePartnerUserAppointment.fk_live_id,
               rLivePartnerUserAppointment.fk_partner_user_id,
               rLivePartnerUserAppointment.gmt_create,
               mPartnerUser.name,
               mPartnerUser.name_en
        FROM app_partner_center.r_live_partner_user_appointment rLivePartnerUserAppointment
        INNER JOIN app_partner_center.m_partner_user mPartnerUser ON rLivePartnerUserAppointment.fk_partner_user_id=mPartnerUser.id
        WHERE rLivePartnerUserAppointment.fk_live_id=#{liveId}


    </select>
    <select id="selectDetailList" resultType="com.partner.vo.jingang.UserAppointmentViewVo">
        SELECT
            rLivePartnerUserAppointment.*,
            mPartnerUser.name,
            mPartnerUser.name_en,
            appSystemCenter.wechat_icon_url
        FROM app_partner_center.r_live_partner_user_appointment rLivePartnerUserAppointment
                 INNER JOIN app_partner_center.m_partner_user mPartnerUser ON rLivePartnerUserAppointment.fk_partner_user_id=mPartnerUser.id
                 INNER JOIN app_system_center.system_user appSystemCenter  ON appSystemCenter.id=mPartnerUser.fk_user_id
        WHERE rLivePartnerUserAppointment.fk_live_id=#{liveId}


    </select>
    <select id="selectDetailUser" resultType="com.partner.vo.jingang.UserAppointmentViewVo">
        SELECT
            mPartnerUser.name,
            mPartnerUser.name_en,
            appSystemCenter.wechat_icon_url
        FROM  app_partner_center.m_partner_user mPartnerUser
                 INNER JOIN app_system_center.system_user appSystemCenter  ON appSystemCenter.id=mPartnerUser.fk_user_id
        WHERE mPartnerUser.id=#{partnerUserId} limit 1;


    </select>
</mapper>
