<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MInstitutionCourseMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MInstitutionCourseEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkInstitutionId" column="fk_institution_id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nameChn" column="name_chn" jdbcType="VARCHAR"/>
            <result property="nameDisplay" column="name_display" jdbcType="VARCHAR"/>
            <result property="startMonth" column="start_month" jdbcType="VARCHAR"/>
            <result property="startDateNote" column="start_date_note" jdbcType="VARCHAR"/>
            <result property="applyMonth" column="apply_month" jdbcType="VARCHAR"/>
            <result property="applyDateNote" column="apply_date_note" jdbcType="VARCHAR"/>
            <result property="fee" column="fee" jdbcType="DECIMAL"/>
            <result property="feeMax" column="fee_max" jdbcType="DECIMAL"/>
            <result property="feeNote" column="fee_note" jdbcType="VARCHAR"/>
            <result property="feeCny" column="fee_cny" jdbcType="DECIMAL"/>
            <result property="durationYear" column="duration_year" jdbcType="DECIMAL"/>
            <result property="durationNote" column="duration_note" jdbcType="VARCHAR"/>
            <result property="isDirect" column="is_direct" jdbcType="BIT"/>
            <result property="introduction" column="introduction" jdbcType="VARCHAR"/>
            <result property="introductionSource" column="introduction_source" jdbcType="VARCHAR"/>
            <result property="coreCourse" column="core_course" jdbcType="VARCHAR"/>
            <result property="entryStandards" column="entry_standards" jdbcType="VARCHAR"/>
            <result property="occupationDevelopment" column="occupation_development" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="isActive" column="is_active" jdbcType="BIT"/>
            <result property="publicLevel" column="public_level" jdbcType="VARCHAR"/>
            <result property="dataLevel" column="data_level" jdbcType="INTEGER"/>
            <result property="viewOrder" column="view_order" jdbcType="INTEGER"/>
            <result property="idGea" column="id_gea" jdbcType="VARCHAR"/>
            <result property="idIae" column="id_iae" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>
