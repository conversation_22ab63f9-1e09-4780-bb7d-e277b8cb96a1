<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.RPayablePlanSettlementInstallmentMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.RPayablePlanSettlementInstallmentEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="numSettlementBatch" column="num_settlement_batch" jdbcType="VARCHAR"/>
            <result property="fkPayablePlanId" column="fk_payable_plan_id" jdbcType="BIGINT"/>
            <result property="fkReceiptFormItemId" column="fk_receipt_form_item_id" jdbcType="BIGINT"/>
            <result property="fkPaymentFormItemId" column="fk_payment_form_item_id" jdbcType="BIGINT"/>
            <result property="fkInvoiceReceivablePlanId" column="fk_invoice_receivable_plan_id" jdbcType="BIGINT"/>
            <result property="fkInvoiceId" column="fk_invoice_id" jdbcType="BIGINT"/>
            <result property="amountExpect" column="amount_expect" jdbcType="DECIMAL"/>
            <result property="serviceFeeExpect" column="service_fee_expect" jdbcType="DECIMAL"/>
            <result property="amountActualInit" column="amount_actual_init" jdbcType="DECIMAL"/>
            <result property="serviceFeeActualInit" column="service_fee_actual_init" jdbcType="DECIMAL"/>
            <result property="amountActual" column="amount_actual" jdbcType="DECIMAL"/>
            <result property="serviceFeeActual" column="service_fee_actual" jdbcType="DECIMAL"/>
            <result property="accountExportTime" column="account_export_time" jdbcType="TIMESTAMP"/>
            <result property="isRollBack" column="is_roll_back" jdbcType="BIT"/>
            <result property="rollBackTime" column="roll_back_time" jdbcType="TIMESTAMP"/>
            <result property="fkAgentIdSettlement" column="fk_agent_id_settlement" jdbcType="BIGINT"/>
            <result property="fkAgentContractAccountId" column="fk_agent_contract_account_id" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="statusSettlement" column="status_settlement" jdbcType="INTEGER"/>
            <result property="statusReview" column="status_review" jdbcType="INTEGER"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>




</mapper>
