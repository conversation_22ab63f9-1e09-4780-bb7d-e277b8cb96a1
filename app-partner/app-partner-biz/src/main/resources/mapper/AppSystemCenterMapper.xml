<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.AppSystemCenterMapper">

    <select id="selectByPlatformCode" resultType="com.apps.api.entity.SystemMenuEntity">
        SELECT *
        FROM app_system_center.system_menu
        WHERE fk_platform_code = 'PARTNER'
          AND is_del_flag = 0
        ORDER BY view_order ASC
    </select>

    <select id="selectSystemMenuByIds" resultType="com.apps.api.entity.SystemMenuEntity">
        SELECT *
        FROM app_system_center.system_menu
        WHERE id IN
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_del_flag = 0
        ORDER BY view_order ASC
    </select>

    <select id="selectSystemPartnerDefaultRoleId" resultType="java.lang.Long">
        select id
        from app_system_center.system_role
        where fk_platform_code = 'PARTNER'
          and role_code = 'PARTNER_DEFAULT'
    </select>

</mapper>