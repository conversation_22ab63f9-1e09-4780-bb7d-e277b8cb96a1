<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MAgentContractAccountMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MAgentContractAccountEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="accountCardType" column="account_card_type" jdbcType="INTEGER"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="bankAccountNum" column="bank_account_num" jdbcType="VARCHAR"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="bankBranchName" column="bank_branch_name" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkAreaStateId" column="fk_area_state_id" jdbcType="BIGINT"/>
            <result property="fkAreaCityId" column="fk_area_city_id" jdbcType="BIGINT"/>
            <result property="fkAreaCityDivisionId" column="fk_area_city_division_id" jdbcType="BIGINT"/>
            <result property="bankAddress" column="bank_address" jdbcType="VARCHAR"/>
            <result property="bankCodeType" column="bank_code_type" jdbcType="VARCHAR"/>
            <result property="bankCode" column="bank_code" jdbcType="VARCHAR"/>
            <result property="areaCountryCode" column="area_country_code" jdbcType="VARCHAR"/>
            <result property="isDefault" column="is_default" jdbcType="BIT"/>
            <result property="isActive" column="is_active" jdbcType="BIT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fk_agent_id,fk_currency_type_num,
        account_card_type,bank_account,bank_account_num,
        bank_name,bank_branch_name,fk_area_country_id,
        fk_area_state_id,fk_area_city_id,fk_area_city_division_id,
        bank_address,bank_code_type,bank_code,
        area_country_code,is_default,is_active,
        remark,gmt_create,gmt_create_user,
        gmt_modified,gmt_modified_user
    </sql>
    <select id="getAgentContractAccount" resultType="com.partner.entity.MAgentContractAccountEntity">
        SELECT <include refid="Base_Column_List"/> FROM ais_sale_center.m_agent_contract_account
        WHERE fk_agent_id=#{agentId} AND is_active=1 limit 10
    </select>
</mapper>
