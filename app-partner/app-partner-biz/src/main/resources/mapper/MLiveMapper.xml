<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.MLiveMapper">
    <select id="getMLiveListPage"
            resultType="com.partner.vo.MLivePageVo">
        SELECT mLive.id,
               mLive.num,
               mLive.title,
               mLive.teacher_name_chn,
               mLive.live_time_start,
               mLive.live_time_end,
               mLive.live_url,
               mLive.loop_url,
               mLive.description,
               mLive.fk_live_type_id,
               mLive.gmt_create,
               (
                SELECT CONCAT(#{query.mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
                    INNER JOIN ais_file_center.m_file_platform platformFile ON platformFile.file_guid = sAttached.fk_file_guid
                    WHERE sAttached.fk_table_name='m_live' AND sAttached.type_key='m_live_logo'    AND sAttached.fk_table_id=mLive.id limit 1
                ) AS file_key,
                (
                SELECT GROUP_CONCAT(#{query.mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
                INNER JOIN ais_file_center.m_file_platform platformFile ON platformFile.file_guid = sAttached.fk_file_guid
                WHERE sAttached.fk_table_name='m_live' AND sAttached.type_key='m_live_file'    AND sAttached.fk_table_id=mLive.id
                ) AS file_key_file
        FROM ais_platform_center.m_live mLive
        LEFT JOIN ais_platform_center.u_live_type uLiveType ON mLive.fk_live_type_id=uLiveType.id

        WHERE 1 = 1 AND mLive.status=1  AND uLiveType.type_key='live'
        <if test="query.liveType != null and query.liveType == 0">
            AND mLive.live_time_end>=NOW() AND uLiveType.type_key='live'
        </if>

        <if test="query.liveType != null and query.liveType == 1">
            AND (
              (mLive.live_time_end &lt;=NOW() AND uLiveType.type_key='live' AND mLive.is_loop=1) OR uLiveType.type_key='recordedVideo'
            )
        </if>


        <if test="query.title != null ">
            AND (
            position(#{query.title,jdbcType=VARCHAR} in mLive.title)
                OR
            position(#{query.title,jdbcType=VARCHAR} in mLive.num)
                OR
            position(#{query.title,jdbcType=VARCHAR} in mLive.teacher_name_chn)

            )

        </if>



        <if test="query.liveType != null and query.liveType == 0">
            ORDER BY mLive.live_time_start
        </if>
        <if test="query.liveType != null and query.liveType == 1">
            ORDER BY mLive.live_time_end DESC
        </if>

        <if test="query.liveDate != null ">
            and mLive.live_time_start>= #{query.liveDate} and mLive.live_time_start &lt; DATE_ADD(#{query.liveDate}, INTERVAL
            1 DAY)
        </if>
    </select>
    <select id="getMLiveFive" resultType="com.partner.vo.MLivePageVo">
        SELECT mLive.id,
               mLive.num,
               mLive.title,
               mLive.teacher_name_chn,
               mLive.live_time_start,
               mLive.live_time_end,
               mLive.live_url,
               mLive.loop_url,
               (
                   SELECT CONCAT(#{mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
                                                                                        INNER JOIN ais_file_center.m_file_platform platformFile ON platformFile.file_guid = sAttached.fk_file_guid
                   WHERE sAttached.fk_table_name='m_live' AND sAttached.type_key='m_live_logo'    AND sAttached.fk_table_id=mLive.id limit 1
               ) AS file_key,
               (
                   SELECT GROUP_CONCAT(#{mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
                                                                                              INNER JOIN ais_file_center.m_file_platform platformFile ON platformFile.file_guid = sAttached.fk_file_guid
                   WHERE sAttached.fk_table_name='m_live' AND sAttached.type_key='m_live_file'    AND sAttached.fk_table_id=mLive.id
               ) AS file_key_file
        FROM ais_platform_center.m_live mLive
                 LEFT JOIN ais_platform_center.u_live_type uLiveType ON mLive.fk_live_type_id=uLiveType.id

        WHERE 1 = 1 AND mLive.status=1

        ORDER BY mLive.live_time_start DESC LIMIT 5


    </select>
    <select id="getStatus" resultType="com.partner.vo.MLivePageVo">

        SELECT mLive.id,
               mLive.num,
               mLive.title,
               mLive.teacher_name_chn,
               mLive.live_time_start,
               mLive.live_time_end,
               mLive.live_url,
               mLive.loop_url,
               mLive.description,
               mLive.fk_live_type_id,
               mLive.is_loop,
               #{liveType} AS isNewLive,
               (
                   SELECT CONCAT(#{mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
                                                                                        INNER JOIN ais_file_center.m_file_platform platformFile ON platformFile.file_guid = sAttached.fk_file_guid
                   WHERE sAttached.fk_table_name='m_live' AND sAttached.type_key='m_live_logo'    AND sAttached.fk_table_id=mLive.id limit 1
               ) AS file_key,
               (
                   SELECT GROUP_CONCAT(#{mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
                                                                                              INNER JOIN ais_file_center.m_file_platform platformFile ON platformFile.file_guid = sAttached.fk_file_guid
                   WHERE sAttached.fk_table_name='m_live' AND sAttached.type_key='m_live_file'    AND sAttached.fk_table_id=mLive.id
               ) AS file_key_file
        FROM ais_platform_center.m_live mLive
                 LEFT JOIN ais_platform_center.u_live_type uLiveType ON mLive.fk_live_type_id=uLiveType.id

        WHERE 1 = 1
            <if test="liveType != null and liveType == 0">
                AND mLive.live_time_end>=NOW() AND uLiveType.type_key='live'
            </if>

            <if test="liveType != null and liveType == 1">
                AND (
                (mLive.live_time_end &lt;=NOW() AND uLiveType.type_key='live' AND mLive.is_loop=1) OR uLiveType.type_key='recordedVideo'
                )
            </if>
                AND YEAR(mLive.live_time_start)=#{year} AND MONTH(mLive.live_time_start)=#{month}
                ORDER BY mLive.live_time_start
    </select>

</mapper>