<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MStudentOfferItemAgentConfirmMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MStudentOfferItemAgentConfirmEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkTenantId" column="fk_tenant_id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkStudentOfferItemId" column="fk_student_offer_item_id" jdbcType="BIGINT"/>
            <result property="fkPartnerUserId" column="fk_partner_user_id" jdbcType="BIGINT"/>
            <result property="isSystemConfirmed" column="is_system_confirmed" jdbcType="BIT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


    <insert id="insertAffirmOfferItem">
        INSERT INTO app_partner_center.m_student_offer_item_agent_confirm(
        fk_tenant_id,
        fk_agent_id,
        fk_student_offer_item_id,
        fk_partner_user_id,
        is_system_confirmed,
        gmt_create,
        gmt_create_user)
        values

        <foreach collection="CommissionAffirmList" item="item" index="index" separator=",">
            (
            #{item.fkTenantId},
            #{item.fkAgentId},
            #{item.fkStudentOfferItemId},
            #{item.fkPartnerUserId},
            #{item.isSystemConfirmed},
            #{item.gmtCreate},
            #{item.gmtCreateUser}
            )
        </foreach>
    </insert>
    <select id="valiyAffirmOfferItem" resultType="java.lang.Integer">
            SELECT COUNT(*) FROM app_partner_center.m_student_offer_item_agent_confirm mStudentOfferItemAgentConfirm
            INNER JOIN ais_sale_center.r_student_offer_item_uuid rStudentOfferItemUuid
            ON mStudentOfferItemAgentConfirm.fk_student_offer_item_id=rStudentOfferItemUuid.fk_student_offer_item_id
            WHERE 1=1 AND rStudentOfferItemUuid.fk_student_offer_item_uuid IN
        <foreach collection="valiyAffirmList" item="valiyAffirmVo" index="index" open="(" separator="," close=")">
            #{valiyAffirmVo.offerItemUUID}
        </foreach>


    </select>


</mapper>
