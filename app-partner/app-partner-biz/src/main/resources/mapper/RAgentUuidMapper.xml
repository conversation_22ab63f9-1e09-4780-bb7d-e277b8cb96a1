<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.RAgentUuidMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.RAgentUuidEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkAgentUuid" column="fk_agent_uuid" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fk_agent_id,fk_agent_uuid,
        gmt_create,gmt_create_user,gmt_modified,
        gmt_modified_user
    </sql>
    <select id="selectByUUID" resultType="com.partner.entity.RAgentUuidEntity">
        SELECT <include refid="Base_Column_List"/> FROM ais_sale_center.r_agent_uuid
        WHERE fk_agent_uuid=#{uuid}
    </select>
</mapper>
