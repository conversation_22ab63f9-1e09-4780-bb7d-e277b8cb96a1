<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.UAreaCountryMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.UAreaCountryEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nameChn" column="name_chn" jdbcType="VARCHAR"/>
            <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
            <result property="capital" column="capital" jdbcType="VARCHAR"/>
            <result property="population" column="population" jdbcType="VARCHAR"/>
            <result property="area" column="area" jdbcType="VARCHAR"/>
            <result property="language" column="language" jdbcType="VARCHAR"/>
            <result property="religion" column="religion" jdbcType="VARCHAR"/>
            <result property="timeDifference" column="time_difference" jdbcType="VARCHAR"/>
            <result property="president" column="president" jdbcType="VARCHAR"/>
            <result property="flagMeaning" column="flag_meaning" jdbcType="VARCHAR"/>
            <result property="emblemMeaning" column="emblem_meaning" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="businessAreaKey" column="business_area_key" jdbcType="VARCHAR"/>
            <result property="publicLevel" column="public_level" jdbcType="VARCHAR"/>
            <result property="viewOrder" column="view_order" jdbcType="INTEGER"/>
            <result property="idGea" column="id_gea" jdbcType="VARCHAR"/>
            <result property="idIae" column="id_iae" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,fk_currency_type_num,num,
        name,name_chn,area_code,
        capital,population,area,
        language,religion,time_difference,
        president,flag_meaning,emblem_meaning,
        remark,business_area_key,public_level,
        view_order,id_gea,id_iae,
        gmt_create,gmt_create_user,gmt_modified,
        gmt_modified_user
    </sql>
</mapper>
