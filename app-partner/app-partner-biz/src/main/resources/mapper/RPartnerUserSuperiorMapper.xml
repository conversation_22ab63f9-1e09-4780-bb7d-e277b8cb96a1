<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.RPartnerUserSuperiorMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.RPartnerUserSuperiorEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkTenantId" column="fk_tenant_id" jdbcType="BIGINT"/>
            <result property="fkPartnerUserId" column="fk_partner_user_id" jdbcType="BIGINT"/>
            <result property="fkPartnerUserIdSuperior" column="fk_partner_user_id_superior" jdbcType="BIGINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectChildList" resultType="java.lang.Long">
        SELECT fk_partner_user_id
        FROM (
                 SELECT @ids AS _ids,
                        (SELECT @ids := GROUP_CONCAT(fk_partner_user_id)
                         FROM r_partner_user_superior
                         WHERE FIND_IN_SET(fk_partner_user_id_superior, @ids)
                        ) AS cids,
                        @l := @l+1 AS LEVEL
                 FROM r_partner_user_superior,
                      (SELECT @ids := #{partUserId}, @l := 0 ) b
                 WHERE @ids IS NOT NULL
             ) id,
             r_partner_user_superior DATA
        WHERE FIND_IN_SET(DATA.fk_partner_user_id_superior, id._ids)
        GROUP BY fk_partner_user_id, fk_partner_user_id_superior
        ORDER BY fk_partner_user_id

    </select>
    <select id="selectLevelChildList" resultType="java.lang.Long">
        SELECT rPartnerUserSuperior.fk_partner_user_id
        FROM r_partner_user_superior rPartnerUserSuperior
        WHERE 1=1 AND rPartnerUserSuperior.fk_partner_user_id_superior IN
        <foreach collection="partUserIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY rPartnerUserSuperior.fk_partner_user_id
    </select>
    <select id="selectLevelSupList" resultType="com.partner.dto.base.UserInfoParams">
        SELECT rPartnerUserSuperior.fk_partner_user_id_superior AS partnerUserId,
               mPartnerUser.fk_user_id AS userId
        FROM app_partner_center.r_partner_user_superior rPartnerUserSuperior
        INNER JOIN app_partner_center.m_partner_user mPartnerUser ON rPartnerUserSuperior.fk_partner_user_id_superior=mPartnerUser.id
        WHERE  rPartnerUserSuperior.fk_partner_user_id =#{partUserId}
        Limit 1
    </select>

    <select id="selectSuper" resultType="java.lang.Integer">
        SELECT count(*)
        FROM r_partner_user_superior rPartnerUserSuperior
        INNER JOIN m_partner_user mPartnerUser ON rPartnerUserSuperior.fk_partner_user_id_superior=mPartnerUser.id

        WHERE rPartnerUserSuperior.fk_partner_user_id=#{partnerUserId}
        <if test="year != 0 ">
            AND YEAR(mPartnerUser.gmt_create) &lt;=#{year}
        </if>

    </select>
</mapper>
