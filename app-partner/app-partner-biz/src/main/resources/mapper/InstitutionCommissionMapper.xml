<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.InstitutionCommissionMapper">

    <select id="getCommissionPage"
            resultType="com.partner.vo.CommissionDetailVo">
        SELECT mInstitution.id AS institutionId,
        mInstitution.name AS institutionName,
        mInstitution.name_chn AS institutionNameChn,
        mInstitution.fk_area_country_id AS countryId,
        country.name AS countryName,
        country.name_chn AS countryNameChn,
        commission.year,
        commission.title,
        commission.commission,
        commission.agent_commission agentCommission,

        (SELECT CONCAT(#{query.mMageAddress}, institutionFile.file_key)
        FROM ais_file_center.m_file_institution institutionFile
        INNER JOIN
        ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name = 'm_institution'
        AND sAttached.type_key = 'institution_logo'
        AND sAttached.fk_table_id = mInstitution.id
        LIMIT 1) AS fileKey,
        (SELECT CONCAT(#{query.mMageAddress}, institutionFile.file_key)
        FROM ais_file_center.m_file_institution institutionFile
        INNER JOIN
        ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name = 'm_institution'
        AND sAttached.type_key = 'institution_pic'
        AND sAttached.fk_table_id = mInstitution.id
        LIMIT 1) AS institutionPic,
        (SELECT CONCAT(#{query.mMageAddress}, institutionFile.file_key)
        FROM ais_file_center.m_file_institution institutionFile
        INNER JOIN
        ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name = 'm_institution'
        AND sAttached.type_key = 'institution_cover'
        AND sAttached.fk_table_id = mInstitution.id
        LIMIT 1) AS fileKeyCover,

        (SELECT GROUP_CONCAT(fk_label_id) FROM ais_pmp_center.r_institution_commission_label rLabel WHERE
        rLabel.fk_institution_id=mInstitution.id) AS labelIds

        FROM ais_institution_center.m_institution mInstitution

        <if test="query.type != null  and query.type==1">
            INNER JOIN  ais_platform_center.m_institution_high_commission mInstitutionHighCommission
            ON mInstitution.id=mInstitutionHighCommission.fk_institution_id AND mInstitutionHighCommission.fk_platform_id=2
        </if>
        INNER JOIN
        ais_institution_center.u_area_country AS country ON mInstitution.fk_area_country_id = country.id
        INNER JOIN
        ais_mps_center.r_institution_provider_commission_institution rCommissionInstitution ON
        rCommissionInstitution.fk_institution_id = mInstitution.id
        INNER JOIN
        ais_mps_center.m_institution_provider_commission AS commission ON
        commission.id = rCommissionInstitution.fk_institution_provider_commission_id
        LEFT JOIN ais_institution_center.m_institution_provider AS provider ON commission.fk_institution_provider_id =
        provider.id
        <if test="query.majorLevelId != null  ">
            LEFT JOIN (
            SELECT fk_institution_provider_commission_id,GROUP_CONCAT(fk_major_level_id) fk_major_level_ids from ais_pmp_center.r_institution_provider_commission_major_level
            GROUP BY fk_institution_provider_commission_id
            ) a  ON a.fk_institution_provider_commission_id=commission.id
        </if>
        <if test="query.applyCountryId != null  ">
            LEFT JOIN (
            SELECT fk_institution_provider_commission_id,GROUP_CONCAT(fk_area_country_id) fk_area_country_ids
            FROM ais_pmp_center.r_institution_provider_commission_area_country
            GROUP BY fk_institution_provider_commission_id
            ) b  ON b.fk_institution_provider_commission_id=commission.id

        </if>


        WHERE commission.year = #{query.year}

        <if test="query.areAcountryId != null  ">
            AND mInstitution.fk_area_country_id=
            #{query.areAcountryId}
        </if>
        <if test="query.institutionName != null and query.institutionName != '' ">
            AND
            (mInstitution.name_chn like CONCAT('%', #{query.institutionName}, '%')
                OR mInstitution.name like CONCAT('%', #{query.institutionName}, '%')
                OR position(#{query.institutionName,jdbcType=VARCHAR} in commission.title))

        </if>
        <if test="query.institutionTypeId != null  ">
            AND mInstitution.fk_institution_type_id=
            #{query.institutionTypeId}
        </if>

        <if test="query.institutionGroupId != null  ">
            AND provider.fk_institution_group_id =
            #{query.institutionGroupId}
        </if>

        <if test="query.majorLevelId != null  ">
            AND FIND_IN_SET(
            #{query.majorLevelId},
            a
            .
            fk_major_level_ids
            )
        </if>

        <if test="query.applyCountryId != null  ">
            AND FIND_IN_SET(
            #{query.applyCountryId},
            b
            .
            fk_area_country_ids
            )
        </if>
        <if test="query.type != null  and query.type==1">
            AND mInstitutionHighCommission.status=1
        </if>

        GROUP BY mInstitution.id

        <if test="query.type != null  and query.type==1">
            ORDER BY mInstitutionHighCommission.weight
            DESC
        </if>


    </select>


    <select id="getCommissionDetail"
            resultType="com.partner.vo.CommissionDetailInfo">

        SELECT commission.year,
               commission.title,
               a.level_name,
               b.national AS national,
               commission.commission,
               commission.agent_commission,
               commission.agent_commission_note,
               commission.follow_commission,
               commission.follow_commission_note,
               commission.agent_follow_commission,
               commission.agent_follow_commission_note
        FROM ais_institution_center.m_institution mi
                 INNER JOIN ais_mps_center.r_institution_provider_commission_institution rpci
                            ON rpci.fk_institution_id = mi.id
                 INNER JOIN ais_mps_center.m_institution_provider_commission AS commission
                            ON commission.id = rpci.fk_institution_provider_commission_id
                 LEFT JOIN ais_institution_center.m_institution_provider AS provider
                           ON commission.fk_institution_provider_id = provider.id

                 LEFT JOIN (SELECT rProviderCommissionCountry.fk_institution_provider_commission_id,
                                   GROUP_CONCAT(country.name) national
                            from ais_pmp_center.r_institution_provider_commission_area_country rProviderCommissionCountry
                                     LEFT JOIN ais_institution_center.u_area_country AS country
                                               ON rProviderCommissionCountry.fk_area_country_id = country.id
                            GROUP BY rProviderCommissionCountry.fk_institution_provider_commission_id) b
                           ON commission.id = b.fk_institution_provider_commission_id

                 LEFT JOIN
             (SELECT commissionML.fk_institution_provider_commission_id,
                     GROUP_CONCAT(majorlevel.level_name) AS level_name
              FROM ais_mps_center.r_institution_provider_commission_major_level AS commissionML
                       LEFT JOIN ais_institution_center.u_major_level majorlevel
                                 ON commissionML.fk_major_level_id = majorlevel.id
              GROUP BY commissionML.fk_institution_provider_commission_id) a
             ON commission.id = a.fk_institution_provider_commission_id
        WHERE commission.year = #{year}
          AND mi.id = #{institutionId}

          AND commission.id IN (SELECT fk_institution_provider_commission_id
                                FROM ais_mps_center.r_institution_provider_commission_company
                                WHERE fk_company_id = #{companyId})


    </select>

    <select id="getCountryCombox" resultType="com.partner.vo.combox.CountryCombox">

        SELECT DISTINCT mi.fk_area_country_id AS areaCountryId,
                        country.name_chn      AS areaCountryName

        FROM ais_institution_center.m_institution mi
                 INNER JOIN ais_institution_center.u_area_country AS country ON mi.fk_area_country_id = country.id
                 INNER JOIN ais_mps_center.r_institution_provider_commission_institution rpci
                            ON rpci.fk_institution_id = mi.id
                 INNER JOIN ais_mps_center.m_institution_provider_commission AS commission
                            ON commission.id = rpci.fk_institution_provider_commission_id
        WHERE commission.year = #{year}
          AND FIND_IN_SET(13, country.public_level)
        ORDER BY country.view_order DESC

    </select>


    <select id="getCountryComboxAll" resultType="com.partner.vo.combox.CountryCombox">


        SELECT country.id       AS areaCountryId,
               country.`name`   AS areaCountryName,
               country.name_chn AS areaCountryNameChn
        FROM ais_institution_center.u_area_country AS country
        WHERE country.num NOT IN ('N/A', 'OTH', 'GLB')
          AND FIND_IN_SET(13, country.public_level)
        ORDER BY country.view_order DESC;
    </select>


    <select id="getTallCommissionList" resultType="com.partner.vo.CommissionDetailVo">

        SELECT
        mi.id AS institutionId,
        mi.name AS institutionName,
        mi.name_chn AS institutionNameChn,
        mihc.recommend_info AS title,
        mihc.gmt_create,
        (SELECT CONCAT(#{mMageAddress}, institutionFile.file_key)
        FROM ais_file_center.m_file_institution institutionFile
        INNER JOIN ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid =
        sAttached.fk_file_guid
        WHERE sAttached.fk_table_name = 'm_institution' AND sAttached.type_key = 'institution_logo' AND
        sAttached.fk_table_id = mi.id
        LIMIT 1) AS fileKey,
        (SELECT CONCAT(#{mMageAddress}, institutionFile.file_key)
        FROM ais_file_center.m_file_institution institutionFile
        INNER JOIN ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid =
        sAttached.fk_file_guid
        WHERE sAttached.fk_table_name = 'm_institution' AND sAttached.type_key = 'institution_pic' AND
        sAttached.fk_table_id = mi.id
        LIMIT 1) AS institutionPic,
        (SELECT CONCAT(#{mMageAddress}, institutionFile.file_key)
        FROM ais_file_center.m_file_institution institutionFile
        INNER JOIN ais_institution_center.s_media_and_attached sAttached ON institutionFile.file_guid =
        sAttached.fk_file_guid
        WHERE sAttached.fk_table_name = 'm_institution' AND sAttached.type_key = 'institution_cover' AND
        sAttached.fk_table_id = mi.id
        LIMIT 1) AS fileKeyCover,
        (
        SELECT CONCAT(#{mMageAddress}, filePlatform.file_key) from ais_platform_center.s_media_and_attached sAttached
        INNER JOIN ais_file_center.m_file_platform filePlatform ON filePlatform.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name='m_institution_high_commission' AND sAttached.fk_table_id=mihc.id limit 1
        ) AS file_commission
        FROM ais_platform_center.m_institution_high_commission mihc
        INNER JOIN ais_institution_center.m_institution mi ON mi.id=mihc.fk_institution_id
        WHERE mihc.fk_company_id=#{companyId} AND mihc.status=1 AND mihc.fk_platform_id=2
        <if test="institutionId != null ">
            AND mi.id=
            #{institutionId}
        </if>

        <if test="institutionName != null and institutionName != '' ">
            AND
            (mi.name_chn like CONCAT('%', #{institutionName}, '%')
                OR mi.name like CONCAT('%', #{institutionName}, '%')
                OR position(#{institutionName,jdbcType=VARCHAR} in mihc.recommend_info))

        </if>


        GROUP BY mi.id

        ORDER BY mihc.weight DESC ,mihc.gmt_create ASC

        <if test="type != null  and type==0">
            LIMIT 6
        </if>
        <if test="type != null  and type==1">
            LIMIT 100
        </if>

    </select>


    <select id="getGroupByCommission" resultType="com.partner.vo.combox.GroupResultCombox">
        SELECT
        institutionGroup.id AS id,
        institutionGroup.`name` AS groupName,
        institutionGroup.name_chn AS groupNameChn

        FROM ais_pmp_center.m_institution_provider_commission AS commission
        INNER JOIN ais_pmp_center.r_institution_provider_commission_company AS commissionCompany ON commission.id =
        commissionCompany.fk_institution_provider_commission_id
        LEFT JOIN ais_institution_center.m_institution_provider AS institutionProvider ON
        commission.fk_institution_provider_id = institutionProvider.id
        LEFT JOIN ais_institution_center.m_institution_group AS institutionGroup ON
        institutionProvider.fk_institution_group_id = institutionGroup.id
        <where>
            <if test="companyId != null and companyId != ''">
                AND commissionCompany.fk_company_id =
                #{companyId}
            </if>
            <if test="year != null and year>0 ">
                AND commission.year=
                #{year}
            </if>
            AND institutionGroup.id IS NOT NULL
        </where>
        GROUP BY institutionGroup.id, institutionGroup.`name`, institutionGroup.name_chn
        ORDER BY institutionGroup.`name`;
    </select>


    <resultMap id="LeaveMap" type="com.partner.vo.combox.LeaveResultCombox">
        <id column="id" property="id" javaType="java.lang.Long"></id>
        <result column="majorLevelName" property="leaveName"></result>
        <result column="majorLevelNameChn" property="leaveNameChn"></result>
    </resultMap>
    <select id="getLeaveList" resultMap="LeaveMap">
        SELECT
        majorLevel.id AS id,
        majorLevel.group_name AS majorGroupName,
        majorLevel.level_name AS majorLevelName,
        majorLevel.group_name_chn AS majorGroupNameChn,
        majorLevel.level_name_chn AS majorLevelNameChn

        FROM ais_pmp_center.m_institution_provider_commission AS commission
        INNER JOIN ais_pmp_center.r_institution_provider_commission_company AS commissionCompany ON commission.id =
        commissionCompany.fk_institution_provider_commission_id
        INNER JOIN ais_pmp_center.r_institution_provider_commission_major_level AS commissionMajorLevel ON commission.id
        = commissionMajorLevel.fk_institution_provider_commission_id
        LEFT JOIN ais_institution_center.u_major_level AS majorLevel ON commissionMajorLevel.fk_major_level_id =
        majorLevel.id
        <where>
            commission.is_active = 1
            <if test="companyId != null">
                AND commissionCompany.fk_company_id =
                #{companyId}
            </if>
            <if test="year != null and year>0 ">
                AND commission.year=
                #{year}
            </if>
        </where>
        GROUP BY majorLevel.id, majorLevel.group_name, majorLevel.level_name, majorLevel.group_name_chn,
        majorLevel.level_name_chn, majorLevel.view_order
        ORDER BY majorLevel.view_order DESC;
    </select>
    <select id="getTypeList" resultType="com.partner.vo.combox.TypeResultCombox">

        SELECT
        institutionType.id AS id,
        institutionType.type_name AS typeName,
        institutionType.type_name_chn AS typeNameChn

        FROM ais_pmp_center.m_institution_provider_commission AS commission
        INNER JOIN ais_pmp_center.r_institution_provider_commission_institution AS commissionInstitution ON
        commission.id = commissionInstitution.fk_institution_provider_commission_id
        INNER JOIN ais_pmp_center.r_institution_provider_commission_company AS commissionCompany ON commission.id =
        commissionCompany.fk_institution_provider_commission_id
        LEFT JOIN ais_institution_center.m_institution AS institution ON commissionInstitution.fk_institution_id =
        institution.id
        LEFT JOIN ais_institution_center.u_institution_type AS institutionType ON institution.fk_institution_type_id =
        institutionType.id
        <where>
            <if test="companyId != null and companyId != ''">
                AND commissionCompany.fk_company_id =
                #{companyId}
            </if>
            <if test="year != null and year>0 ">
                AND commission.year=
                #{year}
            </if>
        </where>
        GROUP BY
        institutionType.id, institutionType.type_name, institutionType.type_name_chn
        ORDER BY institutionType.view_order DESC
    </select>

    <select id="selectInstitutionCover" resultType="com.pmp.vo.institution.InstitutionVo">
        select i.id as institutionId,
        f.file_key as institutionCover
        from ais_institution_center.m_institution i
        inner join ais_institution_center.s_media_and_attached me on
        i.id = me.fk_table_id and me.fk_table_name = 'm_institution'
        AND me.type_key = 'institution_logo'
        inner join ais_file_center.m_file_institution f on f.file_guid = me.fk_file_guid
        where i.id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        group by f.file_key, i.id
    </select>


</mapper>
