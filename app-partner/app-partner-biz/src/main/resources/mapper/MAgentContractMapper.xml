<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MAgentContractMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MAgentContractEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
        <result property="fkAgentContractTypeId" column="fk_agent_contract_type_id" jdbcType="BIGINT"/>
        <result property="fkAgentContractIdRevoke" column="fk_agent_contract_id_revoke" jdbcType="BIGINT"/>
        <result property="contractApprovalMode" column="contract_approval_mode" jdbcType="INTEGER"/>
        <result property="contractNum" column="contract_num" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="returnCommissionRateNote" column="return_commission_rate_note" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="isActive" column="is_active" jdbcType="BIT"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectByAgentId" resultType="com.partner.vo.contract.AgentContractVo">
        SELECT mAgentContract.*,
               uAgentContractType.type_name AS agentContractTypeName,
               mStaff.num                   AS num,
               mStaff.name                  AS bdName,
               mStaff.name_en               AS bdNameEn,
               rAgentContractSignature.id   AS signatureId,
               (SELECT sAttached.id
                FROM ais_sale_center.s_media_and_attached sAttached
                WHERE sAttached.fk_table_name = 'm_agent_contract'
                  AND sAttached.type_key = 'sale_contract_seal_file'
                  AND sAttached.fk_table_id = mAgentContract.id
                limit 1)                    AS sattacheid
        FROM ais_sale_center.m_agent_contract mAgentContract
                 LEFT JOIN ais_sale_center.u_agent_contract_type uAgentContractType
                           ON mAgentContract.fk_agent_contract_type_id = uAgentContractType.id
                 LEFT JOIN ais_sale_center.r_agent_staff rAgentStaff
                           on rAgentStaff.fk_agent_id = mAgentContract.fk_agent_id and rAgentStaff.is_active = 1
                 LEFT JOIN ais_permission_center.m_staff mStaff
                           ON rAgentStaff.fk_staff_id = mStaff.id and mStaff.is_active = 1
                 LEFT JOIN ais_sale_center.r_agent_contract_signature rAgentContractSignature
                           ON rAgentContractSignature.fk_agent_contract_id = mAgentContract.id

        WHERE mAgentContract.fk_agent_id = #{fkAgentId}
          and mAgentContract.id = #{id}

        LIMIT 1

    </select>


    <select id="getContractPage" resultType="com.partner.vo.contract.AgentContractVo">
        SELECT mAgentContract.*,
        uAgentContractType.type_name AS agentContractTypeName,
        mStaff.num AS num,
        mStaff.name AS bdName,
        mStaff.name_en AS bdNameEn,
        rAgentContractSignature.id AS signatureId,
        (
        SELECT sAttached.id FROM ais_sale_center.s_media_and_attached sAttached
        WHERE sAttached.fk_table_name='m_agent_contract' AND sAttached.type_key='sale_contract_seal_file'
        AND sAttached.fk_table_id=mAgentContract.id limit 1
        ) AS sattacheid,
        m_agent.is_settlement_port,
        m_agent.nature


        FROM ais_sale_center.m_agent_contract mAgentContract
        INNER JOIN ais_sale_center.m_agent m_agent ON m_agent.id=mAgentContract.fk_agent_id
        LEFT JOIN ais_sale_center.u_agent_contract_type uAgentContractType ON
        mAgentContract.fk_agent_contract_type_id=uAgentContractType.id
        LEFT JOIN ais_sale_center.r_agent_staff rAgentStaff on rAgentStaff.fk_agent_id = mAgentContract.fk_agent_id and
        rAgentStaff.is_active =1
        LEFT JOIN ais_permission_center.m_staff mStaff ON rAgentStaff.fk_staff_id=mStaff.id and mStaff.is_active=1
        LEFT JOIN ais_sale_center.r_agent_contract_signature rAgentContractSignature ON
        rAgentContractSignature.fk_agent_contract_id=mAgentContract.id
        LEFT JOIN
        (
        SELECT distinct sAttached.fk_table_id FROM ais_sale_center.s_media_and_attached sAttached
        WHERE sAttached.fk_table_name='m_agent_contract' AND sAttached.type_key='sale_contract_seal_file'
        ) seal ON mAgentContract.id=seal.fk_table_id


        WHERE mAgentContract.fk_agent_id=#{query.fkAgentId}
        <if test="query.contractTemplateMode != null">
            AND mAgentContract.contract_template_mode=
            #{query.contractTemplateMode}
        </if>

        <if test="query.statusContract != null and query.statusContract == 3">
            AND rAgentContractSignature.id is NULL
        </if>

        <if test="query.statusContract != null and query.statusContract == 1">
            AND rAgentContractSignature.id is NOT NULL
                AND mAgentContract.start_time &lt;=NOW() AND mAgentContract.end_time>=NOW()
        </if>
        <if test="query.statusContract != null and query.statusContract == 2">
            AND rAgentContractSignature.id is NOT NULL
                AND mAgentContract.end_time &lt;=NOW()
        </if>

        <if test="query.statusContract != null and query.statusContract == 4">
            AND rAgentContractSignature.id is NOT NULL AND seal.fk_table_id IS NULL
        </if>

        and mAgentContract.is_active=1

    </select>
    <select id="selectByContractId" resultType="com.partner.entity.MAgentContractEntity">
        select *
        from ais_sale_center.m_agent_contract
        where id = #{contractId}
    </select>

    <select id="selectContractPage" resultType="com.partner.vo.contract.AgentContractInfoVo">
        select c.id as contractId,
        c.contract_num as contractNum,
        c.start_time as startTime,
        c.end_time as endTime,
        c.fk_agent_id as agentId,
        c.contract_approval_status as contractStatus,
        t.type_name as contractTypeName,
        a.nature as nature
        from ais_sale_center.m_agent_contract c
        inner join ais_sale_center.m_agent a on c.fk_agent_id = a.id
        left join ais_sale_center.u_agent_contract_type t on c.fk_agent_contract_type_id = t.id
        <where>
            c.fk_agent_id = #{params.fkAgentId} and c.is_active = 1
            and c.contract_approval_status in (2,3,4,-4)
            <if test="params.contractStatus != null">
                <!-- 通过、审批中、已驳回等原始状态 -->
                <if test="params.contractStatus == 2
                    or params.contractStatus == 3
                    or params.contractStatus == -4">
                    and c.contract_approval_status = #{params.contractStatus}
                </if>
                <!-- 审核通过-未生效 -->
                <if test="params.contractStatus == 4">
                    and c.contract_approval_status = 4 and NOW() &lt; c.start_time
                </if>
                <!-- 审核通过-生效中 -->
                <if test="params.contractStatus == 6">
                    and c.contract_approval_status = 4 and NOW() &gt;= c.start_time and NOW() &lt;= c.end_time
                </if>
                <!-- 审核通过-已过期 -->
                <if test="params.contractStatus == 7">
                    and c.contract_approval_status = 4 and NOW() &gt; c.end_time
                </if>
            </if>
        </where>
        order by c.id desc
    </select>

    <select id="selectContractDetail" resultType="com.partner.vo.contract.AgentContractDetailVo">
        select c.id                          as contractId,
               c.contract_num                as contractNum,
               c.start_time                  as startTime,
               c.end_time                    as endTime,
               c.fk_agent_id                 as agentId,
               c.contract_approval_status    as contractStatus,
               t.type_name                   as contractTypeName,
               a.nature                      as nature,
               c.gmt_create                  as gmtCreate,
               a.name                        as agentName,
               c.return_commission_rate_note as returnCommissionRateNote,
               c.contract_template_mode      as contractTemplateMode,
               bc.bd_code                    as bdCode,
               s.name                        as bdName,
               s.name_en                     as bdNameEn
        from ais_sale_center.m_agent_contract c
                 inner join ais_sale_center.m_agent a on c.fk_agent_id = a.id
                 left join ais_sale_center.u_agent_contract_type t on c.fk_agent_contract_type_id = t.id
                 left join ais_sale_center.r_agent_staff ras on ras.fk_agent_id = a.id and ras.is_active = 1
                 left join ais_permission_center.m_staff s on s.id = ras.fk_staff_id
                 left join ais_sale_center.r_staff_bd_code bc on bc.fk_staff_id = s.id
        where c.id = #{contractId}
    </select>

    <select id="getLatestAgentContractStatus" resultType="com.partner.vo.contract.AgentContractInfoVo">
        select c.id                       as contractId,
               c.contract_num             as contractNum,
               c.start_time               as startTime,
               c.end_time                 as endTime,
               c.fk_agent_id              as agentId,
               c.contract_approval_status as contractStatus,
               c.gmt_create               as gmtCreate
        from ais_sale_center.m_agent_contract c
                 inner join ais_sale_center.m_agent a on c.fk_agent_id = a.id
        where c.fk_agent_id = #{agentId}
        order by c.id desc
        limit 1
    </select>


</mapper>
