<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.SystemUserMapper">

    <resultMap id="BaseResultMap" type="com.apps.api.entity.SystemUserEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkTenantId" column="fk_tenant_id" jdbcType="BIGINT"/>
            <result property="fkFromPlatformId" column="fk_from_platform_id" jdbcType="BIGINT"/>
            <result property="fkFromPlatformCode" column="fk_from_platform_code" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nameEn" column="name_en" jdbcType="VARCHAR"/>
            <result property="lastNamePy" column="last_name_py" jdbcType="VARCHAR"/>
            <result property="firstNamePy" column="first_name_py" jdbcType="VARCHAR"/>
            <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="INTEGER"/>
            <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
            <result property="identityCard" column="identity_card" jdbcType="VARCHAR"/>
            <result property="mobileAreaCode" column="mobile_area_code" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="telAreaCode" column="tel_area_code" jdbcType="VARCHAR"/>
            <result property="tel" column="tel" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="company" column="company" jdbcType="VARCHAR"/>
            <result property="department" column="department" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="qq" column="qq" jdbcType="VARCHAR"/>
            <result property="whatsapp" column="whatsapp" jdbcType="VARCHAR"/>
            <result property="wechat" column="wechat" jdbcType="VARCHAR"/>
            <result property="wechatNickname" column="wechat_nickname" jdbcType="VARCHAR"/>
            <result property="wechatIconUrl" column="wechat_icon_url" jdbcType="VARCHAR"/>
            <result property="isLockFlag" column="is_lock_flag" jdbcType="BIT"/>
            <result property="isDelFlag" column="is_del_flag" jdbcType="BIT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>






</mapper>
