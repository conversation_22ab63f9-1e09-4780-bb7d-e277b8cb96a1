<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.RStudentAgentMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.RStudentAgentEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkStudentId" column="fk_student_id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="isActive" column="is_active" jdbcType="BIT"/>
            <result property="activeDate" column="active_date" jdbcType="TIMESTAMP"/>
            <result property="unactiveDate" column="unactive_date" jdbcType="TIMESTAMP"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
