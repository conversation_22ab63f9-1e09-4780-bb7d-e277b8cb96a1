<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.SystemConfigMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.SystemConfigEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkPlatformId" column="fk_platform_id" jdbcType="BIGINT"/>
            <result property="fkPlatformCode" column="fk_platform_code" jdbcType="VARCHAR"/>
            <result property="configGroup" column="config_group" jdbcType="VARCHAR"/>
            <result property="configKey" column="config_key" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="value1" column="value1" jdbcType="VARCHAR"/>
            <result property="value2" column="value2" jdbcType="VARCHAR"/>
            <result property="value3" column="value3" jdbcType="VARCHAR"/>
            <result property="value4" column="value4" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>
