<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MFilePartnerMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MFilePartnerEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fileGuid" column="file_guid" jdbcType="VARCHAR"/>
            <result property="fileTypeOrc" column="file_type_orc" jdbcType="VARCHAR"/>
            <result property="fileNameOrc" column="file_name_orc" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
            <result property="fileKey" column="file_key" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,file_guid,file_type_orc,
        file_name_orc,file_name,file_path,
        file_key,gmt_create,gmt_create_user,
        gmt_modified,gmt_modified_user
    </sql>


    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.partner.entity.MFilePartnerEntity" useGeneratedKeys="true">
        insert into app_file_center.m_file_partner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileGuid != null">file_guid,</if>
            <if test="fileTypeOrc != null">file_type_orc,</if>
            <if test="fileNameOrc != null">file_name_orc,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileKey != null">file_key,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtCreateUser != null">gmt_create_user,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="gmtModifiedUser != null">gmt_modified_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="fileGuid != null">#{fileGuid,jdbcType=VARCHAR},</if>
            <if test="fileTypeOrc != null">#{fileTypeOrc,jdbcType=VARCHAR},</if>
            <if test="fileNameOrc != null">#{fileNameOrc,jdbcType=VARCHAR},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="filePath != null">#{filePath,jdbcType=VARCHAR},</if>
            <if test="fileKey != null">#{fileKey,jdbcType=VARCHAR},</if>
            <if test="gmtCreate != null">#{gmtCreate,jdbcType=TIMESTAMP},</if>
            <if test="gmtCreateUser != null">#{gmtCreateUser,jdbcType=VARCHAR},</if>
            <if test="gmtModified != null">#{gmtModified,jdbcType=TIMESTAMP},</if>
            <if test="gmtModifiedUser != null">#{gmtModifiedUser,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <insert id="insertAisFileSelective">
        insert into ais_file_center.m_file_sale
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileGuid != null">file_guid,</if>
            <if test="fileTypeOrc != null">file_type_orc,</if>
            <if test="fileNameOrc != null">file_name_orc,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="fileKey != null">file_key,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtCreateUser != null">gmt_create_user,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="gmtModifiedUser != null">gmt_modified_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="fileGuid != null">#{fileGuid,jdbcType=VARCHAR},</if>
            <if test="fileTypeOrc != null">#{fileTypeOrc,jdbcType=VARCHAR},</if>
            <if test="fileNameOrc != null">#{fileNameOrc,jdbcType=VARCHAR},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="filePath != null">#{filePath,jdbcType=VARCHAR},</if>
            <if test="fileKey != null">#{fileKey,jdbcType=VARCHAR},</if>
            <if test="gmtCreate != null">#{gmtCreate,jdbcType=TIMESTAMP},</if>
            <if test="gmtCreateUser != null">#{gmtCreateUser,jdbcType=VARCHAR},</if>
            <if test="gmtModified != null">#{gmtModified,jdbcType=TIMESTAMP},</if>
            <if test="gmtModifiedUser != null">#{gmtModifiedUser,jdbcType=VARCHAR},</if>
        </trim>


    </insert>
    <select id="selectSaleFileOne" resultType="com.partner.entity.MFilePartnerEntity">
        SELECT mFileSale.* FROM ais_file_center.m_file_sale mFileSale WHERE mFileSale.file_guid=#{fkFileGuid} limit 1


    </select>


</mapper>
