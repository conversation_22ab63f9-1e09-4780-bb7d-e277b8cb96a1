<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.partner.mapper.EventMapper">


    <select id="getEnvenListPage"
            resultType="com.partner.vo.EventPageVo">

        SELECT mEvent.id                      AS id,
               mEvent.num                     AS num,
               mEvent.event_time              AS eventTime,
               mEvent.event_time_end          AS eventTimeEnd,
               mEvent.event_theme             AS eventTheme,
               mEvent.fk_area_country_id_hold AS fkAreaCountryIdHold,
               mEvent.status,
               uAreaCountry.name_chn               AS areaCountryName,
               mEvent.fk_area_state_id_hold   AS fkAreaStateIdHold,
               uAreaState.name_chn               AS areaStateName,
               mEvent.fk_area_city_id_hold    AS fkAreaCityIdHold,
               uAreaCity.name_chn            AS areaCityName,
               mEvent.remark                  AS remark,
               (SELECT `comment` from ais_sale_center.s_comment sComment
                                 WHERE fk_table_name='m_event' and fk_table_id=mEvent.id limit 1) AS comment,
               (SELECT  CONCAT(#{query.mMageAddress}, partnerFile.file_key) FROM app_partner_center.s_media_and_attached sAttached
                                                                                     INNER JOIN app_file_center.m_file_partner partnerFile ON partnerFile.file_guid = sAttached.fk_file_guid
                WHERE sAttached.fk_table_name='m_event'  AND  sAttached.type_key='m_event_partner_logo'
                  AND sAttached.fk_table_id=mEvent.id limit 1
               ) AS file_key,
               (
                   SELECT id FROM ais_sale_center.m_event_registration_agent mEventRegistrationAgent
                             WHERE mEventRegistrationAgent.fk_event_id=mEvent.id AND mEventRegistrationAgent.fk_partner_user_id=#{query.partnerUserId}
                             AND mEventRegistrationAgent.status=1
                             limit 1
               ) AS mEventRegistrationId
                ,
               (
                   SELECT count(*) FROM ais_sale_center.m_event_registration_agent mEventRegistrationAgent
                   WHERE mEventRegistrationAgent.fk_event_id=mEvent.id AND mEventRegistrationAgent.status=1
               ) AS regisPeopleCount,
               uEventType.type_name          AS typeName

        FROM ais_sale_center.m_event mEvent
                 LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON uAreaCountry.id = mEvent.fk_area_country_id_hold
                 LEFT JOIN ais_institution_center.u_area_state uAreaState ON uAreaState.id = mEvent.fk_area_state_id_hold
                 LEFT JOIN ais_institution_center.u_area_city uAreaCity ON uAreaCity.id = mEvent.fk_area_city_id_hold
                 LEFT JOIN ais_sale_center.u_event_type uEventType ON uEventType.id = mEvent.fk_event_type_id
        WHERE mEvent.is_active = 1 AND
            FIND_IN_SET(13, mEvent.public_level)
        ORDER BY mEvent.gmt_create DESC



    </select>
    <select id="searchRegistration" resultType="com.partner.vo.MEventRegistrationAgentVo">

        SELECT mEvent.*,
               mAgent.name  AS agentName,
               mPartnerUser.name AS partnerUserName,
               mEventRegistrationAgent.fk_agent_id,
               mEventRegistrationAgent.fk_partner_user_id,
               mEventRegistrationAgent.status

        FROM ais_sale_center.m_event mEvent
                 INNER JOIN ais_sale_center.m_event_registration_agent mEventRegistrationAgent ON mEvent.id=mEventRegistrationAgent.fk_event_id
                 INNER JOIN ais_sale_center.m_agent mAgent ON mAgent.id=mEventRegistrationAgent.fk_agent_id
                 INNER JOIN app_partner_center.m_partner_user mPartnerUser  ON mPartnerUser.id=mEventRegistrationAgent.fk_partner_user_id
        WHERE mEvent.id=#{query.id}



    </select>



    <select id="getUserInfo" resultType="com.partner.vo.AppointmentInfo">
        SELECT
            mPartnerUser.name AS userNameChn,
            appSystemCenter.wechat_icon_url AS fileKey
        FROM ais_sale_center.m_event_registration_agent mEventRegistrationAgent
                 INNER JOIN app_partner_center.m_partner_user mPartnerUser ON mEventRegistrationAgent.fk_partner_user_id=mPartnerUser.id
                 INNER JOIN app_system_center.system_user appSystemCenter  ON appSystemCenter.id=mPartnerUser.fk_user_id
        WHERE mEventRegistrationAgent.fk_event_id=#{id} and mEventRegistrationAgent.status=1 order by mEventRegistrationAgent.gmt_create DESC
        LIMIT 8

    </select>
























    <select id="getTableName" resultType="java.util.Map">

        ${tableName}
    </select>



</mapper>