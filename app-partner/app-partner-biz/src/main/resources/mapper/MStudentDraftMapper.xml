<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MStudentDraftMapper">



    <select id="getPartnerStudents" resultType="com.partner.vo.student.MStudentDraftVo">

        SELECT
            mAppStudent.id,
            mAppStudent.fk_platform_id,
            mAppStudent.id AS fk_student_uuid,
            mAppStudent.name AS studentName,
            mAppStudent.gender,
            mAppStudent.birthday,
            mAppStudent.mobile_area_code,
            mAppStudent.mobile,
            mAppStudent.status,
            uAreaCountry.name_chn AS areaCountryName
        FROM  ais_sale_center.m_app_student mAppStudent
                 LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON mAppStudent.fk_area_country_id_nationality = uAreaCountry.id

            <where>
                mAppStudent.fk_platform_id=#{query.platformId}
                <if test="query.searchType!=null and query.searchType==-1">
                    AND mAppStudent.status=-1
                </if>
                <if test="query.searchType!=null and query.searchType==0">
                    AND mAppStudent.status=0
                </if>
                <if test="query.searchType!=null and query.searchType==1">
                    AND mAppStudent.status=1
                </if>
                <if test="query.partnerUserId!=null ">
                    AND mAppStudent.fk_platform_create_user_id=#{query.partnerUserId}
                </if>



            </where>
        ORDER BY mAppStudent.gmt_create DESC



    </select>
    <select id="getCheckStudentsPage" resultType="com.partner.vo.student.MStudentDraftVo">

        SELECT
               mAppStudent.id,
               mAppStudent.name AS studentName,
               mAppStudent.status,
               mAppStudentCheck.check_comment,
               mAppStudentCheck.gmt_modified AS checkReadTime,
               rStudentUuid.fk_student_uuid,
               mAppStudentCheck.gmt_create
               FROM  ais_sale_center.m_app_student mAppStudent
                 INNER JOIN  ais_sale_center.m_app_student_check mAppStudentCheck ON  mAppStudent.id=mAppStudentCheck.fk_app_student_id
                 LEFT  JOIN  ais_sale_center.r_student_uuid rStudentUuid ON mAppStudent.fk_student_id=rStudentUuid.fk_student_id
                 WHERE
                    mAppStudent.fk_platform_id=#{query.platformId} AND
                    mAppStudent.fk_platform_create_user_id=#{query.partnerUserId}
                    and mAppStudent.status in (-1,2)
        ORDER BY mAppStudentCheck.gmt_create DESC


    </select>
    <select id="getReadingCheck">
        UPDATE  ais_sale_center.m_app_student_check set gmt_modified=now() WHERE gmt_modified IS NULL
        AND  fk_app_student_id IN (
                SELECT id FROM (
        SELECT id FROM ais_sale_center.m_app_student
           WHERE fk_platform_create_user_id=#{partnerUserId} ORDER BY gmt_create DESC LIMIT 100
                                          ) AS tmpids
                                                                                      )

    </select>
    <select id="getNoReadingNum" resultType="java.lang.Integer">
        SELECT count(*) FROM ais_sale_center.m_app_student mAppStudent
            INNER JOIN ais_sale_center.m_app_student_check mAppStudentCheck ON mAppStudent.id=mAppStudentCheck.fk_app_student_id
                        WHERE mAppStudent.status in (-1,2) and fk_platform_create_user_id=#{partnerUserId} AND mAppStudentCheck.gmt_modified IS NULL
    </select>


</mapper>