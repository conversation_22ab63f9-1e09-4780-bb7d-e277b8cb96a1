<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MFilePlatformMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MFilePlatformEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fileGuid" column="file_guid" jdbcType="VARCHAR"/>
            <result property="fileTypeOrc" column="file_type_orc" jdbcType="VARCHAR"/>
            <result property="fileNameOrc" column="file_name_orc" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
            <result property="fileKey" column="file_key" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>
