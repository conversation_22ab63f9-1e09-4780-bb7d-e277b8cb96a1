<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MInstitutionMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MInstitutionEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkInstitutionTypeId" column="fk_institution_type_id" jdbcType="BIGINT"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkAreaStateId" column="fk_area_state_id" jdbcType="BIGINT"/>
            <result property="fkAreaCityId" column="fk_area_city_id" jdbcType="BIGINT"/>
            <result property="fkCurrencyTypeNum" column="fk_currency_type_num" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nameChn" column="name_chn" jdbcType="VARCHAR"/>
            <result property="nameDisplay" column="name_display" jdbcType="VARCHAR"/>
            <result property="shortName" column="short_name" jdbcType="VARCHAR"/>
            <result property="shortNameChn" column="short_name_chn" jdbcType="VARCHAR"/>
            <result property="nature" column="nature" jdbcType="VARCHAR"/>
            <result property="establishedDate" column="established_date" jdbcType="VARCHAR"/>
            <result property="applyDate" column="apply_date" jdbcType="VARCHAR"/>
            <result property="applyFeeMin" column="apply_fee_min" jdbcType="DECIMAL"/>
            <result property="applyFeeMax" column="apply_fee_max" jdbcType="DECIMAL"/>
            <result property="applyFeeRef" column="apply_fee_ref" jdbcType="DECIMAL"/>
            <result property="applyFeeCny" column="apply_fee_cny" jdbcType="DECIMAL"/>
            <result property="website" column="website" jdbcType="VARCHAR"/>
            <result property="zipCode" column="zip_code" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="detail" column="detail" jdbcType="VARCHAR"/>
            <result property="mapXyGg" column="map_xy_gg" jdbcType="VARCHAR"/>
            <result property="mapXyBd" column="map_xy_bd" jdbcType="VARCHAR"/>
            <result property="rankingType" column="ranking_type" jdbcType="VARCHAR"/>
            <result property="isIncludingNonEnglish" column="is_including_non_english" jdbcType="BIT"/>
            <result property="isKpi" column="is_kpi" jdbcType="BIT"/>
            <result property="kpiLevel" column="kpi_level" jdbcType="INTEGER"/>
            <result property="isActive" column="is_active" jdbcType="BIT"/>
            <result property="publicLevel" column="public_level" jdbcType="VARCHAR"/>
            <result property="dataLevel" column="data_level" jdbcType="INTEGER"/>
            <result property="idGea" column="id_gea" jdbcType="VARCHAR"/>
            <result property="idIae" column="id_iae" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>



</mapper>
