package com.partner.exception;

import com.alibaba.csp.sentinel.Tracer;
import com.common.core.util.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;


@Order(1)
@RestControllerAdvice
public class GlobalExceptionConfig   {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionConfig.class);

    @ExceptionHandler({Exception.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R handleGlobalException(Exception e) {
        log.error("全局异常信息 ex={}", e.getMessage(), e);
        Tracer.trace(e);
        return R.failed(e.getLocalizedMessage());
    }

    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseStatus(HttpStatus.OK)
    public R handleIllegalArgumentException(IllegalArgumentException exception) {
        log.error("非法参数,ex = {}", exception.getMessage(), exception);
        return R.failed(exception.getMessage());
    }

    @ExceptionHandler({AccessDeniedException.class})
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public R handleAccessDeniedException(AccessDeniedException e) {
        String msg = SpringSecurityMessageSource.getAccessor().getMessage("AbstractAccessDecisionManager.accessDenied", e.getMessage());
        log.warn("拒绝授权异常信息 ex={}", msg);
        return R.failed(msg);
    }

    @ExceptionHandler({MethodArgumentNotValidException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleBodyValidException(MethodArgumentNotValidException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.warn("参数绑定异常,ex = {}", ((FieldError)fieldErrors.get(0)).getDefaultMessage());
       // return R.failed(String.format("%s %s", ((FieldError)fieldErrors.get(0)).getField(), ((FieldError)fieldErrors.get(0)).getDefaultMessage()));
        return R.failed(String.format("%s", ((FieldError)fieldErrors.get(0)).getDefaultMessage()));
    }

    @ExceptionHandler({BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R bindExceptionHandler(BindException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.warn("参数绑定异常,ex = {}", ((FieldError)fieldErrors.get(0)).getDefaultMessage());
        return R.failed(((FieldError)fieldErrors.get(0)).getDefaultMessage());
    }



    @ExceptionHandler(PartnerExceptionInfo.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R exception(PartnerExceptionInfo e) {
        log.error("全局异常信息 PartnerExceptionInfo={}", e.getMessage(), e);
        Tracer.trace(e);
        return R.restResult(null,e.getErrorCode(),e.getErrorMessage());
    }




}
