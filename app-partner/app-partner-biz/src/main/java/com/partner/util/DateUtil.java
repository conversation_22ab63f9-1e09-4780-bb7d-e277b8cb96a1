package com.partner.util;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/3/31
 * @Version 1.0
 * @apiNote:
 */
public class DateUtil {

    public static Date getFirstDayOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        if (year != null) {
            calendar.set(Calendar.YEAR, year);
        }
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定年份的最后一天 23:59:59
     *
     * @param year 指定年份，为 null 时使用当前年份
     * @return Date
     */
    public static Date getLastDayOfYear(Integer year) {
        Calendar calendar = Calendar.getInstance();
        if (year != null) {
            calendar.set(Calendar.YEAR, year);
        }
        calendar.set(Calendar.MONTH, Calendar.DECEMBER);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取指定年份，如果为 null 则返回当前年份
     *
     * @param year 指定年份，可以为 null
     * @return 最终使用的年份
     */
    public static int getYearOrCurrentYear(Integer year) {
        if (Objects.nonNull(year)) {
            return year;
        }
        return Calendar.getInstance().get(Calendar.YEAR);
    }

}
