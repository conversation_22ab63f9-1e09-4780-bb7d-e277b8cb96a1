package com.partner.util;

import com.alibaba.fastjson.JSONObject;
import com.apps.api.dto.base.BaseUserInfoDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.MPartnerUserEntity;
import com.partner.mapper.MPartnerUserMapper;
import com.partner.service.RPartnerUserAreaCountryService;
import com.partner.vo.agent.PartnerAgent;
import com.partner.vo.base.PartnerUserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author:Oliver
 * @Date: 2025/1/16  15:38
 * @Version 1.0
 */
@Slf4j
@Component
public class UserInfoParamsUtils {

    private static RedisTemplate<String, Object> redisTemplate;

    private static RPartnerUserAreaCountryService partnerUserAreaCountryService;
    @Autowired
    private ApplicationContext applicationContext;
    private static MPartnerUserMapper partnerUserMapper;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        UserInfoParamsUtils.redisTemplate = redisTemplate;
    }

    @PostConstruct
    public void init() {
        partnerUserAreaCountryService = applicationContext.getBean(RPartnerUserAreaCountryService.class);
        partnerUserMapper = applicationContext.getBean(MPartnerUserMapper.class);
    }

    public static UserInfoParams getUserInfoParams(Long tenantId, String platformCode, Long loginUserId) {

        // 获取当前请求的 uuid 参数
        String uuid = RequestHeaderUtils.getUuidOrThrow(loginUserId);

//        String redisKey = tenantId + ":" + platformCode + ":" + loginUserId + "-" + uuid;
        String redisKey = RedisKeyUtils.buildUserInfoKey(tenantId, platformCode, loginUserId, uuid);
        Object cachedData = redisTemplate.opsForValue().get(redisKey);

        //用户缓存没有partner用户时为true
        Boolean userflag = false;
        if (!Objects.isNull(cachedData)) {
            UserInfoParams cacheUser;
            if (cachedData instanceof String) {
                cacheUser = JSONObject.parseObject((String) cachedData, UserInfoParams.class);
            } else {
                cacheUser = (UserInfoParams) cachedData;
            }
            if (Objects.isNull(cacheUser.getPartnerUserId()) || Objects.isNull(cacheUser.getAgentId()) || cacheUser.getAgentId() < 1L) {
                userflag = true;
            }
        }

        if (Objects.isNull(cachedData) || userflag) {
            //重新查询一遍
            //缓存已保存的用户和代理的管理信息
//            Object obj = redisTemplate.opsForValue().get(RedisConstant.PARTNER_USERINFO_AGENT_KEY_PREFIX + loginUserId + "-" + uuid);
            Object obj = redisTemplate.opsForValue().get(RedisKeyUtils.buildPartnerAgentKey(loginUserId, uuid));

            PartnerAgent partnerAgent = JSONObject.parseObject((String) obj, PartnerAgent.class);
            if (Objects.isNull(partnerAgent) || Objects.isNull(partnerAgent.getAgentId())) {
                MPartnerUserEntity partnerUser = partnerUserMapper.selectOne(new LambdaQueryWrapper<MPartnerUserEntity>()
                        .eq(MPartnerUserEntity::getFkUserId, loginUserId)
                        .orderByDesc(MPartnerUserEntity::getId), false);
                if (Objects.isNull(partnerUser) || Objects.isNull(partnerUser.getFkAgentId())) {
                    throw new RuntimeException("获取用户缓存信息失败");
                }
                partnerAgent = PartnerAgent.builder()
                        .agentId(partnerUser.getFkAgentId())
                        .partnerUserId(partnerUser.getId())
                        .systemUserId(partnerUser.getFkUserId()).build();
                //保存用户和代理的管理信息
                redisTemplate.opsForValue().set(RedisKeyUtils.buildPartnerAgentKey(loginUserId, uuid), JSONObject.toJSONString(partnerAgent));
//                redisTemplate.opsForValue().set(RedisConstant.PARTNER_USERINFO_AGENT_KEY_PREFIX + loginUserId + "-" + uuid, JSONObject.toJSONString(partnerAgent));
            }
            FzhUser user = SecurityUtils.getUser();
            BaseUserInfoDto userInfoDto = BaseUserInfoDto.builder()
                    .platformId(Long.parseLong(user.getFkFromPlatformId()))
                    .userId(loginUserId)
                    .tenantId(tenantId)
                    .roleCode(user.getRoleCode())
                    .agentId(partnerAgent.getAgentId())
                    .build();
            log.info("查询用户业务缓存信息:{}", JSONObject.toJSONString(userInfoDto));
            PartnerUserInfoVo partnerInfo = partnerUserAreaCountryService.getPartnerInfo(userInfoDto);
            if (Objects.isNull(partnerInfo)) {
                throw new RuntimeException("获取用户缓存信息失败");
            }
            UserInfoParams userInfoParams = new UserInfoParams();
            BeanUtils.copyProperties(partnerInfo, userInfoParams);
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(userInfoParams), 1, TimeUnit.DAYS);
            return userInfoParams;
        }
        if (cachedData instanceof String) {
            return JSONObject.parseObject((String) cachedData, UserInfoParams.class);
        }
        return (UserInfoParams) cachedData;

    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 删除缓存内容
     *
     * @param
     * @return
     */
    public static Boolean deleteUserInfoParams(Long tenantId, String platformCode, Long loginUserId) {
        String redisKey = tenantId + ":" + platformCode + ":" + loginUserId;
        redisTemplate.opsForValue().getOperations().delete(redisKey);
        return true;
    }


    public static BigDecimal getExchangeRate(String key, String formCurrency, String toCurrency) {
        BigDecimal exchangeRate = new BigDecimal(1);
        String redisKey = key + ":" + formCurrency + ":" + toCurrency;
        Object cachedData = redisTemplate.opsForValue().get(redisKey);
        if (Objects.nonNull(cachedData)) {
            if (cachedData instanceof BigDecimal) {
                exchangeRate = (BigDecimal) cachedData;
            }
        }
        return exchangeRate;
    }


    public static void setExchangeRate(String key, String formCurrency, String toCurrency, BigDecimal exchangeRate) {
        String redisKey = key + ":" + formCurrency + ":" + toCurrency;
        Object cachedData = redisTemplate.opsForValue().get(redisKey);
        if (Objects.isNull(cachedData)) {
            redisTemplate.opsForValue().set(redisKey, exchangeRate);
            redisTemplate.expire(redisKey, 24, TimeUnit.HOURS);
            //redisTemplate.expire(redisKey, 60, TimeUnit.SECONDS);
        }
    }

    /**
     * 获取当前代理id
     *
     * @return
     */
    public static Long getCurrentAgentId() {
        UserInfoParams userInfoParams = getUserInfoParams(Long.valueOf(SecurityUtils.getUser().getFkTenantId()),
                SecurityUtils.getUser().getFkFromPlatformCode(),
                SecurityUtils.getUser().getId());
        return userInfoParams.getAgentId();
    }

    /**
     * 获取当前partner用户id
     *
     * @return
     */
    public static Long getCurrentPartnerUserId() {
        UserInfoParams userInfoParams = getUserInfoParams(Long.valueOf(SecurityUtils.getUser().getFkTenantId()),
                SecurityUtils.getUser().getFkFromPlatformCode(),
                SecurityUtils.getUser().getId());
        return userInfoParams.getPartnerUserId();
    }

    /**
     * 获取当前公司id
     *
     * @return
     */
    public static Long getCurrentCompanyId() {
        UserInfoParams userInfoParams = getUserInfoParams(Long.valueOf(SecurityUtils.getUser().getFkTenantId()),
                SecurityUtils.getUser().getFkFromPlatformCode(),
                SecurityUtils.getUser().getId());
        return userInfoParams.getCompanyId();
    }

}
