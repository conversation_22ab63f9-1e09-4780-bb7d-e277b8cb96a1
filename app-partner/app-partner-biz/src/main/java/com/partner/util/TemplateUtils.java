package com.partner.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;

@Slf4j
@Component
public class TemplateUtils {

    public static FileInputStream getdownLoad(String fileName, String sourcePat) throws IOException {
        //读取模板
        InputStream is = TemplateUtils.class.getClassLoader().getResourceAsStream("template/" + fileName);
        FileInputStream fileis=convertToTempFileInputStream(is);
        return fileis;
    }
    public static FileInputStream convertToTempFileInputStream(InputStream inputStream) throws IOException {
        // 创建一个临时文件
        File tempFile = File.createTempFile("temp", null);
        tempFile.deleteOnExit(); // 确保 JVM 退出时删除临时文件

        // 将 InputStream 的内容复制到临时文件中
        try (OutputStream outStream = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, bytesRead);
            }
        }

        // 返回指向临时文件的 FileInputStream
        return new FileInputStream(tempFile);
    }

}
