package com.partner.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.TeamDataSumDto;
import com.partner.dto.finance.*;
import com.partner.service.FinanceService;
import com.partner.service.MAgentContractAccountService;
import com.partner.dto.finance.AmountConvetParamsDto;
import com.partner.vo.TeamMemberVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(description = "finance" , name = "小程序-佣金" )
@RestController
@RequestMapping("/finance")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class FinanceController {

    private final FinanceService financeService;

    private final MAgentContractAccountService magentContractAccountService;

    @Operation(summary = "佣金-确认名单列表" , description = "佣金-确认名单列表" )
    @SysLog("佣金-确认名单列表" )
    @GetMapping("/getCommissionPage" )
    public R getCommissionAffirmPage(Page page, @ParameterObject @Valid CommissionSearchParamsDto dto) {

        return R.ok(financeService.getCommissionAffirmPage(page,dto));

    }
    @Operation(summary = "佣金-确认" , description = "佣金-确认" )
    @SysLog("佣金-确认" )
    @PostMapping("/affirmOfferItem" )
    public R affirmOfferItem(@RequestBody @Validated CommissionAffirmParamsDto paramsDto){

        financeService.affirmOfferItem(paramsDto);
        return R.ok();
    }

    @Operation(summary = "佣金-可结算信息" , description = "佣金-可结算信息" )
    @SysLog("佣金-可结算信息" )
    @GetMapping("/getAgentSettlementPage" )
    public R getAgentSettlementPage(@ParameterObject @Valid SettlementSearchParamsDto dto) {
        return R.ok(financeService.getAgentSettlementPage(dto));

    }


    @Operation(summary = "顾问合同-绑定账户列表数据" , description = "顾问合同-绑定账户列表数据" )
    @SysLog("顾问合同-绑定账户列表数据" )
    @GetMapping("/getAgentContractAccount" )
    public R getAgentContractAccount(){
        return R.ok(magentContractAccountService.getAgentContractAccount());
    }

    @Operation(summary = "佣金-人民币(默认币种)转目标币种金额" , description = "佣金-人民币转目标币种金额" )
    @GetMapping("/getAmountConvet" )
    public R getAmountConvet(@ParameterObject @Valid AmountConvetParamsDto dto){

        return R.ok(financeService.getAmountConvet(dto));
    }

    @Operation(summary = "佣金-结算校验" , description = "佣金-结算校验" )
    @SysLog("佣金-结算校验" )
    @GetMapping("/getValidSettlement" )
    public R getValidSettlement(){

        return R.ok(financeService.getValidSettlement());
    }


    @Operation(summary = "佣金-提交结算申请" , description = "佣金-提交结算申请" )
    @SysLog("佣金-提交结算申请" )
    @PostMapping("/agentConfirmSettlement" )
    public R agentConfirmSettlement(@RequestBody @Validated ConfirmSettlementDto confirmDto){

        return R.ok(financeService.agentConfirmSettlement(confirmDto));
    }
    @PostMapping("/testDSsourceshiwu" )
    public R testDSsourceshiwu(@RequestBody TeamMemberVo params){
        financeService.testDSsourceshiwu(params);
        return R.ok();
    }





}
