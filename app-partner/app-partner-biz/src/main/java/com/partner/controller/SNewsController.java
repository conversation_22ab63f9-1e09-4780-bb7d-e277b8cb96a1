package com.partner.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.security.annotation.Inner;
import com.partner.dto.SNewsParamsDto;
import com.partner.entity.SNewsEntity;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.service.SNewsService;
import com.pig4cloud.plugin.excel.annotation.ResponseExcel;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * partner
 *
 * <AUTHOR>
 * @date 2024-11-22 10:42:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/sNews" )
@Tag(description = "sNews" , name = "小程序-资讯管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Inner(value = false)
public class SNewsController {

    private final  SNewsService sNewsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param dto partner
     * @return
     */
    @Operation(summary = "资讯查询" , description = "资讯查询" )
    @GetMapping("/getSNewsListPage" )
    public R getSNewsListPage( Page page, @ParameterObject SNewsParamsDto dto) {
        return R.ok(sNewsService.getSNewsListPage(page, dto));
    }


    /**
     * 通过id查询partner
     * @param id id
     * @return R
     */
    @Operation(summary = "资讯详情" , description = "序资讯详情" )
    @GetMapping("/getByDetail/{id}" )
    public R getByDetail(@PathVariable("id" ) Long id,@Param("fkPartnerUserId")   Long fkPartnerUserId
            ,@Param("clientId")   String clientId) {
        return R.ok(sNewsService.getByDetail(id,   fkPartnerUserId));
    }
    @Operation(summary = "国家下拉框" , description = "国家下拉框" )
    @GetMapping("/getCountryCombox" )
    public R getCountryCombox(@ParameterObject SNewsParamsDto dto){
        return R.ok(sNewsService.getCountryCombox(dto));
    }

    @Operation(summary = "学校下拉框" , description = "学校下拉框" )
    @GetMapping("/getInstitutionCombox" )
    public R getInstitutionCombox(@ParameterObject SNewsParamsDto dto){
        return R.ok(sNewsService.getInstitutionCombox(dto));
    }



}