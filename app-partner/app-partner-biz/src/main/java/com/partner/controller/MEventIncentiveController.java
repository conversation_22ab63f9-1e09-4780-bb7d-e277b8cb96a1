package com.partner.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.MEventIncentiveParamsDto;
import com.partner.service.MEventIncentiveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(description = "mEventIncentive" , name = "小程序-奖励政策" )
@RestController
@RequestMapping("/mEventIncentive")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MEventIncentiveController {


    private final MEventIncentiveService mEventIncentiveService;

    @Operation(summary = "奖励政策分页查询" , description = "奖励政策分页查询" )
    @SysLog("奖励政策分页查询" )
    @GetMapping("/getMEventIncentivePage" )
    public R getMEventIncentivePage(Page page, @ParameterObject MEventIncentiveParamsDto dto) {
        return R.ok(mEventIncentiveService.getMEventIncentivePage(page, dto));
    }
    @Operation(summary = "奖励政策-国家下拉框" , description = "奖励政策-国家下拉框" )
    @SysLog("奖励政策-国家下拉框" )
    @GetMapping("/getCountryCombox" )
    public R getCountryCombox(@ParameterObject MEventIncentiveParamsDto dto){
        return R.ok(mEventIncentiveService.getCountryCombox(dto));
    }


    @Operation(summary = "奖励政策-详细信息" , description = "奖励政策-详细信息" )
    @SysLog("奖励政策-详细信息" )
    @GetMapping("/getMeventDetail/{id}" )
    public R getIncentiveDetail(@PathVariable("id" ) Long eventIncentiveId){
        return R.ok(mEventIncentiveService.getIncentiveDetail(eventIncentiveId));
    }


}
