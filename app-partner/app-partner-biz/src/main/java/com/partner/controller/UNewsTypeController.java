package com.partner.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.entity.UNewsTypeEntity;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.service.UNewsTypeService;
import com.pig4cloud.plugin.excel.annotation.ResponseExcel;
import org.springframework.security.access.prepost.PreAuthorize;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * partner
 *
 * <AUTHOR>
 * @date 2024-11-22 10:48:25
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/uNewsType" )
@Tag(description = "uNewsType" , name = "新闻类型" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class UNewsTypeController {

    private final  UNewsTypeService uNewsTypeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param uNewsType partner
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    public R getUNewsTypePage(@ParameterObject Page page, @ParameterObject UNewsTypeEntity uNewsType) {
        LambdaQueryWrapper<UNewsTypeEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(uNewsTypeService.page(page, wrapper));
    }


    /**
     * 通过id查询partner
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(uNewsTypeService.getById(id));
    }

    /**
     * 新增partner
     * @param uNewsType partner
     * @return R
     */
    @Operation(summary = "新增partner" , description = "新增partner" )
    @SysLog("新增partner" )
    @PostMapping
    public R save(@RequestBody UNewsTypeEntity uNewsType) {
        return R.ok(uNewsTypeService.save(uNewsType));
    }

    /**
     * 修改partner
     * @param uNewsType partner
     * @return R
     */
    @Operation(summary = "修改partner" , description = "修改partner" )
    @SysLog("修改partner" )
    @PutMapping
    public R updateById(@RequestBody UNewsTypeEntity uNewsType) {
        return R.ok(uNewsTypeService.updateById(uNewsType));
    }

    /**
     * 通过id删除partner
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除partner" , description = "通过id删除partner" )
    @SysLog("通过id删除partner" )
    @DeleteMapping
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(uNewsTypeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param uNewsType 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    public List<UNewsTypeEntity> export(UNewsTypeEntity uNewsType,Long[] ids) {
        return uNewsTypeService.list(Wrappers.lambdaQuery(uNewsType).in(ArrayUtil.isNotEmpty(ids), UNewsTypeEntity::getId, ids));
    }
}