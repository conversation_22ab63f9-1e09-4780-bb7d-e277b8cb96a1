package com.partner.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.MLiveParamsDto;
import com.partner.service.MLiveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(description = "mLive" , name = "小程序-热门培训-未登录调用接口" )
@RestController
@RequestMapping("/mLive")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
@Inner(value = false)
public class MLiveController {

    private final MLiveService mLiveService;
    /**
     * 分页查询
     * @param page 分页对象
     * @param dto live
     * @return
     */
    @Operation(summary = "直播分页查询" , description = "最新直播-往期培训-培训日历" )
    @SysLog("直播分页查询" )
    @GetMapping("/getMLiveListPage" )
    public R getMLiveListPage(Page page, @ParameterObject @Valid MLiveParamsDto dto) {
        return R.ok(mLiveService.getMLiveListPage(page, dto));
    }



    @Operation(summary = "首页最新直播查询" , description = "首页最新直播查询" )
    @SysLog("首页最新直播查询" )
    @GetMapping("/getHomeMLiveList" )
    public R getHomeMLiveList() {
        String loginType="0";//已登录
        return R.ok(mLiveService.getHomeMLiveList(loginType));
    }



}
