package com.partner.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.finance.DownloadMSettlementBillParamsDto;
import com.partner.dto.finance.MSettlementBillParamsDto;
import com.partner.service.MSettlementBillDownloadService;
import com.partner.service.MSettlementBillPdfService;
import com.partner.service.MSettlementBillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


@Tag(description = "mSettlementBill" , name = "小程序-对账单查询" )
@RestController
@RequestMapping("/mSettlementBill")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MSettlementBillController {

    private final MSettlementBillService mSettlementBillService;
    private final MSettlementBillPdfService mSettlementBillPdfService;

    private final MSettlementBillDownloadService mSettlementBillDownloadService;


    @Operation(summary = "佣金-对账单查询" , description = "佣金-对账单查询" )
    @SysLog("佣金-对账单查询")
    @GetMapping("/getMSettlementBill")
    public R getMSettlementBillPage(Page page, @ParameterObject  MSettlementBillParamsDto dto) {
        return R.ok(mSettlementBillService.getMSettlementBillPage(page,dto));
    }

    @Operation(summary = "佣金-对账单明细" , description = "佣金-对账单明细" )
    @SysLog("佣金-对账单明细")
    @GetMapping("/getMSettlementBillDetail/{msettlementId}")
    public R getMSettlementBillDetail(@PathVariable("msettlementId")    Long msettlementId) {
        return R.ok(mSettlementBillService.getMSettlementBillDetail(msettlementId));
    }



    @Operation(summary = "下载对账单WORD", description = "下载对账单")
    @PostMapping("downloadSettlementBill")
    public void downloadSettlementBill(HttpServletResponse response, @RequestBody MSettlementBillParamsDto dto) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        mSettlementBillService.downloadSettlementBill(dto,response);

    }

    @Operation(summary = "下载对账单PDF", description = "下载对账单")
    @PostMapping("downloadSettlementBillPdf")
    public void downloadSettlementBillPdf(HttpServletResponse response, @RequestBody @Valid MSettlementBillParamsDto dto) {
        //设置文件路径
        mSettlementBillPdfService.downloadSettlementBillPdf(dto,response);
    }


    @Operation(summary = "佣金-生成PDF对账单文件" , description = "佣金-生成PDF对账单文件" )
    @SysLog("佣金-生成PDF对账单文件" )
    @PostMapping("/createSettlementPdf" )
    public R createSettlementPdf(@RequestBody @Validated MSettlementBillParamsDto dto){
        mSettlementBillPdfService.createSettlementPdf(dto);
        return R.ok();
    }



    @Operation(summary = "佣金-批量下载对账单" , description = "佣金-批量下载对账单" )
    @PostMapping("/dowanloadMSettlementBill")
    public void dowanloadMSettlementBill(HttpServletResponse response,@RequestBody DownloadMSettlementBillParamsDto params) {
        mSettlementBillDownloadService.dowanloadMSettlementBill(response,params);
    }

    @Operation(summary = "佣金-批量下载对账单-桶地址" , description = "佣金-批量下载对账单-桶地址" )
    @PostMapping("/dowanloadMSettlementBillCloud")
    public R dowanloadMSettlementBillCloud(HttpServletResponse response,@RequestBody DownloadMSettlementBillParamsDto params) {


        return R.ok(mSettlementBillDownloadService.dowanloadMSettlementBillCloud(params));
    }



}
