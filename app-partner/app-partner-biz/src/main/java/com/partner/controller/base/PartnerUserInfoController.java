package com.partner.controller.base;

import com.apps.api.dto.base.BaseUserInfoDto;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.service.RPartnerUserAreaCountryService;
import com.partner.util.UserInfoParamsUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(description = "partnerUserInfo" , name = "小程序-获取登录用户的partner用户信息" )
@RestController
@RequestMapping("/partnerUserInfo")
//@Inner(value = false)
@RequiredArgsConstructor
public class PartnerUserInfoController {
    private final RPartnerUserAreaCountryService rPartnerUserAreaCountryService;

    @Operation(summary = "partner-获取用户信息对象" , description = "partner-获取用户信息对象" )
    @SysLog("partner-获取用户信息对象" )
    @Inner(value = false)
    @PostMapping("/getPartnerInfo" )
    public R getPartnerInfo(@RequestBody @Validated BaseUserInfoDto paramsDto){
        return R.ok(rPartnerUserAreaCountryService.getPartnerInfo(paramsDto));
    }

}
