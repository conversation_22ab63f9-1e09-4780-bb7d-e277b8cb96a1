package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.ReadingSignDto;
import com.partner.service.ReadingSignService;
import com.partner.vo.ReadingSignNumVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

@Tag(description = "readingSign" , name = "小程序-存在未阅读数据" )
@RestController
@RequestMapping("/readingSign")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class ReadingSignController {

    private final ReadingSignService readingSignService;

    @Operation(summary = "小程序-存在未阅读数据" , description = "小程序-存在未阅读数据" )
    @GetMapping("/getNoReadingType" )
    public R getNoReadingType(){
        ReadingSignNumVo readingSignNumVo= readingSignService.getNoReadingType();
        return R.ok(readingSignNumVo);
    }

    @Operation(summary = "小程序-阅读亮标数据" , description = "小程序-阅读亮标数据" )
    @PostMapping("/readingSingn" )
    public R readingSingn(@RequestBody ReadingSignDto params){
        readingSignService.readingSingn(params);
        return R.ok();
    }

}
