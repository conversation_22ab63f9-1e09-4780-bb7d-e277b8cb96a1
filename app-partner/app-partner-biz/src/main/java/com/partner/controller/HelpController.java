package com.partner.controller;


import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.service.MHelpService;
import com.partner.vo.HelpVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(description = "help" , name = "小程序-帮助信息管理" )
@RestController
@RequestMapping("/help")
@RequiredArgsConstructor
public class HelpController {
    private final MHelpService helpService;


    @Operation(summary = "帮助-帮助详情接口" , description = "帮助-帮助详情接口" )
    @SysLog("帮助-帮助详情接口" )
    @Inner(value = false)
    @GetMapping("/getDetail")
    public R getDetail() {
        List<HelpVo> data = helpService.getDetail();
        return R.ok(data);
    }

}
