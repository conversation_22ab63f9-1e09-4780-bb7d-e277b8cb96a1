//package com.partner.controller;
//
//
//import com.common.core.util.R;
//import com.partner.service.AttachService;
//import com.partner.vo.attach.AttachVo;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.validation.constraints.NotNull;
//import java.util.List;
//
//@Tag(description = "附件管理", name = "附件管理")
//@RestController
//@RequestMapping("/attach")
//@RequiredArgsConstructor
//public class AttachController {
//
//    private final AttachService attachService;
//
//    @Operation(summary = "上传附件-公开桶", description = "上传附件-公开桶")
//    @PostMapping("/uploadPublicAttached")
//    public R<List<AttachVo>> uploadAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
//        return R.ok(attachService.uploadAttach(files, true));
//    }
//
//    @Operation(summary = "上传附件-私密桶", description = "上传附件-私密桶")
//    @PostMapping("/uploadPrivateAttached")
//    public R<List<AttachVo>> uploadPrivateAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
//        return R.ok(attachService.uploadAttach(files, false));
//    }
//
//
////    @Operation(summary = "下载文件接口-私密桶", description = "下载文件接口-私密桶")
////    @PostMapping("downloadAttached")
////    public void downloadAttached(HttpServletResponse response, @RequestBody MFilePartnerDto fileParams) {
////        //设置文件路径
////        response.setHeader("content-type", "application/octet-stream");
////        response.setContentType("application/octet-stream");
////        tencentCloudService.downLoadObject(fileParams, response, false, true, false);
////    }
//
//}
