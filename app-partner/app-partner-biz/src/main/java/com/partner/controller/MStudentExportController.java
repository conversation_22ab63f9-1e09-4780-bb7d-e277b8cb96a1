package com.partner.controller;


import com.partner.dto.finance.MSettlementBillParamsDto;
import com.partner.dto.student.MStudentExportParams;
import com.partner.service.MStudentExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@Tag(description = "mstudentexport" , name = "小程序-学生信息导出" )
@RestController
@RequestMapping("/mstudentexport")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MStudentExportController {

    private final MStudentExportService mStudentExportService;

    @Operation(summary = "下载学生信息excel", description = "下载学生信息excel")
    @PostMapping("getStudentsExport")
    public void getStudentsExport(HttpServletResponse response, @RequestBody MStudentExportParams dto) {
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        mStudentExportService.getStudentsExport(dto,response);

    }

}
