package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.TableNameDto;
import com.partner.dto.UserInfoCacheDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.service.RPartnerUserAreaCountryService;
import com.partner.service.SearchRedisCacheService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;


@Tag(description = "searchredis" , name = "小程序-查询用户缓存信息" )
@RestController
@RequestMapping("/searchRedisCache")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
@Inner(value = false)
public class SearchRedisCacheController {
    private final SearchRedisCacheService searchRedisCacheService;

    private final RPartnerUserAreaCountryService rPartnerUserAreaCountryService;


    @Operation(summary = "查询用户缓存信息" , description = "查询用户缓存信息" )
    @SysLog("查询用户缓存信息" )
    @GetMapping("/getUserInfoParams" )
    public R getUserInfoParams(@ParameterObject UserInfoCacheDto params) {
        return R.ok(searchRedisCacheService.getUserInfoParams(params));

    }

    @Operation(summary = "清理用户缓存信息" , description = "清理用户缓存信息" )
    @SysLog("清理用户缓存信息" )
    @PostMapping("/deleteUserInfoParams" )
    public R deleteUserInfoParams(@RequestBody UserInfoCacheDto params) {
        rPartnerUserAreaCountryService.deleteSupCache(params.getPartnerUserId());
        return R.ok();

    }

    @Operation(summary = "查询信息" , description = "查询信息" )
    @SysLog("查询信息" )
    @PostMapping("/getTableName" )
    public R getTableName(@RequestBody TableNameDto params) {
        return R.ok(searchRedisCacheService.getTableName(params));

    }




    @Operation(summary = "查询Redis" , description = "查询Redis" )
    @GetMapping("/getRedisObject" )
    public R getRedisObject(@ParameterObject TableNameDto params) {
        return R.ok(searchRedisCacheService.getRedisObject(params));

    }


}
