package com.partner.controller;

import com.common.core.util.R;
import com.partner.service.MFilePlatformService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

@Tag(description = "platFormFileUploadDownload" , name = "平台附件-文件上传下载" )
@RestController
@RequestMapping("/platFormFileUploadDownload")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class PlatFormFileController {

    private final MFilePlatformService fileService;


    @Operation(summary = "平台附件-上传附件-公开桶" , description = "上传附件" )
    @PostMapping("uploadHtiPublicFile")
    public R uploadHtiPublicFile(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files){
        return R.ok(fileService.uploadAppendix(files,true,true));
    }
}
