package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.student.MStudentAddOrEditDto;
import com.partner.dto.student.MStudentAddPublicDto;
import com.partner.service.MStudentPublicService;
import com.partner.service.MStudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(description = "mStudentPublic" , name = "小程序-学生草稿" )
@RestController
@RequestMapping("/public/mStudent")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
@Inner(value = false)
public class MStudentPublicController {

    private final MStudentPublicService mStudentPublicService;

    @Operation(summary = "分享填写学生信息-保存草稿" , description = "分享填写学生信息-保存草稿" )
    @SysLog("分享填写学生信息-新增" )
    @PostMapping("/addStudents" )
    public R addStudents(@RequestBody @Validated(MStudentAddPublicDto.Add.class) MStudentAddPublicDto dto){

        mStudentPublicService.addStudents(dto);
        return R.ok();
    }

}
