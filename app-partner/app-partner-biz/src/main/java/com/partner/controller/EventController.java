package com.partner.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.partner.dto.MEventParamsDto;
import com.partner.entity.EventEntity;
import com.partner.service.EventService;
import com.partner.vo.MEventRegistrationAgentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(description = "event" , name = "小程序-热门活动" )
@RestController
@RequestMapping("/event")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class EventController {

    private final EventService eventService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param params partner
     * @return
     */
    @Operation(summary = "活动查询" , description = "活动查询" )
    @GetMapping("/getEnvenListPage" )
    @Inner(value = false)
    public R getEnvenListPage(Page page, @ParameterObject MEventParamsDto params){
        return R.ok(eventService.getEnvenListPage(page, params));
    }


    @Operation(summary = "活动查询--已登录有用户信息" , description = "活动查询--已登录有用户信息" )
    @GetMapping("/getEnvenListPageUser" )
    public R getEnvenListPageUser(Page page, @ParameterObject MEventParamsDto params){
        return R.ok(eventService.getEnvenListPage(page, params));
    }

    @Operation(summary = "活动报名" , description = "活动报名" )
    @PostMapping("/getEnvenSignUp" )
    public R getEnvenSignUp(@RequestBody MEventParamsDto params){
        return R.ok(eventService.getEnvenSignUp(params));
    }
    @Operation(summary = "取消报名" , description = "活动报名" )
    @PostMapping("/cancelEnvenSignUp" )
    public R cancelEnvenSignUp(@RequestBody MEventParamsDto params){
        return R.ok(eventService.cancelEnvenSignUp(params));
    }


    @Operation(summary = "活动-报名列表",description = "活动-报名列表")
    @GetMapping("searchRegistration")
    public R searchRegistration(Page page, @ParameterObject MEventParamsDto params) {

        return R.ok(eventService.searchRegistration(page, params));
    }



}
