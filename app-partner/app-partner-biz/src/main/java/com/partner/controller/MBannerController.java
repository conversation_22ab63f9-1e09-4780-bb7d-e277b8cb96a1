package com.partner.controller;

import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.MBannerParamsDto;
import com.partner.dto.MMessageParamsDto;
import com.partner.service.MBannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(description = "mbanner" , name = "小程序-banner管理" )
@RestController
@RequestMapping("/mbanner")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
@Inner(value = false)
public class MBannerController {
    private final MBannerService mbannerService;
    @Operation(summary = "首页-banner管理" , description = "首页-banner管理" )
    @SysLog("首页-banner管理" )
    @GetMapping("/searchBanner" )
    public R searchBanner(@ParameterObject @Valid MBannerParamsDto params){
        return R.ok(mbannerService.searchBanner(params));
    }


}
