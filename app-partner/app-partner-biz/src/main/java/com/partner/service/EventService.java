package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.MEventParamsDto;
import com.partner.entity.EventEntity;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * partner
 *
 * <AUTHOR>
 * @date 2024-11-22 10:42:54
 */
public interface EventService extends IService<EventEntity> {
    IPage getEnvenListPage(Page page,MEventParamsDto params);

    /**
     *
     * @param params
     * @desc 活动报名
     * @return
     */
    boolean getEnvenSignUp( MEventParamsDto params);
    /**
     * @param params
     * @desc 活动取消报名
     * @return
     */
    boolean cancelEnvenSignUp( MEventParamsDto params);


    /**
     *
     * @param page
     * @desc 活动报名列表
     * @return
     */
    IPage searchRegistration(Page page,MEventParamsDto params);

}
