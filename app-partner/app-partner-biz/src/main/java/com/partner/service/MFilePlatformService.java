package com.partner.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.entity.MFilePlatformEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_file_platform】的数据库操作Service
* @createDate 2025-05-22 14:14:22
*/
public interface MFilePlatformService extends IService<MFilePlatformEntity> {
    /**
     * 上传附件
     *
     * @param files
     * @param isPub
     * @return
     */
    List<MFilePlatformEntity> uploadAppendix(MultipartFile[] files, Boolean isPub, Boolean isPartnerPath);

}
