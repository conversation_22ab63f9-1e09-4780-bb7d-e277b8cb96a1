package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.student.MStudentDraftParamsDto;
import com.partner.entity.MAppStudentEntity;
import com.partner.entity.MAppStudentOfferItemEntity;
import com.partner.entity.MInstitutionEntity;
import com.partner.eunms.PartnerErrorEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.*;
import com.partner.service.MStudentDraftService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.TencentCloudUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.MAppStudentOfferItemVo;
import com.partner.vo.MStudentBaseVo;
import com.partner.vo.student.MStudentDraftVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
@AllArgsConstructor
public class MStudentDraftServiceImpl implements MStudentDraftService {

    private final MStudentDraftMapper mStudentDraftMapper;

    private final MAppStudentMapper mAppStudentMapper;
    private final MAppStudentOfferItemMapper mAppStudentOfferItemMapper;
    private final MInstitutionMapper mInstitutionMapper;
    private final TencentCloudUtils tencentCloudUtils;
    private final SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Override
    public IPage getPartnerStudents(Page page, MStudentDraftParamsDto dto) {
        IPage<MStudentDraftVo> pageNum = null;

        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        dto.setPlatformId(userinfo.getPlatformId());
        dto.setPartnerUserId(userinfo.getPartnerUserId());
        if (ObjectUtil.isNotEmpty(dto) && ObjectUtil.isNotEmpty(dto.getSearchType())
                && (dto.getSearchType() == -1 || dto.getSearchType() == 1 || dto.getSearchType() == 0)) {

            pageNum = mStudentDraftMapper.getPartnerStudents(page, dto);
        }
        return pageNum;
    }

    @Override
    public IPage getCheckStudentsPage(Page page, MStudentDraftParamsDto dto) {
        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        dto.setPlatformId(userinfo.getPlatformId());
        dto.setPartnerUserId(userinfo.getPartnerUserId());

        IPage<MStudentDraftVo> pageNum = mStudentDraftMapper.getCheckStudentsPage(page, dto);

        return pageNum;
    }

    @Override
    public void getReadingCheck() {
        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        MStudentDraftParamsDto params = new MStudentDraftParamsDto();
        params.setPartnerUserId(userinfo.getPartnerUserId());

        mStudentDraftMapper.getReadingCheck(params);

    }

    @Override
    public int getNoReadingNum() {
        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()), fzhUser.getFkFromPlatformCode(), fzhUser.getId());

        MStudentDraftParamsDto params = new MStudentDraftParamsDto();
        params.setPartnerUserId(userinfo.getPartnerUserId());
        int resultnum = mStudentDraftMapper.getNoReadingNum(params);
        return resultnum;
    }


    @Override
    @DSTransactional
    public long deleteOne(Long id) {
        MAppStudentEntity appStudentEntity = mAppStudentMapper.selectById(id);
        if (ObjectUtil.isNotEmpty(appStudentEntity) && (appStudentEntity.getStatus() == 1 || appStudentEntity.getStatus() == 2)) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode, PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":非草稿信息不能删除!");
        }
        mAppStudentMapper.deleteById(id);
        //只能删除草稿
        //删除学生
        //删除学生代理关系
        //删除学生分配
        // 删除学生UUID


        return id;
    }


    @Override
    public MStudentBaseVo getStudentOne(Long id) {
        MAppStudentEntity mappsudententity = mAppStudentMapper.selectById(id);

        MStudentBaseVo mStudentBaseVo = BeanCopyUtils.objClone(mappsudententity, MStudentBaseVo::new);
        mStudentBaseVo.setStudentUUID(mappsudententity.getId().toString());

        List<MAppStudentOfferItemEntity> itemEntitylist = mAppStudentOfferItemMapper.selectList(new LambdaQueryWrapper<MAppStudentOfferItemEntity>()
                .eq(MAppStudentOfferItemEntity::getFkAppStudentId, id)
        );

        List<MAppStudentOfferItemVo> result = new ArrayList<>();
        for (MAppStudentOfferItemEntity tmp : itemEntitylist) {
            MAppStudentOfferItemVo itemvo = BeanCopyUtils.objClone(tmp, MAppStudentOfferItemVo::new);
            if (tmp.getFkInstitutionId() != null) {
                MInstitutionEntity institution = mInstitutionMapper.selectById(tmp.getFkInstitutionId());
                itemvo.setAreaStateId(institution.getFkAreaStateId());
            }
            result.add(itemvo);
        }


        mStudentBaseVo.setList(result);


        //草稿箱附近-在sale-center的媒体库里面
        mStudentBaseVo.setFileArray(sMediaAndAttachedMapper.selectSaleCenterAttachAndFile("m_app_student", "m_app_student_file", id));
//        SMediaAndAttachedPublicDto params = new SMediaAndAttachedPublicDto(); //学生添加附件
//        String baseurl = tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
//        params.setMMageAddress(baseurl);
//        params.setFkTableName("m_app_student");
//        params.setFkTableId(id);
//        params.setTypeKey("m_app_student_file");
//        List<FileArray> fileArr = sMediaAndAttachedMapper.selectPublicFileArrays(params);

        return mStudentBaseVo;
    }


}
