package com.partner.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.entity.PartnerRoleMenu;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:
 */
public interface PartnerRoleMenuService extends IService<PartnerRoleMenu> {

    /**
     * 保存角色菜单
     *
     * @param roleId
     * @param menuIds
     */
    void saveRoleMenu(Long roleId, List<Long> menuIds);

    /**
     * 获取角色菜单ID
     *
     * @param roleId
     * @return
     */
    List<Long> getRoleMenuIds(Long roleId);

    /**
     * 获取角色菜单权限
     *
     * @param roleIds
     * @return
     */
    List<String> getMenuPermissionByRoleIds(List<Long> roleIds);

}
