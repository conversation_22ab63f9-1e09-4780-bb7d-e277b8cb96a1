package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.SNewsParamsDto;
import com.partner.entity.SNewsEntity;
import com.partner.vo.SNewsDetailVo;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.combox.InstitutionCombox;

import java.util.List;

public interface SNewsService extends IService<SNewsEntity> {

    /**
     * 创建list分页
     * @param page
     * @param dto
     * @return
     */
    IPage getSNewsListPage( Page page,  SNewsParamsDto dto);

    /**
     * 新闻详情
     * @param id
     * @param fkPartnerUserId
     * @return
     */
    SNewsDetailVo getByDetail(Long id,  Long fkPartnerUserId);

    /**
     * 国家下拉
     * @param dto
     * @return
     */
    List<CountryCombox> getCountryCombox(SNewsParamsDto dto);

    /**
     * 学校下拉
     * @param dto
     * @return
     */
    List<InstitutionCombox> getInstitutionCombox(SNewsParamsDto dto);


}