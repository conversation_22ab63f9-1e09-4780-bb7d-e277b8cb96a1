package com.partner.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.offeritem.MAppStudentOfferItemDeleteDto;
import com.partner.dto.offeritem.MAppStudentOfferItemUpdateDto;
import com.partner.dto.student.MAppStudentAddApplyDto;
import com.partner.entity.MStudentOfferItemEntity;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;


public interface MStudentOfferItemService extends IService<MStudentOfferItemEntity> {

    /**
     * 加申申请计划
     * @param list
     * @return
     */
    boolean addOfferItemStudents(MAppStudentAddApplyDto list);

    /**
     * 加申变更
     * @param params
     * @return
     */
    boolean updateOfferItemStudents(MAppStudentOfferItemUpdateDto params);

    /**
     * 删除临时加申
     * @param params
     * @return
     */
    boolean removeById(MAppStudentOfferItemDeleteDto params);
}
