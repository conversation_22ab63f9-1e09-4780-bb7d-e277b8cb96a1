package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.student.MStudentExportParams;
import com.partner.eunms.PartnerErrorEnum;
import com.partner.eunms.PartnerMenuPermissionEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.MStudentExportMapper;
import com.partner.service.MStudentExportService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.MyDateUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.student.MStudentExportVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.Period;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 学生信息导出服务实现类
 * 提供学生数据的Excel导出功能，包含完整的权限控制机制
 */
@Service
@AllArgsConstructor
public class MStudentExportServiceImpl implements MStudentExportService {

    private final MStudentExportMapper mStudentExportMapper;

    // ==================== 常量定义 ====================

    /** Excel工作表名称 */
    private static final String SHEET_NAME = "学生信息";

    /** 默认列宽（字符数 * 256） */
    private static final int DEFAULT_COLUMN_WIDTH = 20 * 256;

    /** Excel列总数 */
    private static final int TOTAL_COLUMNS = 20;

    /** 性别编码：女 */
    private static final String GENDER_FEMALE_CODE = "0";

    /** 地址分隔符 */
    private static final String ADDRESS_SEPARATOR = "/";

    /** Excel表头定义 */
    private static final String[] EXCEL_HEADERS = {
            "最终申请状态", "最初申请状态", "其他入学失败原因", "姓名(中)", "姓名(英)",
            "申请国家", "申请学校", "申请课程", "成功入读国家", "成功入读学校",
            "入读课程", "开学时间", "性别", "所在区域", "年龄",
            "生日", "移动电话", "Email", "学生绑定代理", "学生申请资料收取时间"
    };

    // ==================== 公共接口方法 ====================

    /**
     * 导出学生信息到Excel文件
     *
     * @param params 导出参数，包含筛选条件和权限信息
     * @param response HTTP响应对象，用于输出Excel文件
     */
    @Override
    public void getStudentsExport(MStudentExportParams params, HttpServletResponse response) {
        try {
            // 1. 权限检查和参数构建
            MStudentExportParams exportParams = buildExportParams(params);

            // 2. 权限验证：无权限时返回提示Excel
            if (exportParams.getStudentFlag()) {
                createEmptyExcel(response);
                return;
            }

            // 3. 查询学生数据
            List<MStudentExportVo> studentList = mStudentExportMapper.getStudentsExport(exportParams);

            // 4. 创建并配置Excel工作簿
            Workbook workbook = createWorkbookWithSettings();

            // 5. 创建表头
            Sheet sheet = workbook.getSheet(SHEET_NAME);
            createHeaderRow(sheet);

            // 6. 填充数据行
            fillDataRows(sheet, studentList);

            // 7. 输出Excel文件到响应
            writeWorkbookToResponse(workbook, response);

        } catch (Exception e) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,
                    "学生信息导出失败: " + e.getMessage());
        }
    }

    // ==================== Excel创建相关方法 ====================

    /**
     * 创建Excel工作簿并进行基础设置
     * 包括创建工作表和设置列宽
     *
     * @return 配置好的Excel工作簿
     */
    private Workbook createWorkbookWithSettings() {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(SHEET_NAME);

        // 批量设置列宽，避免重复代码
        for (int i = 0; i < TOTAL_COLUMNS; i++) {
            sheet.setColumnWidth(i, DEFAULT_COLUMN_WIDTH);
        }

        return workbook;
    }

    /**
     * 创建Excel表头行
     * 使用预定义的表头数组，确保列顺序的一致性
     *
     * @param sheet Excel工作表
     */
    private void createHeaderRow(Sheet sheet) {
        Row headerRow = sheet.createRow(0);

        // 使用常量数组创建表头，避免硬编码
        for (int i = 0; i < EXCEL_HEADERS.length; i++) {
            headerRow.createCell(i).setCellValue(EXCEL_HEADERS[i]);
        }
    }

    /**
     * 填充所有学生数据行
     * 遍历学生列表，为每个学生创建一行数据
     *
     * @param sheet Excel工作表
     * @param studentList 学生数据列表
     */
    private void fillDataRows(Sheet sheet, List<MStudentExportVo> studentList) {
        for (int i = 0; i < studentList.size(); i++) {
            MStudentExportVo student = studentList.get(i);
            Row dataRow = sheet.createRow(i + 1); // 从第2行开始（索引1）
            fillSingleDataRow(dataRow, student);
        }
    }

    /**
     * 填充单个学生的数据行
     * 将学生信息按列顺序填入Excel行中
     *
     * @param dataRow Excel数据行
     * @param student 学生信息对象
     */
    private void fillSingleDataRow(Row dataRow, MStudentExportVo student) {
        int columnIndex = 0;

        // 申请状态信息
        dataRow.createCell(columnIndex++).setCellValue(student.getStartStepName());
        dataRow.createCell(columnIndex++).setCellValue(student.getEndStepName());
        dataRow.createCell(columnIndex++).setCellValue(""); // 其他入学失败原因（预留字段）

        // 学生基本信息
        dataRow.createCell(columnIndex++).setCellValue(student.getName());
        dataRow.createCell(columnIndex++).setCellValue(
                (student.getLastName() != null ? student.getLastName() : "") +
                        (student.getFirstName() != null ? student.getFirstName() : ""));

        // 申请信息
        dataRow.createCell(columnIndex++).setCellValue(student.getCountryName());
        dataRow.createCell(columnIndex++).setCellValue(student.getInstitutionName());
        dataRow.createCell(columnIndex++).setCellValue(student.getCourseName());

        // 成功入读信息
        dataRow.createCell(columnIndex++).setCellValue(student.getCountryNameSuccess());
        dataRow.createCell(columnIndex++).setCellValue(student.getInstitutionNameSuccess());
        dataRow.createCell(columnIndex++).setCellValue(student.getCourseNameSuccess());
        dataRow.createCell(columnIndex++).setCellValue(MyDateUtils.formatDate(student.getOpeningTime()));

        // 个人详细信息
        dataRow.createCell(columnIndex++).setCellValue(formatGenderDisplay(student.getGender()));
        dataRow.createCell(columnIndex++).setCellValue(formatStudentAddress(student));
        dataRow.createCell(columnIndex++).setCellValue(calculateAge(student.getBirthday(), LocalDate.now()));
        dataRow.createCell(columnIndex++).setCellValue(MyDateUtils.formatDate(student.getBirthday()));

        // 联系方式和其他信息
        dataRow.createCell(columnIndex++).setCellValue(student.getMobile());
        dataRow.createCell(columnIndex++).setCellValue(student.getEmail());
        dataRow.createCell(columnIndex++).setCellValue(student.getAgentName());
        dataRow.createCell(columnIndex++).setCellValue(
                MyDateUtils.formatDate(student.getReceivedApplicationDataDate()));
    }

    /**
     * 将Excel工作簿写入HTTP响应
     * 处理文件输出和异常情况
     *
     * @param workbook Excel工作簿
     * @param response HTTP响应对象
     */
    private void writeWorkbookToResponse(Workbook workbook, HttpServletResponse response) {
        try (ServletOutputStream out = response.getOutputStream()) {
            workbook.write(out);
            out.flush();
        } catch (Exception e) {
            throw new RuntimeException("Excel文件写入响应失败", e);
        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                // 记录日志但不抛出异常，避免掩盖主要异常
                e.printStackTrace();
            }
        }
    }

    // ==================== 数据格式化方法 ====================

    /**
     * 格式化学生地址信息
     * 将国家、州/省、城市信息组合成完整地址
     *
     * @param student 学生信息对象
     * @return 格式化后的地址字符串，如："中国/广东省/深圳市"
     */
    private String formatStudentAddress(MStudentExportVo student) {
        StringBuilder address = new StringBuilder();

        // 按层级组合地址：国家 -> 州/省 -> 城市
        if (StringUtils.isNotBlank(student.getFkAreaCountryName())) {
            address.append(student.getFkAreaCountryName());
        }

        if (StringUtils.isNotBlank(student.getFkAreaStateName())) {
            if (address.length() > 0) {
                address.append(ADDRESS_SEPARATOR);
            }
            address.append(student.getFkAreaStateName());
        }

        if (StringUtils.isNotBlank(student.getFkAreaCityName())) {
            if (address.length() > 0) {
                address.append(ADDRESS_SEPARATOR);
            }
            address.append(student.getFkAreaCityName());
        }

        return address.toString();
    }

    /**
     * 格式化性别显示
     * 将数据库中的性别编码转换为可读文本
     *
     * @param genderCode 性别编码（0-女，其他-男）
     * @return 性别显示文本
     */
    private String formatGenderDisplay(String genderCode) {
        if (StringUtils.isNotBlank(genderCode) && GENDER_FEMALE_CODE.equals(genderCode)) {
            return "女";
        }
        return "男";
    }

    // ==================== 工具方法 ====================

    /**
     * 计算年龄
     * 根据生日和当前日期计算年龄
     *
     * @param birthDate 生日
     * @param currentDate 当前日期
     * @return 年龄（整数年）
     */
    public  int calculateAge(LocalDate birthDate, LocalDate currentDate) {
        if (birthDate != null && currentDate != null) {
            return Period.between(birthDate, currentDate).getYears();
        } else {
            return 0;
        }
    }

    /**
     * 构建导出参数，包含权限检查逻辑
     * 参考MStudentServiceImpl.resultParams方法
     */
    private MStudentExportParams buildExportParams(MStudentExportParams dto) {
        MStudentExportParams params = new MStudentExportParams();
        BeanCopyUtils.copyProperties(dto, params);

        FzhUser fzhUser = SecurityUtils.getUser();
        UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(
                Long.parseLong(fzhUser.getFkTenantId()),
                fzhUser.getFkFromPlatformCode(),
                fzhUser.getId()
        );

        // 权限检查逻辑（与MStudentServiceImpl保持一致）
        if (CollectionUtils.isEmpty(userinfo.getRoleIds())) {
            params.setStudentFlag(true);
            return params;
        }

        if (CollectionUtils.isNotEmpty(userinfo.getRoleIds())) {
            List<String> permissionKeyArr = userinfo.getPermissionKeys();
            if (CollectionUtils.isEmpty(permissionKeyArr)) {
                params.setStudentFlag(true);
            }
            if (!permissionKeyArr.contains(PartnerMenuPermissionEnum.STUDENT_VIEW_ALL.getCode())
                    && !permissionKeyArr.contains(PartnerMenuPermissionEnum.STUDENT_VIEW_PERSON.getCode())) {
                params.setStudentFlag(true);
            } else if (permissionKeyArr.contains(PartnerMenuPermissionEnum.STUDENT_VIEW_ALL.getCode())) {
                params.setRoleTypeFlag(false);
                params.setStudentFlag(false);
            } else if (permissionKeyArr.contains(PartnerMenuPermissionEnum.STUDENT_VIEW_PERSON.getCode())) {
                params.setRoleTypeFlag(true);
                params.setStudentFlag(false);
            }
            if (params.getStudentFlag()) {
                return params;
            }
        }

        Long agentId = userinfo.getAgentId();
        if (ObjectUtil.isEmpty(agentId)) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,
                    PartnerErrorEnum.CHECK_EXCEPTION.errorMessage + ":代理不存在!");
        }

        // 设置权限相关参数
        params.setFkAgentId(userinfo.getAgentId());
        params.setUserId(userinfo.getUserId());
        params.setTenantId(userinfo.getTenantId());
        params.setPlatformId(userinfo.getPlatformId());
        params.setPartnerUserId(userinfo.getPartnerUserId());
        params.setCompanyId(userinfo.getCompanyId());
        params.setAreaCountryIds(userinfo.getAreaCountryIds());
        params.setLevelPartnerUserIds(userinfo.getLevelPartnerUserIds());
        params.setAgentId(userinfo.getAgentId());

        return params;
    }

    /**
     * 创建空的Excel文件（无权限时返回）
     */
    private void createEmptyExcel(HttpServletResponse response) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("学生信息");

        // 创建表头行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("无权限查看学生信息");

        try {
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
