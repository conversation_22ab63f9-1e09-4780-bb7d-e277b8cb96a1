package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.MEventParamsDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.EventEntity;
import com.partner.entity.MEventRegistrationAgentEntity;
import com.partner.eunms.ConfigTypeEnum;
import com.partner.mapper.EventMapper;
import com.partner.mapper.MEventRegistrationAgentMapper;
import com.partner.service.EventService;
import com.partner.util.TencentCloudUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.AppointmentInfo;
import com.partner.vo.EventPageVo;
import com.partner.vo.MEventRegistrationAgentVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class EventServiceImpl extends ServiceImpl<EventMapper, EventEntity> implements EventService {

    private final EventMapper eventmapper;

    private final MEventRegistrationAgentMapper mEventRegistrationAgentMapper;
    private final TencentCloudUtils tencentCloudUtils;


    @Override
    public IPage getEnvenListPage(Page page, MEventParamsDto params) {
        String baseurl=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        params.setMMageAddress(baseurl);

        FzhUser fzhUser= SecurityUtils.getUser();
        if(fzhUser!=null){
            UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
            params.setPartnerUserId(userinfo.getPartnerUserId());
        }
        //热门活动信息查询
        IPage<EventPageVo> pageNum=eventmapper.getEnvenListPage(page,params);
        List<EventPageVo> eventList= pageNum.getRecords();
        if(ObjectUtil.isNotEmpty(eventList)){
            for(EventPageVo eventPageVo:eventList){
                //报名信息查询
                List<AppointmentInfo> userinfoArr=eventmapper.getUserInfo(eventPageVo);
                if(ObjectUtil.isNotEmpty(userinfoArr)){
                    eventPageVo.setUserinfo(userinfoArr);
                }
            }

        }

        return pageNum;
    }

    @Override
    public boolean getEnvenSignUp(MEventParamsDto params) {
        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        String loginId=fzhUser.getLoginId();
        //查询已报名活动信息
        MEventRegistrationAgentEntity meventdb= mEventRegistrationAgentMapper.selectOne(new LambdaQueryWrapper<MEventRegistrationAgentEntity>()
                .eq(MEventRegistrationAgentEntity::getFkEventId,params.getId())
                .eq(MEventRegistrationAgentEntity::getFkPartnerUserId,userinfo.getPartnerUserId())
        ,false
        );
        if(ObjectUtil.isNotEmpty(meventdb)){
            meventdb.setStatus(1);
            meventdb.setRemark("重新报名");
            mEventRegistrationAgentMapper.updateById(meventdb);

            return true;
        }
        MEventRegistrationAgentEntity meventRegistrationAgentEntity=new MEventRegistrationAgentEntity();
        meventRegistrationAgentEntity.setFkEventId(params.getId());
        meventRegistrationAgentEntity.setFkAgentId(userinfo.getAgentId());
        meventRegistrationAgentEntity.setFkPartnerUserId(userinfo.getPartnerUserId());
        meventRegistrationAgentEntity.setName(params.getName());
        meventRegistrationAgentEntity.setMobileAreaCode(params.getMobileAreaCode());
        meventRegistrationAgentEntity.setMobile(params.getMobile());
        meventRegistrationAgentEntity.setPeopleCount(params.getPeopleCount());

        meventRegistrationAgentEntity.setStatus(1);
        meventRegistrationAgentEntity.setRemark("活动报名");
        meventRegistrationAgentEntity.setGmtCreate(LocalDateTime.now());
        meventRegistrationAgentEntity.setGmtCreateUser(loginId);

        //保存报名
        mEventRegistrationAgentMapper.insert(meventRegistrationAgentEntity);

        return true;
    }

    @Override
    public boolean cancelEnvenSignUp(MEventParamsDto params) {
        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        //查询报名活动信息
        MEventRegistrationAgentEntity meventdb= mEventRegistrationAgentMapper.selectOne(new LambdaQueryWrapper<MEventRegistrationAgentEntity>()
                        .eq(MEventRegistrationAgentEntity::getFkEventId,params.getId())
                        .eq(MEventRegistrationAgentEntity::getFkPartnerUserId,userinfo.getPartnerUserId())
                ,false
        );
        meventdb.setStatus(2);
        if(ObjectUtil.isNotEmpty(meventdb)){
            mEventRegistrationAgentMapper.updateById(meventdb);
        }

        return true;
    }


    @Override
    public IPage searchRegistration(Page page, MEventParamsDto params) {

        IPage<MEventRegistrationAgentVo> pageNum=eventmapper.searchRegistration(page,params);

        return pageNum;
    }


}
