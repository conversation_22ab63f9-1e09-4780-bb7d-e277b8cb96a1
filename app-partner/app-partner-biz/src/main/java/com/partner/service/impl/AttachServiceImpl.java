package com.partner.service.impl;

import com.common.core.util.HttpUtils;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.constant.AisMiddleConstant;
import com.partner.dto.attach.UploadRequestDto;
import com.partner.service.AttachService;
import com.partner.vo.attach.AttachVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/11
 * @Version 1.0
 * @apiNote:附件管理-调用AIS上传和下载
 */
@Service
@Slf4j
public class AttachServiceImpl implements AttachService {

    @Resource
    private HttpUtils httpUtils;

    @Override
    public List<AttachVo> uploadAttach(MultipartFile[] files, Boolean isPub) {
        FzhUser user = SecurityUtils.getUser();
        for (MultipartFile file : files) {
            UploadRequestDto requestDto = UploadRequestDto.builder()
                    .files(new MultipartFile[]{file})
                    .isPrivateBucket(isPub)
                    .serviceName(AisMiddleConstant.UPLOAD_SERVICE_NAME)
                    .typeKey("s_media_and_attached")
                    .gmtCreateUser(user.getLoginId())
                    .prefix(isPub ? AisMiddleConstant.PUBLIC_FILE_PREFIX : AisMiddleConstant.PRIVATE_FILE_PREFIX)
                    .build();
            String result = httpUtils.sendPostRequest(requestDto, AisMiddleConstant.UPLOAD_PATH, file);
            log.info("上传结果：{}", result);
        }
        return Collections.emptyList();
    }

    @Override
    public void saveMediaAndAttached(List<UploadRequestDto> requestList) {

    }
}
