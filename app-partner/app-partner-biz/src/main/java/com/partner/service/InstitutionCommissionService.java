package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.CommissionParamsDetailDto;
import com.partner.dto.CommissionParamsDto;
import com.partner.vo.combox.GroupResultCombox;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.CommissionDetailInfo;
import com.partner.vo.CommissionDetailVo;
import com.partner.vo.combox.LeaveResultCombox;
import com.partner.vo.combox.TypeResultCombox;
import com.pmp.dto.CommissionDto;
import com.pmp.dto.DateDto;
import com.pmp.dto.InstitutionDto;
import com.pmp.vo.commission.MajorLevelTreeVo;
import com.pmp.vo.commission.MajorLevelVo;
import com.pmp.vo.commission.MergeCommissionVo;
import com.pmp.vo.institution.CountryVo;
import com.pmp.vo.institution.GroupVo;
import com.pmp.vo.institution.InstitutionTypeVo;
import com.pmp.vo.institution.InstitutionVo;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

public interface InstitutionCommissionService  extends IService<CommissionDetailVo> {
    /**
     * @desc 佣金分页查询
     * @return
     */
    IPage getCommissionPage(Page page, CommissionParamsDto dto);

    /**
     * 高佣明细
     * @param dto
     * @return
     */
    List<CommissionDetailInfo>  getCommissionDetail(CommissionParamsDetailDto dto);
    /**
     * 首页高佣
     * @param dto
     * @return
     */
    CommissionDetailVo getHomeCommissionDetail(CommissionParamsDetailDto dto);

    List<CountryCombox> getCountryCombox(CommissionParamsDetailDto dto,int type);


    List<TypeResultCombox> getTypeList(CommissionParamsDetailDto dto);

    List<GroupResultCombox> getGroupByCommission(CommissionParamsDetailDto dto);


    List<LeaveResultCombox> getLeaveList(CommissionParamsDetailDto dto);


    /**
     * 所有高佣
     * @param dto
     * @return
     */
    List<CommissionDetailVo> getTallCommissionList(CommissionParamsDto dto);

    IPage getTallCommissionList(Page page, CommissionParamsDto dto);

    /**
     * 获取学校佣金-pmp2.0
     * @param commissionDto
     * @return
     */
    MergeCommissionVo getMergeCommission(CommissionDto commissionDto);

    /**
     * 获取国家列表-pmp2.0
     * @param dto
     * @return
     */
    List<CountryVo> countryList(DateDto dto);

    /**
     * 获取学校类型列表-pmp2.0
     * @return
     */
    List<InstitutionTypeVo> institutionTypeList();

    /**
     * 获取集团列表-pmp2.0
     * @param dateDto
     * @return
     */
    List<GroupVo> groupList(DateDto dateDto);

    /**
     * 获取课程等级列表-pmp2.0
     * @return
     */
    List<MajorLevelVo> selectMajorLevel();

    /**
     * 获取课程等级树-pmp2.0
     * @return
     */
    List<MajorLevelTreeVo> getMajorLevelTree();

    /**
     * 获取学校列表-pmp2.0
     * @param institutionDto
     * @return
     */
    Page<InstitutionVo> institutionList(InstitutionDto institutionDto);

}
