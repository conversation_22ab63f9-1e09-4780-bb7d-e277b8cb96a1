package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.apps.api.entity.SystemUserEntity;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.partner.dto.TableNameDto;
import com.partner.dto.UserInfoCacheDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.mapper.EventMapper;
import com.partner.mapper.SystemUserMapper;
import com.partner.service.SearchRedisCacheService;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class SearchRedisCacheServiceImpl implements SearchRedisCacheService {
    private final RedisTemplate redisTemplate;
    private final SystemUserMapper systemUserMapper;
    private final EventMapper eventMapper;

    @Override
    public UserInfoParams getUserInfoParams(UserInfoCacheDto params) {
        UserInfoParams result=new UserInfoParams();
        String redisKey = params.getTenantId() + ":PARTNER:" + params.getUserId();

        if(params.getUserId()==null && params.getUserName()!=null && params.getUserName().length()>0){
            SystemUserEntity poEntity=systemUserMapper.selectOne(new LambdaQueryWrapper<SystemUserEntity>().eq(SystemUserEntity::getName,params.getUserName())
                    ,false
            );
            if(ObjectUtil.isNotEmpty(poEntity)){
                params.setUserId(poEntity.getId());
            }
        }
        if(params.getUserId()==null){
            return null;
        }
        Object cachedData = redisTemplate.opsForValue().get(redisKey);
        if(cachedData==null){
            return null;
        }
        if (cachedData instanceof String) {
            return JSONObject.parseObject((String) cachedData, UserInfoParams.class);
        }
        return (UserInfoParams) cachedData;
    }

    @Override
    public List<Map<String,String>> getTableName(TableNameDto params) {
        List<Map<String,String>>  result=eventMapper.getTableName(params);

        return result;
    }

    @Override
    public Object getRedisObject(TableNameDto params) {
        Object cachedData = null;
        if(params.getType()==0){
             cachedData = redisTemplate.opsForValue().get(params.getRedisKey());
        }else if(params.getType()==1){
            cachedData = redisTemplate.opsForHash().get(params.getRedisKey(),params.getHashKey());
        }

        if(cachedData==null){
            return null;
        }

        return cachedData;
    }


}
