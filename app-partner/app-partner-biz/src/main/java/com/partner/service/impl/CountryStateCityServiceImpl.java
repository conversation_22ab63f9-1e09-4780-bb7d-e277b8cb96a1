package com.partner.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.base.CountryStateCityParamsDto;
import com.partner.dto.base.InstitutionCourseParamsDto;
import com.partner.dto.base.InstitutionSearchDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.student.AreaCountryDto;
import com.partner.mapper.CountryStateCityMapper;
import com.partner.service.CountryStateCityService;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.base.*;
import com.partner.vo.combox.AreaCountryVo;
import lombok.AllArgsConstructor;
import org.jsoup.Connection;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@AllArgsConstructor
public class CountryStateCityServiceImpl implements CountryStateCityService {
    private final CountryStateCityMapper countryStateCityMapper;

    @Override
    public List<CountryBaseCombox> getCountryCombox(CountryStateCityParamsDto dto) {


        return countryStateCityMapper.getCountryCombox(dto);
    }

    @Override
    public List<CountryBaseCombox> getAllPubCountryCombox(CountryStateCityParamsDto dto) {
        return countryStateCityMapper.getAllPubCountryCombox(dto);
    }


    @Override
    public List<StateBaseCombox> getStateCombox(CountryStateCityParamsDto dto) {

        return countryStateCityMapper.getStateCombox(dto);
    }

    @Override
    public List<CityBaseCombox> getCityCombox(CountryStateCityParamsDto dto) {

        return countryStateCityMapper.getCityCombox(dto);
    }

    @Override
    public List<BaseCombox> getEducationCombox() {
        return countryStateCityMapper.getEducationCombox();
    }

    @Override
    public List<BaseCombox> getInstitutionList(CountryStateCityParamsDto dto) {
        return countryStateCityMapper.getInstitutionList(dto);
    }

    @Override
    public List<CourseCombox> getInstitutionCourseList(InstitutionCourseParamsDto dto) {
        List<CourseCombox> list=countryStateCityMapper.getInstitutionCourseList(dto);
        return list;
    }

    @Override
    public List<BaseCombox> getInstitutionListSearch(InstitutionSearchDto dto) {

        FzhUser fzhUser= SecurityUtils.getUser();
        if(ObjectUtils.isNotEmpty(fzhUser)){
            UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
            if(ObjectUtils.isNotEmpty(userinfo)){
                dto.setFkCompanyId(userinfo.getCompanyId());
            }
        }


        return countryStateCityMapper.getInstitutionListSearch(dto);
    }

    @Override
    public List<AreaCountryVo> getAreaCode(AreaCountryDto params) {
        return countryStateCityMapper.getAreaCode(params);
    }


}
