package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.MLiveParamsDto;
import com.partner.dto.MLivecalendarParamsDto;
import com.partner.entity.MLiveEntity;
import com.partner.vo.MLivePageVo;
import com.partner.vo.MLivecalendarVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


public interface MLiveService  extends IService<MLiveEntity> {

    /**
     * 直播分页
     * @param page
     * @param dto
     * @return
     */
    IPage getMLiveListPage(Page page,MLiveParamsDto dto);

    /**
     * 首页直播
     * @param loginType
     * @return
     */
    List<MLivePageVo> getHomeMLiveList(String loginType);

    /**
     *
     * @param params
     * @return
     */
    List<MLivecalendarVo> getStatus(MLivecalendarParamsDto params);

}
