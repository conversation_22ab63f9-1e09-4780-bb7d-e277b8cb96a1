package com.partner.service.impl;

import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.MMessageParamsDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.mapper.MMessageMapper;
import com.partner.service.MMessageService;
import com.partner.entity.MMessageEntity;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.MMessageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@AllArgsConstructor
public class MMessageServiceImpl extends ServiceImpl<MMessageMapper, MMessageEntity>  implements MMessageService {

    private final MMessageMapper mMessageMapper;


    @Override
    public List<MMessageVo> searchMessage(MMessageParamsDto params) {
        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());

        params.setFkPlatformId(userinfo.getPlatformId());

        List<MMessageVo>  result= mMessageMapper.searchMessage(params);

        return result;
    }
}
