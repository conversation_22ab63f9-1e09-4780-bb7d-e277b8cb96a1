package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.SNewsParamsDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.RNewsPartnerUserReadEntity;
import com.partner.entity.SMediaAndAttachedEntity;
import com.partner.entity.SNewsEntity;
import com.partner.eunms.ConfigTypeEnum;
import com.partner.mapper.RNewsPartnerUserReadMapper;
import com.partner.mapper.SMediaAndAttachedMapper;
import com.partner.mapper.SNewsMapper;
import com.partner.service.SNewsService;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.SMediaAndAttachedVo;
import com.partner.vo.SNewsDetailVo;
import com.partner.vo.SNewsListVo;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.combox.InstitutionCombox;
import jdk.nashorn.api.scripting.ScriptUtils;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


/**
 * partner
 *
 * <AUTHOR>
 * @date 2024-11-22 10:42:54
 */
@Service
@AllArgsConstructor
public class SNewsServiceImpl extends ServiceImpl<SNewsMapper, SNewsEntity> implements SNewsService {

    private final SNewsMapper snewsmapper;


    private final RNewsPartnerUserReadMapper rNewsPartnerUserReadMapper;

    private final SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Override
    public IPage getSNewsListPage(Page page, SNewsParamsDto dto) {
        dto.setNewsTypeId(ConfigTypeEnum.U_NEWS_TYPE.key);

        IPage<SNewsListVo>  result=snewsmapper.getSNewsListPage(page,dto);


        return result;
    }

    @Override
    public SNewsDetailVo getByDetail(Long id,  Long fkPartnerUserId) {
        SNewsDetailVo resultvo=new SNewsDetailVo();
        SNewsEntity snewspo=snewsmapper.getByDetail(id);

        if(!ObjectUtil.isEmpty(snewspo)){
            BeanUtils.copyProperties(snewspo, resultvo);
            try{
                Long rNewsCount=
                        rNewsPartnerUserReadMapper.selectCount(new LambdaQueryWrapper<RNewsPartnerUserReadEntity>()
                                .eq(RNewsPartnerUserReadEntity::getFkNewsId,id));
                Long total_tmp=rNewsCount;
                FzhUser user=SecurityUtils.getUser();

                if(ObjectUtil.isNotEmpty(fkPartnerUserId)){

                    RNewsPartnerUserReadEntity  rNewsPO=rNewsPartnerUserReadMapper.selectOne(new LambdaQueryWrapper<RNewsPartnerUserReadEntity>()
                                    .eq(RNewsPartnerUserReadEntity::getFkNewsId,id)
                                    .eq(RNewsPartnerUserReadEntity::getFkPartnerUserId,fkPartnerUserId)
                            ,false);
                    if(ObjectUtil.isEmpty(rNewsPO)){
                        RNewsPartnerUserReadEntity savePO=new RNewsPartnerUserReadEntity();
                        savePO.setFkNewsId(id);
                        savePO.setFkPartnerUserId(fkPartnerUserId);
                        savePO.setGmtCreate(LocalDateTime.now());
                        savePO.setGmtCreateUser(fkPartnerUserId.toString());
                        rNewsPartnerUserReadMapper.insert(savePO);
                    }
                }
                total_tmp=total_tmp+1;
                resultvo.setTotal(total_tmp.intValue());

                SMediaAndAttachedEntity params=new SMediaAndAttachedEntity();
                params.setFkTableName("s_news");
                params.setTypeKey("institution_news_appendix");
                params.setFkTableId(snewspo.getId());

                List<SMediaAndAttachedVo> fileKeyArry=sMediaAndAttachedMapper.selectInstitutionFile(params);
                resultvo.setFileKeyArry(fileKeyArry);
            }catch (Exception e){
                log.error(e.getMessage());
            }

        }

        return resultvo;
    }


    @Override
    public List<CountryCombox> getCountryCombox(SNewsParamsDto dto) {
        dto.setNewsTypeId(ConfigTypeEnum.U_NEWS_TYPE.key);
        List<CountryCombox> resultlist=snewsmapper.getCountryCombox(dto);

        return resultlist;
    }

    @Override
    public List<InstitutionCombox> getInstitutionCombox(SNewsParamsDto dto) {
        dto.setNewsTypeId(ConfigTypeEnum.U_NEWS_TYPE.key);
        List<InstitutionCombox> resultlist=snewsmapper.getInstitutionCombox(dto);

        return resultlist;
    }


}