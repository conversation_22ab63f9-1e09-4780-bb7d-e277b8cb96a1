package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.apps.api.entity.SystemMenuEntity;
import com.apps.api.entity.SystemUserEntity;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.TeamDataSumDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.finance.*;
import com.partner.dto.finance.paramsmapper.CommissionSearchParams;
import com.partner.dto.finance.paramsmapper.SettlementSearchParams;
import com.partner.entity.*;
import com.partner.eunms.ConfigTypeEnum;
import com.partner.eunms.PartnerErrorEnum;
import com.partner.eunms.SettlementEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.*;
import com.partner.service.FinanceService;
import com.partner.util.BeanCopyUtils;
import com.partner.util.MyDateUtils;
import com.partner.util.PartnerExchangeRateUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.TeamMemberVo;
import com.partner.vo.finance.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class FinanceServiceImpl implements FinanceService {

    private final FinanceMapper financeMapper;

    private final MStudentOfferItemAgentConfirmMapper offerItemAgentConfirmMapper;

    private final RStudentOfferItemUuidMapper  rstudentOfferItemUuidMapper;
    private final RAgentUuidMapper rAgentUuidMapper;
    private final RStudentUuidMapper rStudentUuidMapper;

    private final RPayablePlanSettlementInstallmentMapper rPayablePlanSettlementInstallmentMapper;

    private final RPayablePlanSettlementStatusMapper rPayablePlanSettlementStatusMapper;

    private final RPayablePlanSettlementFlagMapper rPayablePlanSettlementFlagMapper;

    private final SaletestMapper saletestMapper;
    private final PartnertestMapper partnertestMapper;

    private final MSettlementBillMapper mSettlementBillMapper;
    private final MSettlementBillItemMapper mSettlementBillItemMapper;
    private final RSettlementBillSettlementInstallmentMapper rSettlementBillSettlementInstallmentMapper;
    private final LogSettlementBillSettlementInstallmentDetailMapper rSettlementBillSettlementInstallmentDetailMapper;

    private final SMediaAndAttachedMapper sMediaAndAttachedMapper;

    private final UExchangeRateMapper uExchangeRateMapper;

    private final RSettlementSignatureMapper rSettlementSignatureMapper;

    private final MCompanyMapper mCompanyMapper;

    private final SystemConfigMapper systemConfigMapper;

    private final PartnerExchangeRateUtils partnerExchangeRateUtils;

    private final SystemUserMapper systemUserMapper;

    private final AppSystemCenterMapper appSystemCenterMapper;



    @Override
    public IPage getCommissionAffirmPage(Page page, CommissionSearchParamsDto dto) {

        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());


        CommissionSearchParams params=new CommissionSearchParams();
        BeanCopyUtils.copyProperties(dto,params);
        BeanCopyUtils.copyProperties(userinfo,params);
        params.setAgentId(userinfo.getAgentId());
        params.setPartnerUserId(userinfo.getPartnerUserId());

        if(CollectionUtils.isNotEmpty(userinfo.getRoleIds())){

            List<String> permissionKeyArr=userinfo.getPermissionKeys();
            if(CollectionUtils.isEmpty(permissionKeyArr)){
                params.setStudentFlag(true);
            }
            if(!permissionKeyArr.contains("STUDENT_VIEW_ALL")
                    && !permissionKeyArr.contains("STUDENT_VIEW_PERSON")  ){
                params.setStudentFlag(true);
            }else if(permissionKeyArr.contains("STUDENT_VIEW_ALL")) {
                params.setRoleTypeFlag(false);
                params.setStudentFlag(false);
            }else if(permissionKeyArr.contains("STUDENT_VIEW_PERSON")) {
                params.setRoleTypeFlag(true);
            }
        }

        if(params.getStudentFlag()){
            return new Page();
        }

        IPage<CommissionAffirmVo> listpage=financeMapper.getCommissionAffirmPage(page,params);

        List<CommissionAffirmVo> listCommissionAffirm= listpage.getRecords();
        if(ObjectUtil.isNotEmpty(listCommissionAffirm)){
            List<Long> ids=listCommissionAffirm.stream().map(CommissionAffirmVo::getOfferItemId).collect(Collectors.toList());
            List<MReceivablePlanEntity>  mreceivableplanlist= financeMapper.getReceivablePlan(ids);

            if(ObjectUtil.isNotEmpty(mreceivableplanlist)){
                Map<Long,MReceivablePlanEntity> receivableMap=mreceivableplanlist.stream().collect(Collectors.toMap(o -> o.getFkTypeTargetId(), o -> o, (v1, v2) -> v1));
                for(CommissionAffirmVo commissionAffirm: listCommissionAffirm){
                    if(receivableMap.containsKey(commissionAffirm.getOfferItemId())){
                        MReceivablePlanEntity receivableEn=receivableMap.get(commissionAffirm.getOfferItemId());
                        commissionAffirm.setTuitionAmount(receivableEn.getTuitionAmount());
                        commissionAffirm.setFkCurrencyTypeNum(receivableEn.getFkCurrencyTypeNum());

                    }
                }

            }



        }



        return listpage;
    }

    public void affirmOfferItem(CommissionAffirmParamsDto paramsDto){
        List<MStudentOfferItemAgentConfirmEntity> confirmEntityList=new ArrayList<>();

        //--后续增加逻辑-------校验财务角色不允许 确认名单
        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());

        String loginId=fzhUser.getLoginId();


        List<CommissionAffirmDto> commissionAffirmVoList=paramsDto.getCommissionAffirmVoList();
/*
        RAgentUuidEntity rAgentUuidEntity=rAgentUuidMapper.selectByUUID(paramsDto.getAgentUUID());*/

        Long agentId=userinfo.getAgentId();
        if(ObjectUtil.isEmpty(agentId)){
            log.error("==============FinanceServiceImpl==============user===="+JSONObject.toJSONString(paramsDto));
            //UUID数据不存在!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":代理不存在!");
        }
        //校验  申请计划是否已经确认
        int valiynum= offerItemAgentConfirmMapper.valiyAffirmOfferItem(commissionAffirmVoList);
        if(valiynum>0){
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":已确认申请计划不能重复操作!");
        }

        for(CommissionAffirmDto commissionAffirmDto:commissionAffirmVoList){

            RStudentOfferItemUuidEntity uuidEntity=
                    rstudentOfferItemUuidMapper.selectByUUID(commissionAffirmDto.getOfferItemUUID());
            if(ObjectUtil.isEmpty(uuidEntity)){
                //UUID数据不存在!
                throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":申请项目UUID不存在!");
            }

            MStudentOfferItemAgentConfirmEntity tmpAgentConfirm=new MStudentOfferItemAgentConfirmEntity();
            tmpAgentConfirm.setFkTenantId(userinfo.getTenantId());
            tmpAgentConfirm.setFkStudentOfferItemId(uuidEntity.getFkStudentOfferItemId());
            tmpAgentConfirm.setFkAgentId(agentId);
            tmpAgentConfirm.setFkPartnerUserId(userinfo.getPartnerUserId());
            tmpAgentConfirm.setIsSystemConfirmed(false);
            tmpAgentConfirm.setGmtCreate(LocalDateTime.now());
            tmpAgentConfirm.setGmtCreateUser(loginId);

            confirmEntityList.add(tmpAgentConfirm);
        }




        offerItemAgentConfirmMapper.insertAffirmOfferItem(confirmEntityList);
    }

    @Override
    public AgentSettlementOfferItemResultVo getAgentSettlementPage( SettlementSearchParamsDto dto) {
        AgentSettlementOfferItemResultVo resultVo=new AgentSettlementOfferItemResultVo();
        FzhUser fzhUser= SecurityUtils.getUser();
        String loginId=fzhUser.getLoginId();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        Long agentId=userinfo.getAgentId();
        if(ObjectUtil.isEmpty(agentId)){
            //代理数据不存在!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":代理不存在!");
        }

        SettlementSearchParams params=new SettlementSearchParams();
        BeanCopyUtils.copyProperties(dto,params);
        params.setAgentId(agentId);

        List<AgentSettlementOfferItemVo> settmentList=financeMapper.getAgentSettlementList(params);

        //默认币种
        String toCurrency=getCurrencyDefault(userinfo.getCompanyId(),"PARTNER","PARTNER_COMPANY_DEFAULT_CURRENCY");

        if(ObjectUtil.isNotEmpty(settmentList)){
            List<Long> ids=settmentList.stream().map(AgentSettlementOfferItemVo::getOfferItemId).collect(Collectors.toList());
            //查询申请计划对应得分期支付信息
            List<MReceivablePlanEntity>  mreceivableplanlist= financeMapper.getReceivablePlan(ids);
            if(ObjectUtil.isNotEmpty(mreceivableplanlist)){
                Map<Long,MReceivablePlanEntity> receivableMap=mreceivableplanlist.stream().collect(Collectors.toMap(o -> o.getFkTypeTargetId(), o -> o, (v1, v2) -> v1));
                for(AgentSettlementOfferItemVo settletmp:settmentList){
                    if(receivableMap.containsKey(settletmp.getOfferItemId())){
                        MReceivablePlanEntity receivableEn=receivableMap.get(settletmp.getOfferItemId());
                        settletmp.setTuitionAmount(receivableEn.getTuitionAmount());
                        settletmp.setTuitionCurrencyTypeNum(receivableEn.getFkCurrencyTypeNum());
                        settletmp.setCommissionRate(receivableEn.getCommissionRate());
                        settletmp.setFixedAmount(receivableEn.getFixedAmount());
                    }

                }

            }


        }

        //计算总金额显示
        //获取汇率
        BigDecimal amount=new BigDecimal(0);
        for(AgentSettlementOfferItemVo settletmp:settmentList){
            String fromCurrency=settletmp.getFkCurrencyTypeNum();


            BigDecimal exchangeRate=new BigDecimal(1);
            BigDecimal amountExchange=new BigDecimal(0);
            BigDecimal serviceFeeExchange=new BigDecimal(0);
            if(ObjectUtil.isEmpty(fromCurrency) ||  ObjectUtil.isEmpty(toCurrency) ||  fromCurrency.equals(toCurrency)){
                exchangeRate=new BigDecimal(1);
                amountExchange =settletmp.getAmountActual();
                serviceFeeExchange=settletmp.getServiceFeeActual();
                settletmp.setExchangeRate(exchangeRate);
                settletmp.setAmountExchange(amountExchange);
                settletmp.setServiceFeeExchange(serviceFeeExchange);
            }else {

                exchangeRate=new BigDecimal(1);
                amountExchange =settletmp.getAmountActual();
                serviceFeeExchange=settletmp.getServiceFeeActual();

                //活动缓存汇率
                BigDecimal exchangeRate_redis=UserInfoParamsUtils.getExchangeRate(ConfigTypeEnum.M_EXCHANGERATE_HASH.uNewsType,
                        fromCurrency,toCurrency);
                if(exchangeRate_redis.compareTo(BigDecimal.ONE) == 0){

                    //获取汇率
                    UExchangeRateEntity entity=getLastRate(fromCurrency,toCurrency);
                    if(ObjectUtil.isNotEmpty(entity) &&  ObjectUtil.isEmpty(entity.getId())){
                        entity.setGetDate(LocalDate.now());
                        entity.setGmtCreateUser(userinfo.getPartnerUserId().toString());
                        uExchangeRateMapper.insert(entity);
                    }
                    if(ObjectUtil.isNotEmpty(entity)){
                        exchangeRate=entity.getExchangeRate();
                        amountExchange=amountExchange.multiply(exchangeRate);
                        serviceFeeExchange=serviceFeeExchange.multiply(exchangeRate);
                        UserInfoParamsUtils.setExchangeRate(ConfigTypeEnum.M_EXCHANGERATE_HASH.uNewsType,
                                fromCurrency,toCurrency,exchangeRate);
                    }else {
                        throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":无线上汇率!");
                    }

                }else{
                    exchangeRate=exchangeRate_redis;
                    amountExchange=amountExchange.multiply(exchangeRate);
                    serviceFeeExchange=serviceFeeExchange.multiply(exchangeRate);

                }


                settletmp.setExchangeRate(exchangeRate);
                settletmp.setAmountExchange(amountExchange);
                settletmp.setServiceFeeExchange(serviceFeeExchange);


            }
            amount=amount.add(amountExchange);
            amount=amount.add(serviceFeeExchange);

        }
        resultVo.setAmount(amount);
        resultVo.setSettmentList(settmentList);
        return resultVo;
    }

    @Override
    public Boolean getValidSettlement() {

        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        Long agentId=userinfo.getAgentId();
        //判断 代理 最新一个结算单，是否已经处理完，否则提示结算中
        int countnum=financeMapper.selectstatusSettlement(agentId);
        if(countnum>0){
            log.error("=========agentConfirmSettlement代理下存在未处理完成的结算单!======"+agentId);
            //代理下存在未处理完成的结算单!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":存在未处理完成的结算单!");
        }
        return true;
    }

    public String getCurrencyDefault(Long companyId,String fkPlatformCode,String configKey){
        String toCurrency="CNY";
        try{
            MCompanyEntity company=mCompanyMapper.selectById(companyId);
            if(ObjectUtil.isNotEmpty(company)){
                SystemConfigEntity configEntity=systemConfigMapper.selectOne(new LambdaQueryWrapper<SystemConfigEntity>()
                        .eq(SystemConfigEntity::getFkPlatformCode,fkPlatformCode)
                        .eq(SystemConfigEntity::getConfigKey,configKey),false
                );
                String num=company.getNum();
                if(ObjectUtil.isNotEmpty(configEntity)){
                    String value1=configEntity.getValue1();
                    JSONObject jsonobject=JSONObject.parseObject(value1);
                    String currency=jsonobject.getString(num);
                    if(StringUtils.isNotEmpty(currency)){
                        toCurrency=currency;
                    }
                }
            }
        }catch (Exception e){
            log.error(e.getMessage());
        }

        return toCurrency;
    }


    public UExchangeRateEntity getLastRate(String fromCurrency, String toCurrency)  {
        if("FCY".equals(toCurrency)){
            toCurrency="CNY";
        }
        UExchangeRateEntity rateEntity=uExchangeRateMapper.selectOne(new LambdaQueryWrapper<UExchangeRateEntity>()
                        .eq(UExchangeRateEntity::getFkCurrencyTypeNumFrom,fromCurrency)
                        .eq(UExchangeRateEntity::getFkCurrencyTypeNumTo,toCurrency)
                        .eq(UExchangeRateEntity::getGetDate,LocalDate.now())
                ,false);
        if(ObjectUtil.isEmpty(rateEntity)){
            try{
                rateEntity=new UExchangeRateEntity();
                String result=partnerExchangeRateUtils.getExchangeRateUtils(fromCurrency,toCurrency);
                if(StringUtils.isNotEmpty(result)){
                    PartnerExchangeRate exchangeRate= JSONObject.parseObject(result, PartnerExchangeRate.class);
                    if(StringUtils.isNotEmpty(exchangeRate.getStatus()) && "0".equals(exchangeRate.getStatus())){
                        PartnerExchangeRateResult partnerExchangeRateResult=exchangeRate.getResult();
                        rateEntity.setFkCurrencyTypeNumFrom(fromCurrency);
                        rateEntity.setFkCurrencyTypeNumTo(toCurrency);
                        rateEntity.setExchangeRate(partnerExchangeRateResult.getRate());
                        rateEntity.setGetDate(LocalDate.now());

                        rateEntity.setGmtCreate(LocalDateTime.now());
                    } else  {
                        return null;
                    }
                }else{
                    return null;
                }

            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }


        return rateEntity;
    }



    @Override
    @DSTransactional
    public Long agentConfirmSettlement(ConfirmSettlementDto confirmDto ) {
        Long resultvalue=0l;
        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
        Long agentId=userinfo.getAgentId();

        String loginid=fzhUser.getLoginId();
        //日志
        List<LogSettlementBillSettlementInstallmentDetailEntity> logSettlementBill=new ArrayList<>();
        //批次
        List<RSettlementBillSettlementInstallmentEntity> batchSettlementBill=new ArrayList<>();
        MFileSettlementInfo fileinfo=confirmDto.getFileEntity();
        if(ObjectUtil.isEmpty(agentId)){
            //代理UUID数据不存在!
            //throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":代理不存在!");
        }
        List<ConfirmSettlementInfo> listConfirm=confirmDto.getConfirmSettlementInfoList();
        if(ObjectUtil.isEmpty(listConfirm)){
            //提交结算信息不存在!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":提交结算信息不存在!");
        }
        //判断 代理 最新一个结算单，是否已经处理完，否则提示结算中
        int countnum=financeMapper.selectstatusSettlement(agentId);
        if(countnum>0){
            log.error("=========agentConfirmSettlement代理下存在未处理完成的结算单!======"+JSONObject.toJSONString(confirmDto));
            //代理下存在未处理完成的结算单!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":存在未处理完成的结算单!");
        }



        for(ConfirmSettlementInfo settlementInfo:listConfirm){


            //学生UUID校验
            if(ObjectUtil.isEmpty(settlementInfo.getStudentUUID())  ){
                //代理UUID数据不存在!
                throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":学生UUID不存在!");
            }

        }
        List<String> UUIDs=listConfirm.stream().map(ConfirmSettlementInfo::getStudentUUID).collect(Collectors.toList());
        List<RStudentUuidEntity> studentUuidList=rStudentUuidMapper.selecteBatchByUUIDs(UUIDs);
        //学生UUID校验
        if(ObjectUtil.isEmpty(studentUuidList)  ){
            //代理UUID数据不存在!
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":学生UUID不存在!");
        }

        Map<String,RStudentUuidEntity> rStudentmap=studentUuidList.stream().collect(Collectors.toMap(o->o.getFkStudentUuid(), o -> o,(v1, v2)->v1));


        for(ConfirmSettlementInfo confirmSettlementInfo:listConfirm){
            log.error("=========agentConfirmSettlement提交结算信息不存在======"+JSONObject.toJSONString(confirmDto));
            if(confirmSettlementInfo.getStatusSettlement().intValue()!= SettlementEnum.SETTLEMENT_IN_PROGRESS.code){
                //提交结算信息不存在!
                throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":结算单非结算中!");
            }

        }
        //查询 数据保存LOG
        LambdaQueryWrapper<RPayablePlanSettlementInstallmentEntity> lambdalog = Wrappers.<RPayablePlanSettlementInstallmentEntity>lambdaQuery()
                .in(RPayablePlanSettlementInstallmentEntity::getFkPayablePlanId,listConfirm.stream().map(o->o.getPayablePlanId()).collect(Collectors.toList()))
                .in(RPayablePlanSettlementInstallmentEntity::getStatusSettlement, Arrays.asList(1,2));
        List<RPayablePlanSettlementInstallmentEntity> allLog = rPayablePlanSettlementInstallmentMapper.selectList(lambdalog);




        //判断是否需要修改分期结算单 合同账号信息(不一致需要修改,一致则直接跳过)
        for(ConfirmSettlementInfo confirmSettlementInfo:listConfirm){
            if (confirmDto.getTargetAgentContractAccountId().equals(confirmSettlementInfo.getFkAgentContractAccountId())) {
                continue;
            }
            LambdaQueryWrapper<RPayablePlanSettlementInstallmentEntity> lambdaQueryWrapper = Wrappers.<RPayablePlanSettlementInstallmentEntity>lambdaQuery()
                    .eq(RPayablePlanSettlementInstallmentEntity::getFkPayablePlanId, confirmSettlementInfo.getPayablePlanId())
                    .eq(RPayablePlanSettlementInstallmentEntity::getStatusSettlement, confirmSettlementInfo.getStatusSettlement());
            if (ObjectUtil.isNotEmpty(confirmSettlementInfo.getFkAgentContractAccountId())) {
                lambdaQueryWrapper.eq(RPayablePlanSettlementInstallmentEntity::getFkAgentContractAccountId, confirmSettlementInfo.getFkAgentContractAccountId());
            } else {
                lambdaQueryWrapper.isNull(RPayablePlanSettlementInstallmentEntity::getFkAgentContractAccountId);
            }
            List<RPayablePlanSettlementInstallmentEntity> sourcePayablePlanSettlementInstallments = rPayablePlanSettlementInstallmentMapper.selectList(lambdaQueryWrapper);
            if(ObjectUtil.isEmpty(sourcePayablePlanSettlementInstallments)){
                //提交结算信息不存在!
                throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":结算单不存在!");
            }
            List<Long> settlementIds = sourcePayablePlanSettlementInstallments.stream().map(RPayablePlanSettlementInstallmentEntity::getId).collect(Collectors.toList());






            //查询待支付信息预处理
            List<RPayablePlanSettlementInstallmentEntity> targetPayablePlanSettlementInstallments = rPayablePlanSettlementInstallmentMapper.selectList(Wrappers.<RPayablePlanSettlementInstallmentEntity>lambdaQuery()
                    .eq(RPayablePlanSettlementInstallmentEntity::getFkPayablePlanId, confirmSettlementInfo.getPayablePlanId())
                    .eq(RPayablePlanSettlementInstallmentEntity::getStatusSettlement, confirmSettlementInfo.getStatusSettlement())
                    .eq(RPayablePlanSettlementInstallmentEntity::getFkAgentContractAccountId, confirmDto.getTargetAgentContractAccountId()));

            RPayablePlanSettlementInstallmentEntity updatePayablePlanSettlementInstallment = new RPayablePlanSettlementInstallmentEntity();
            updatePayablePlanSettlementInstallment.setFkAgentContractAccountId(confirmDto.getTargetAgentContractAccountId());
            updatePayablePlanSettlementInstallment.setFkCurrencyTypeNum(confirmDto.getTargetAgentContractAccountNum());

            if (ObjectUtil.isNotEmpty(targetPayablePlanSettlementInstallments)) {
                RPayablePlanSettlementInstallmentEntity payablePlanSettlementInstallment = sourcePayablePlanSettlementInstallments.get(0);
                BigDecimal amountActual = payablePlanSettlementInstallment.getAmountActual();
                BigDecimal serviceFeeActual = payablePlanSettlementInstallment.getServiceFeeActual();
                RPayablePlanSettlementInstallmentEntity targetPayablePlanSettlementInstallment = targetPayablePlanSettlementInstallments.get(0);
                amountActual = amountActual.add(targetPayablePlanSettlementInstallment.getAmountActual());
                serviceFeeActual = serviceFeeActual.add(targetPayablePlanSettlementInstallment.getServiceFeeActual());
                updatePayablePlanSettlementInstallment.setAmountActual(amountActual);
                updatePayablePlanSettlementInstallment.setServiceFeeActual(serviceFeeActual);
                settlementIds.addAll(targetPayablePlanSettlementInstallments.stream().map(RPayablePlanSettlementInstallmentEntity::getId).collect(Collectors.toList()));
            }
            updatePayablePlanSettlementInstallment.setGmtModifiedUser(loginid);//小程序用户
            rPayablePlanSettlementInstallmentMapper.update(updatePayablePlanSettlementInstallment, Wrappers.<RPayablePlanSettlementInstallmentEntity>lambdaQuery().in(RPayablePlanSettlementInstallmentEntity::getId, settlementIds));

        }


        //提交结算逻辑
        LambdaQueryWrapper<RPayablePlanSettlementInstallmentEntity> payablePlanSettlementInstallmentLambdaQueryWrapper = Wrappers.<RPayablePlanSettlementInstallmentEntity>lambdaQuery();
        for (ConfirmSettlementInfo confirmSettlementInfo : listConfirm) {
            payablePlanSettlementInstallmentLambdaQueryWrapper.or(queryWrapper ->
                    queryWrapper.eq(RPayablePlanSettlementInstallmentEntity::getFkPayablePlanId, confirmSettlementInfo.getPayablePlanId())
                            .eq(RPayablePlanSettlementInstallmentEntity::getFkAgentContractAccountId, confirmDto.getTargetAgentContractAccountId())
                            .eq(RPayablePlanSettlementInstallmentEntity::getStatusSettlement, confirmSettlementInfo.getStatusSettlement()));
        }
        //检查是否有结算标记 结算银行账号
        List<RPayablePlanSettlementInstallmentEntity> settlementInstallmentList = rPayablePlanSettlementInstallmentMapper.selectList(payablePlanSettlementInstallmentLambdaQueryWrapper);
        if (settlementInstallmentList.stream().anyMatch(settlementInstallment -> ObjectUtil.isEmpty(settlementInstallment.getFkAgentContractAccountId()))) {
            throw new PartnerExceptionInfo(PartnerErrorEnum.CHECK_EXCEPTION.errorCode,PartnerErrorEnum.CHECK_EXCEPTION.errorMessage+":结算代理合同账户为空!");
        }

        for (ConfirmSettlementInfo confirmSettlementInfo : listConfirm) {
            Integer statusSettlement = confirmSettlementInfo.getStatusSettlement();
            Integer targetStatusSettlement= SettlementEnum.AGENT_CONFIRMATION.code;

            RPayablePlanSettlementInstallmentEntity payablePlanSettlementInstallment = new RPayablePlanSettlementInstallmentEntity();
            LambdaQueryWrapper<RPayablePlanSettlementInstallmentEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RPayablePlanSettlementInstallmentEntity::getFkPayablePlanId, confirmSettlementInfo.getPayablePlanId())
                    .eq(RPayablePlanSettlementInstallmentEntity::getStatusSettlement, statusSettlement)
                    .eq(RPayablePlanSettlementInstallmentEntity::getFkAgentContractAccountId, confirmDto.getTargetAgentContractAccountId());
            List<RPayablePlanSettlementInstallmentEntity> payablePlanSettlementInstallments = rPayablePlanSettlementInstallmentMapper.selectList(wrapper);

            List<Long> settlementIds = payablePlanSettlementInstallments.stream().map(RPayablePlanSettlementInstallmentEntity::getId).collect(Collectors.toList());

            BigDecimal amountActual = payablePlanSettlementInstallments.get(0).getAmountActual();
            BigDecimal serviceFeeActual = payablePlanSettlementInstallments.get(0).getServiceFeeActual();


            List<RPayablePlanSettlementInstallmentEntity> targetSettlementInstallments = rPayablePlanSettlementInstallmentMapper.selectList(Wrappers.<RPayablePlanSettlementInstallmentEntity>lambdaQuery()
                    .eq(RPayablePlanSettlementInstallmentEntity::getFkPayablePlanId, confirmSettlementInfo.getPayablePlanId())
                    .eq(RPayablePlanSettlementInstallmentEntity::getFkAgentContractAccountId, confirmDto.getTargetAgentContractAccountId())
                    .eq(RPayablePlanSettlementInstallmentEntity::getStatusSettlement, targetStatusSettlement));
            if (ObjectUtil.isNotEmpty(targetSettlementInstallments)) {
                RPayablePlanSettlementInstallmentEntity targersettlementInstallment = targetSettlementInstallments.get(0);
                amountActual = amountActual.add(targersettlementInstallment.getAmountActual());
                serviceFeeActual = serviceFeeActual.add(targersettlementInstallment.getServiceFeeActual());
                settlementIds.addAll(targetSettlementInstallments.stream().map(RPayablePlanSettlementInstallmentEntity  ::getId).collect(Collectors.toList()));

            }
            payablePlanSettlementInstallment.setAmountActual(amountActual);
            payablePlanSettlementInstallment.setServiceFeeActual(serviceFeeActual);
            payablePlanSettlementInstallment.setStatusSettlement(targetStatusSettlement);
            payablePlanSettlementInstallment.setAccountExportTime(LocalDateTime.now());
            payablePlanSettlementInstallment.setRollBackTime(MyDateUtils.nextMonth().toLocalDateTime());
            payablePlanSettlementInstallment.setGmtModifiedUser(loginid);
            rPayablePlanSettlementInstallmentMapper.update(payablePlanSettlementInstallment, Wrappers.<RPayablePlanSettlementInstallmentEntity>lambdaUpdate().in(RPayablePlanSettlementInstallmentEntity::getId, settlementIds));

            for (Long settlementId : settlementIds) {
                RPayablePlanSettlementStatusEntity payablePlanSettlementStatus = new RPayablePlanSettlementStatusEntity();
                payablePlanSettlementStatus.setFkPayablePlanId(confirmSettlementInfo.getPayablePlanId());
                payablePlanSettlementStatus.setStatusSettlement(targetStatusSettlement);
                payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(settlementId);
                payablePlanSettlementStatus.setGmtModifiedUser(loginid);
                //保存结算单 信息
                rPayablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
                for(RPayablePlanSettlementInstallmentEntity settlementInstallment: payablePlanSettlementInstallments){
                    RSettlementBillSettlementInstallmentEntity batchTmp=new RSettlementBillSettlementInstallmentEntity();
                    batchTmp.setFkPayablePlanSettlementInstallmentId(settlementId);
                    batchSettlementBill.add(batchTmp);
                }
            }
        }

        //删除该代理-删除佣金结算第二步标记  该数据表已经没有使用了,然后出现了
        LambdaQueryWrapper<RPayablePlanSettlementFlagEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RPayablePlanSettlementFlagEntity::getStatusSettlement, SettlementEnum.SETTLEMENT_IN_PROGRESS.code).eq(RPayablePlanSettlementFlagEntity::getFkAgentId, agentId);
        rPayablePlanSettlementFlagMapper.delete(lambdaQueryWrapper);


        MSettlementBillEntity msettlementBillEntity = new MSettlementBillEntity();
        msettlementBillEntity.setFkAgentId(agentId);
        msettlementBillEntity.setFkAgentContractAccountId(confirmDto.getTargetAgentContractAccountId());
        msettlementBillEntity.setFkCurrencyTypeNum(confirmDto.getTargetAgentContractAccountNum());
        msettlementBillEntity.setGmtCreateUser(userinfo.getPartnerUserId().toString());
        msettlementBillEntity.setGmtCreate(LocalDateTime.now());
        mSettlementBillMapper.insert(msettlementBillEntity);

        List<MSettlementBillItemEntity> savebillItem=new ArrayList();
        BigDecimal amount=new BigDecimal(0);//总金额
        for(ConfirmSettlementInfo settlementInfo:listConfirm){
            MSettlementBillItemEntity mSettlementBillItemEntity = new MSettlementBillItemEntity();
            mSettlementBillItemEntity.setFkSettlementBillId(msettlementBillEntity.getId());
            mSettlementBillItemEntity.setFkPayablePlanId(settlementInfo.getPayablePlanId());
            RStudentUuidEntity studentUUIDTmp=rStudentmap.get(settlementInfo.getStudentUUID());
            mSettlementBillItemEntity.setFkStudentId(studentUUIDTmp.getFkStudentId());
            mSettlementBillItemEntity.setAmountActual(settlementInfo.getAmountActual());
            mSettlementBillItemEntity.setServiceFeeActual(settlementInfo.getServiceFeeActual());

            //获取汇率
            UExchangeRateEntity entityWebExchange=getLastRate(settlementInfo.getFkCurrencyTypeNum(),confirmDto.getTargetAgentContractAccountNum());
            if(ObjectUtil.isNotEmpty(entityWebExchange) &&  ObjectUtil.isEmpty(entityWebExchange.getId())){
                entityWebExchange.setGetDate(LocalDate.now());
                entityWebExchange.setGmtCreateUser(userinfo.getPartnerUserId().toString());
                uExchangeRateMapper.insert(entityWebExchange);
            }
            if(ObjectUtil.isNotEmpty(entityWebExchange)){
                BigDecimal exchangeRate=entityWebExchange.getExchangeRate();
                BigDecimal amountExchange =settlementInfo.getAmountActual();
                BigDecimal serviceFeeExchange=settlementInfo.getServiceFeeActual();

                amountExchange=amountExchange.multiply(exchangeRate);
                serviceFeeExchange=serviceFeeExchange.multiply(exchangeRate);

                mSettlementBillItemEntity.setExchangeRate(entityWebExchange.getExchangeRate());
                mSettlementBillItemEntity.setAmountExchange(amountExchange);
                mSettlementBillItemEntity.setServiceFeeExchange(serviceFeeExchange);

            }


            /*mSettlementBillItemEntity.setExchangeRate(settlementInfo.getExchangeRate());
            mSettlementBillItemEntity.setAmountExchange(settlementInfo.getAmountExchange());
            mSettlementBillItemEntity.setServiceFeeExchange(settlementInfo.getServiceFeeExchange());*/
            amount=amount.add(settlementInfo.getAmountExchange());
            amount=amount.add(settlementInfo.getServiceFeeExchange());

            mSettlementBillItemEntity.setFkCurrencyTypeNum(settlementInfo.getFkCurrencyTypeNum());
            mSettlementBillItemEntity.setGmtCreateUser(userinfo.getPartnerUserId().toString());
            mSettlementBillItemEntity.setGmtCreate(LocalDateTime.now());
            savebillItem.add(mSettlementBillItemEntity);
        }
        msettlementBillEntity.setAmount(amount);
        mSettlementBillMapper.updateById(msettlementBillEntity);//结算单
        mSettlementBillItemMapper.insertOrUpdate(savebillItem);//结算单明细

        for(RSettlementBillSettlementInstallmentEntity settlementInstallment:batchSettlementBill) {
            settlementInstallment.setFkSettlementBillId(msettlementBillEntity.getId());
            settlementInstallment.setGmtCreate(LocalDateTime.now());
            settlementInstallment.setGmtCreateUser(userinfo.getPartnerUserId().toString());
            settlementInstallment.setGmtCreate(LocalDateTime.now());
        }
        rSettlementBillSettlementInstallmentMapper.insertOrUpdate(batchSettlementBill);

        for(RPayablePlanSettlementInstallmentEntity installmentEntity:allLog){
            LogSettlementBillSettlementInstallmentDetailEntity logSave=
                    BeanCopyUtils.objClone(installmentEntity, LogSettlementBillSettlementInstallmentDetailEntity::new);
            logSave.setFkSettlementBillId(msettlementBillEntity.getId());
            logSave.setFkPayablePlanSettlementInstallmentId(installmentEntity.getId());
            logSave.setId(null);
            logSettlementBill.add(logSave);
        }
        rSettlementBillSettlementInstallmentDetailMapper.insertOrUpdate(logSettlementBill);//结算单日志保存


        if(ObjectUtil.isNotEmpty(fileinfo)){
            SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
            sMediaAndAttachedEntity.setFkFileGuid(fileinfo.getFileGuid());
            sMediaAndAttachedEntity.setFkTableName("m_settlement_bill");
            sMediaAndAttachedEntity.setFkTableId(msettlementBillEntity.getId());
            sMediaAndAttachedEntity.setGmtCreate(LocalDateTime.now());
            sMediaAndAttachedEntity.setGmtCreateUser(userinfo.getPartnerUserId().toString());
            sMediaAndAttachedEntity.setRemark("结算签名附件");
            //结算单签名
            sMediaAndAttachedMapper.insertSelective(sMediaAndAttachedEntity);
        }

        if(ObjectUtil.isNotEmpty(confirmDto.getSignature())){
            RSettlementSignatureEntity rSettlementSignatureEntity=new RSettlementSignatureEntity();
            rSettlementSignatureEntity.setFkSettlementBillId(msettlementBillEntity.getId());
            rSettlementSignatureEntity.setSignature(confirmDto.getSignature());
            rSettlementSignatureEntity.setGmtCreate(LocalDateTime.now());
            rSettlementSignatureEntity.setGmtCreateUser(userinfo.getPartnerUserId().toString());
            rSettlementSignatureMapper.insert(rSettlementSignatureEntity);
        }
        if(ObjectUtil.isNotEmpty(msettlementBillEntity)){
            resultvalue=msettlementBillEntity.getId();
        }
        return resultvalue;
    }


    @Override
    public BigDecimal getAmountConvet(AmountConvetParamsDto paramsDto) {
        BigDecimal amountResult=paramsDto.getAmountRbm();

        FzhUser fzhUser= SecurityUtils.getUser();
        UserInfoParams userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());

        //默认币种
        String defaultCurrency=getCurrencyDefault(userinfo.getCompanyId(),"PARTNER","PARTNER_COMPANY_DEFAULT_CURRENCY");

        if(defaultCurrency.equals(paramsDto.getToCurrencyNum())){
            return amountResult;
        }
        BigDecimal exchangeRate_redis=UserInfoParamsUtils.getExchangeRate(ConfigTypeEnum.M_EXCHANGERATE_HASH.uNewsType,
                defaultCurrency,paramsDto.getToCurrencyNum());
        if(exchangeRate_redis.compareTo(BigDecimal.ONE) == 0){

            //获取汇率
            UExchangeRateEntity entityWebExchange=getLastRate(defaultCurrency,paramsDto.getToCurrencyNum());
            if(ObjectUtil.isNotEmpty(entityWebExchange) &&  ObjectUtil.isEmpty(entityWebExchange.getId())){
                entityWebExchange.setGetDate(LocalDate.now());
                entityWebExchange.setGmtCreateUser(userinfo.getPartnerUserId().toString());
                uExchangeRateMapper.insert(entityWebExchange);
            }
            if(ObjectUtil.isNotEmpty(entityWebExchange)){
                amountResult=amountResult.multiply(entityWebExchange.getExchangeRate());
                UserInfoParamsUtils.setExchangeRate(ConfigTypeEnum.M_EXCHANGERATE_HASH.uNewsType,
                        defaultCurrency,paramsDto.getToCurrencyNum(),entityWebExchange.getExchangeRate());

            }
        }else{
            amountResult=amountResult.multiply(exchangeRate_redis);
        }

        return amountResult;
    }


    @Override
    @DSTransactional
    public void testDSsourceshiwu(TeamMemberVo params) {
        FzhUser fzhUser= SecurityUtils.getUser();

        if(params.getUserId()==null && params.getName()!=null && params.getName().length()>0){
            SystemUserEntity poEntity=systemUserMapper.selectOne(new LambdaQueryWrapper<SystemUserEntity>().eq(SystemUserEntity::getName,params.getName())
                    ,false
            );
            if(ObjectUtil.isNotEmpty(poEntity)){
                params.setUserId(poEntity.getId());
            }
        }
        UserInfoParams userinfo=UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),params.getUserId());


        Partnertest partpo1=new Partnertest();
        partpo1.setName("testpart1");
        partnertestMapper.insert(partpo1);
        Saletest po=new Saletest();
        po.setName("test");
        saletestMapper.insert(po);

        Partnertest partpo=new Partnertest();
        partpo.setName("testpart2");
        partnertestMapper.insert(partpo);

    }


}
