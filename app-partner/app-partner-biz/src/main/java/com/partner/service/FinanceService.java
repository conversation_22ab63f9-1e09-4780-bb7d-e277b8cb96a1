package com.partner.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.TeamDataSumDto;
import com.partner.dto.finance.*;
import com.partner.entity.UExchangeRateEntity;
import com.partner.vo.TeamMemberVo;
import com.partner.vo.finance.AgentSettlementOfferItemResultVo;

import java.math.BigDecimal;
import java.util.List;


public interface FinanceService {

    /**
     * @desc 确认名单列表
     * @return
     */
    IPage getCommissionAffirmPage(Page page, CommissionSearchParamsDto dto);

    /**
     * @desc 名单确认
     * @return
     */
    void affirmOfferItem( CommissionAffirmParamsDto paramsDto);

    /**
     * @desc 可结算信息
     * @return
     */
    AgentSettlementOfferItemResultVo getAgentSettlementPage(SettlementSearchParamsDto dto);
    /**
     * @desc 结算信息校验
     * @return
     */
    Boolean getValidSettlement();

    Long agentConfirmSettlement(ConfirmSettlementDto confirmInfo);

    /**
     * @desc 金额转换-按币种
     * @return
     */
    BigDecimal getAmountConvet(AmountConvetParamsDto paramsDto);

    /**
     * @desc 金额转换-默认币种金额
     * @return
     */
    String getCurrencyDefault(Long companyId,String fkPlatformCode,String configKey);
    UExchangeRateEntity getLastRate(String fromCurrency, String toCurrency);
    void testDSsourceshiwu(TeamMemberVo params);
}
