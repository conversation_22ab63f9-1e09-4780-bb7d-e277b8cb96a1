package com.partner.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.partner.dto.MInstitutionRankingParamsDto;
import com.partner.entity.MInstitutionRankingEntity;
import com.partner.eunms.ConfigTypeEnum;
import com.partner.service.MInstitutionRankingService;
import com.partner.mapper.MInstitutionRankingMapper;
import com.partner.util.MyDateUtils;
import com.partner.util.TencentCloudUtils;
import com.partner.vo.MInstitutionRankingVo;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.combox.CourseTypeGroupCombox;
import com.partner.vo.combox.InstitutionTypeCombox;
import com.partner.vo.combox.RankingTypeCombox;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_institution_ranking】的数据库操作Service实现
* @createDate 2024-12-23 19:15:17
*/
@Service
@AllArgsConstructor
public class MInstitutionRankingServiceImpl extends ServiceImpl<MInstitutionRankingMapper, MInstitutionRankingEntity>
    implements MInstitutionRankingService{

    private final MInstitutionRankingMapper mInstitutionRankingMapper;
    private final TencentCloudUtils tencentCloudUtils;

    @Override
    public List<CountryCombox> getCountryCombox(MInstitutionRankingParamsDto dto) {
        List<CountryCombox> resultCountryCombox = mInstitutionRankingMapper.getCountryCombox(dto);

        return resultCountryCombox;
    }

    @Override
    public List<InstitutionTypeCombox> getInstitutionTypeCombox(MInstitutionRankingParamsDto dto) {
        return mInstitutionRankingMapper.getInstitutionTypeCombox(dto);
    }

    @Override
    public List<CourseTypeGroupCombox> getCourseTypeGroupCombox(MInstitutionRankingParamsDto dto) {
        return mInstitutionRankingMapper.getCourseTypeGroupCombox(dto);
    }


    @Override
    public List<RankingTypeCombox> getRankingCombox(MInstitutionRankingParamsDto dto) {
        List<RankingTypeCombox>  resultRankingCombox=mInstitutionRankingMapper.getRankingCombox(dto);
        return resultRankingCombox;
    }

    @Override
    public IPage getPageInstitutionRanking(Page page, MInstitutionRankingParamsDto dto) {
        if(dto.getYear()==null){
            dto.setYear(MyDateUtils.getYear(new Date()));//获取当年
        }
        String baseurl=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        dto.setMMageAddress(baseurl);
        IPage<MInstitutionRankingVo> rankingVo = mInstitutionRankingMapper.getPageInstitutionRanking(page, dto);


        return rankingVo;
    }


}




