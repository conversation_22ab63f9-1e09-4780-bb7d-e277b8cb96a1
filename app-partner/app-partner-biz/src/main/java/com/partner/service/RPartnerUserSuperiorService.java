package com.partner.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.entity.RPartnerUserSuperiorEntity;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【r_partner_user_superior】的数据库操作Service
 * @createDate 2025-01-14 21:05:44
 */
public interface RPartnerUserSuperiorService extends IService<RPartnerUserSuperiorEntity> {


    /**
     * 保存伙伴用户上级
     *
     * @param partnerUserId
     * @param superiorIds
     */
    void savePartnerUserSuperior(Long partnerUserId, List<Long> superiorIds);


    /**
     * 获取伙伴用户下级
     *
     * @param partnerUserId
     * @return
     */
    Set<Long> getAllSubUserIds(Long partnerUserId);

    /**
     * 获取伙伴用户上级
     *
     * @param partnerUserId
     * @return
     */
    Set<Long> getAllSuperiorUserIds(Long partnerUserId);

    /**
     * 删除缓存-上级
     *
     * @param partnerUserId
     */
    void deleteSupCache(Long partnerUserId);
}
