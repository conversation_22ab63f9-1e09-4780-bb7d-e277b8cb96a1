package com.partner.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.ReadingSignDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.eunms.ConfigTypeEnum;
import com.partner.service.ReadingSignService;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.ReadingSignNumVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class ReadingSignServiceImpl implements ReadingSignService {
    private final RedisTemplate redisTemplate;


    @Override
    public ReadingSignNumVo getNoReadingType() {
        ReadingSignNumVo readingSignNumVo = new ReadingSignNumVo();

        FzhUser user = SecurityUtils.getUser();
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()),
                user.getFkFromPlatformCode(), user.getId());


        String mappstudenthash= ConfigTypeEnum.M_APP_STUDENT_HASH.uNewsType+":"+userInfoParams.getPartnerUserId();
        Object cachedDataDraft=redisTemplate.opsForHash().get(mappstudenthash,ConfigTypeEnum.M_APP_STUDENT_DRAFT.uNewsType);
        if(ObjectUtils.isNotEmpty(cachedDataDraft) && cachedDataDraft instanceof Integer){
            int typevalue= Integer.parseInt(cachedDataDraft.toString());
            if(typevalue==1){
                readingSignNumVo.setMappStudentDraft(1);
            }else{
                readingSignNumVo.setMappStudentDraft(0);
            }
        }

        Object cachedDataCheck=redisTemplate.opsForHash().get(mappstudenthash,ConfigTypeEnum.M_APP_STUDENT_CHECK.uNewsType);
        if(ObjectUtils.isNotEmpty(cachedDataCheck) && cachedDataCheck instanceof Integer){
            int typevalue= Integer.parseInt(cachedDataCheck.toString());
            if(typevalue==1){
                readingSignNumVo.setMappStudentCheck(1);
            }else{
                readingSignNumVo.setMappStudentCheck(0);
            }
        }

        return readingSignNumVo;
    }

    @Override
    public void readingSingn(ReadingSignDto params) {
        String type=params.getType();

        FzhUser user = SecurityUtils.getUser();
        UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()),
                user.getFkFromPlatformCode(), user.getId());

        String mappstudenthash= ConfigTypeEnum.M_APP_STUDENT_HASH.uNewsType+":"+userInfoParams.getPartnerUserId();
        if(StringUtils.isNotBlank(type) && ConfigTypeEnum.M_APP_STUDENT_DRAFT.uNewsType.equals(type)){
            //学生草稿阅读，取消标识
            redisTemplate.opsForHash().delete(mappstudenthash,ConfigTypeEnum.M_APP_STUDENT_DRAFT.uNewsType);
        }else if(StringUtils.isNotBlank(type) && ConfigTypeEnum.M_APP_STUDENT_CHECK.uNewsType.equals(type)){
            //学生审核中标识
            redisTemplate.opsForHash().delete(mappstudenthash,ConfigTypeEnum.M_APP_STUDENT_CHECK.uNewsType);
        }else {
            //学生草稿阅读，取消标识
            redisTemplate.opsForHash().delete(mappstudenthash,ConfigTypeEnum.M_APP_STUDENT_DRAFT.uNewsType);
            //学生审核中标识
            redisTemplate.opsForHash().delete(mappstudenthash,ConfigTypeEnum.M_APP_STUDENT_CHECK.uNewsType);

        }

    }


}
