package com.partner.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.S3Object;
import com.common.file.core.FileTemplate;
import com.partner.config.FileProperties;
import com.partner.eunms.FileUploadEnum;
import com.partner.exception.PartnerExceptionInfo;
import com.partner.mapper.DynamicFileMapper;
import com.partner.service.PartnerFileService;
import com.partner.util.AppendixUtils;
import com.partner.vo.file.UploadFileParam;
import com.partner.vo.file.UploadFileVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * @Author:Oliver
 * @Date: 2025/7/18
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PartnerFileServiceImpl implements PartnerFileService {

    private final DynamicFileMapper dynamicFileMapper;
    private final FileTemplate fileTemplate;
    private final FileProperties fileProperties;

    @Override
    public UploadFileVo upload(MultipartFile file, Boolean isPrivate, String code,String customerFileName) {
        FileUploadEnum fileUploadEnum = FileUploadEnum.getEnumByCode(code);
        if (Objects.isNull(fileUploadEnum)) {
            log.error("无效的上传文件Code:{}", code);
            throw new PartnerExceptionInfo(500, "无效的文件Code");
        }
        log.info("===>上传文件-接收到的原文件名:{}", file.getOriginalFilename());
        log.info("===>上传文件-自定义文件名:{}", customerFileName);
        //fileKey-文件夹+文件名
        String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
        if (isPrivate) {
            //没有拼根目录的地址
            fileName = getPrivateFilePath(file);
        }
        log.info("uuid形式的fileName:{}", fileName);
        log.info("原文件名:{}", file.getOriginalFilename());
        String catalogue = isPrivate ? fileProperties.getPrivateCatalogue() : fileProperties.getCatalogue() + "/";
        String filePath = catalogue + fileName;
        log.info("filePath:{}", filePath);
        //文件guid
        String fileGuid = IdUtil.simpleUUID().replaceAll("-", "");
        //源文件类型
        String fileTypeOrc = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")).toLowerCase();
        log.info("fileTypeOrc:{}", fileTypeOrc);

        try (InputStream inputStream = file.getInputStream()) {
            String bucketName = isPrivate ? fileProperties.getPrivateBucketName() : fileProperties.getPublicBucketName();
            log.info("bucketName:{},filePath:{}", bucketName, filePath);
            fileTemplate.putObject(bucketName, filePath, inputStream, file.getContentType());
        } catch (Exception e) {
            log.error("上传失败", e);
            throw new RuntimeException("上传文件失败");
        }

        //构造保存文件信息
        UploadFileVo uploadFile = UploadFileVo.builder()
                .fileGuid(fileGuid)
                .fileTypeOrc(fileTypeOrc)
                .fileNameOrc(StringUtils.isBlank(customerFileName)?file.getOriginalFilename():customerFileName)
                .fileName(StringUtils.isBlank(customerFileName)?file.getOriginalFilename():customerFileName)
                .filePath("/" + filePath)
                .fileKey("/" + filePath)
                .gmtCreate(new Date())
                .build();

        //上传到文件库
        UploadFileParam uploadFileParam = UploadFileParam.builder()
                .fileDb(StringUtils.substringBefore(fileUploadEnum.getFileCenter(), "."))
                .fileTable(StringUtils.substringAfter(fileUploadEnum.getFileCenter(), "."))
                .mediaDb(StringUtils.substringBefore(fileUploadEnum.getMediaTable(), "."))
                .mediaTable(StringUtils.substringAfter(fileUploadEnum.getMediaTable(), "."))
                .tableId(0L)
                .tableName("")
                .typeKey("")
                .mediaInfo(uploadFile)
                .uploadTogether(fileUploadEnum.getUploadTogether())
                .build();
        // 保存文件
        saveFileAndMedia(uploadFileParam,true);
        return uploadFile;
    }

    @Override
    public void getFile(String bucket, String fileKey, HttpServletResponse response, Boolean isPrivate) {
        fileKey = StringUtils.substringAfter(fileKey, "/");
        if (StrUtil.isBlank(bucket)) {
            bucket = fileProperties.getPublicBucketName();
        }
        if (isPrivate) {
            bucket = fileProperties.getPrivateBucketName();
        }
        try (S3Object s3Object = fileTemplate.getObject(bucket, fileKey)) {
            response.setContentType("application/octet-stream; charset=UTF-8");
            IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
        } catch (Exception e) {
            log.error("文件读取异常: {}", e.getLocalizedMessage());
            throw new RuntimeException("文件下载失败");
        }
    }

    @Override
    public InputStream getFileStream(String bucket, String fileKey, Boolean isPrivate) {
        fileKey = StringUtils.substringAfter(fileKey, "/");
        if (StrUtil.isBlank(bucket)) {
            bucket = fileProperties.getPublicBucketName();
        }
        if (isPrivate) {
            bucket = fileProperties.getPrivateBucketName();
        }
        try {
            // 从 S3 获取对象（你可根据 fileTemplate 的类型做细节封装）
            S3Object s3Object = fileTemplate.getObject(bucket, fileKey);
            return s3Object.getObjectContent();
        } catch (Exception e) {
            log.error("获取文件流失败: bucket={}, key={}, error={}", bucket, fileKey, e.getMessage(), e);
            throw new RuntimeException("文件流获取失败", e);
        }
    }

    @Override
    public String getFileBase64(String bucket, String fileKey, Boolean isPrivate) {
        try {
            InputStream fileStream = getFileStream(bucket, fileKey, isPrivate);
            byte[] bytes = IOUtils.toByteArray(fileStream);
            return Base64.encodeBase64String(bytes);
        } catch (Exception e) {
            log.error("获取文件 Base64 编码失败: bucket={}, key={}, error={}", bucket, fileKey, e.getMessage(), e);
            throw new RuntimeException("文件 Base64 获取失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFileAndMedia(UploadFileParam param,Boolean saveFileCenter) {
        // 参数校验
        if (Objects.isNull(param)) {
            log.error("参数不能为空:{}", JSONObject.toJSONString(param));
            return;
        }
        // 调用统一填充方法
        fillDefaultValues(param);
        UploadFileVo vo = param.getMediaInfo();
        // 插入文件记录
        if (saveFileCenter){
            dynamicFileMapper.insertFileRecord(param, vo);
            log.info("插入文件记录成功:{}", JSONObject.toJSONString(param));
        }
        // 插入媒体记录
        if (Objects.nonNull(param.getUploadTogether()) && param.getUploadTogether()
                && StringUtils.isNotBlank(param.getMediaDb()) && StringUtils.isNotBlank(param.getMediaTable())) {
            dynamicFileMapper.insertMediaRecord(param);
            log.info("插入媒体记录成功:{}", JSONObject.toJSONString(param.getMediaInfo()));
        }
    }

    private static void fillDefaultValues(UploadFileParam param) {
        Date now = new Date();
        String defaultUser = "admin-iae";

        // 填充 UploadFileParam 字段
        if (Objects.isNull(param.getGmtCreateUser())) {
            param.setGmtCreate(now);
        }
        if (StringUtils.isBlank(param.getGmtCreateUser())) {
            param.setGmtCreateUser(defaultUser);
        }

        // 填充内部 vo 字段
        UploadFileVo vo = param.getMediaInfo();
        if (vo != null) {
            if (Objects.isNull(vo.getGmtCreate())) {
                vo.setGmtCreate(param.getGmtCreate());
            }
            if (StringUtils.isBlank(vo.getGmtCreateUser())) {
                vo.setGmtCreateUser(param.getGmtCreateUser());
            }
        }
    }

    public static String getPrivateFilePath(MultipartFile file) {
        String fileFileName = file.getOriginalFilename();
        String dateString = AppendixUtils.getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        return dateString + fileName;
    }

}
