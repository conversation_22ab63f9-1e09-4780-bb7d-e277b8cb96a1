package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.entity.MFilePartnerEntity;
import com.partner.entity.MFilePlatformEntity;
import com.partner.service.ITencentCloudService;
import com.partner.service.MFilePlatformService;
import com.partner.mapper.MFilePlatformMapper;
import com.partner.util.AppendixUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【m_file_platform】的数据库操作Service实现
* @createDate 2025-05-22 14:14:22
*/
@Service
public class MFilePlatformServiceImpl extends ServiceImpl<MFilePlatformMapper, MFilePlatformEntity>
    implements MFilePlatformService{

    @Resource
    private ITencentCloudService tencentCloudService;
    @Resource
    private MFilePlatformMapper filePlatformMapper;


    //图片存储地址-公开桶
    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    //文件存储
    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;

    @Override
    public List<MFilePlatformEntity> uploadAppendix(MultipartFile[] files, Boolean isPub, Boolean isPartnerPath) {
        List<MFilePlatformEntity> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = AppendixUtils.getFilePath(file);
            String bucketName = fileBucketName;//私有桶
            if(isPub){
                fileurl = AppendixUtils.getFileHtiPath(file);//公开桶
                if(isPartnerPath){
                    fileurl = AppendixUtils.getPartHtiPath(file);
                }
                bucketName=imageBucketName;//公开桶
            }
            MFilePlatformEntity fileVo = saveFileInfo(bucketName, file, fileurl, getAppendixPerfix(),isPub);
            datas.add(fileVo);

        }
        return datas;
    }


    @Transactional(rollbackFor = Exception.class)
    public MFilePlatformEntity saveFileInfo(String bucketName, MultipartFile file, String fileurl, String perfix, boolean isPub) {
        MFilePlatformEntity filePlatformEntity = null;
        if (ObjectUtil.isNotEmpty(perfix)) {
            fileurl = perfix + fileurl;
        }
        String filename = file.getOriginalFilename();
        int i = filename.lastIndexOf(".");
        int j = fileurl.lastIndexOf("/");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //获取目标文件名称
        String targetFileName = fileurl.substring(j + 1, fileurl.length());
        String ossPath = subString(fileurl);//如：/m_file_finance/files/2021/09/07/67db88c4-e052-42be-b13f-de487fd7aa98.jpg


        filePlatformEntity = new MFilePlatformEntity();
        filePlatformEntity.setFilePath(fileurl);
        filePlatformEntity.setFileNameOrc(filename);
        filePlatformEntity.setFileTypeOrc(substring);
        filePlatformEntity.setFileName(targetFileName);
        filePlatformEntity.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
        filePlatformEntity.setGmtCreate(LocalDateTime.now());
        FzhUser user = SecurityUtils.getUser();
        if(ObjectUtil.isNotEmpty(user)){
            String loginId = user.getLoginId();
            filePlatformEntity.setGmtCreateUser(loginId);
        }


        tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
        filePlatformEntity.setFileKey(ossPath);
        filePlatformMapper.insert(filePlatformEntity);
        return filePlatformEntity;
    }
    static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/app-partner/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }
    private String getAppendixPerfix() {

        return "/data/project/get/app-partner/target";
    }
}




