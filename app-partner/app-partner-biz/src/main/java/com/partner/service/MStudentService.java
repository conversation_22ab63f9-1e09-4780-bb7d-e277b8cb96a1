package com.partner.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.base.CountryStateCityParamsDto;
import com.partner.dto.student.MStudentAddOrEditDto;
import com.partner.dto.student.MStudentParamsDetailDto;
import com.partner.dto.student.MStudentParamsDto;
import com.partner.dto.student.MStudentSubAndAddOrEditDto;
import com.partner.entity.MStudentEntity;
import com.partner.vo.base.CountryBaseCombox;
import com.partner.vo.combox.StudentOfferItemCourtyCombox;
import com.partner.vo.combox.StudentOfferItemStepCombox;
import com.partner.vo.student.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【m_student】的数据库操作Service
* @createDate 2024-12-05 19:03:19
*/
public interface MStudentService extends IService<MStudentEntity> {

    /**
     * 查询学生  申请计划国家
     * @param dto
     * @return
     */
    List<StudentOfferItemCourtyCombox> getCountryComboxPeople(MStudentParamsDto dto);

    /**
     * 查询申请计划国家
     * @param dto
     * @return
     */
    List<StudentOfferItemCourtyCombox> getCountryComboxApply(MStudentParamsDto dto);


    /**
     * 查询 学生 申请步骤数量
     * @param dto
     * @return
     */
    List<StudentOfferItemStepCombox> getAllStepOrderApplyNums(MStudentParamsDto dto);

    /**
     * 学生集合
     * @param page
     * @param dto
     * @return
     */
    IPage getPeopleStudentList(Page page,MStudentParamsDto dto);

    /**
     * 学生 申请集合
     * @param page
     * @param dto
     * @return
     */
    IPage getApplyStudentList(Page page,MStudentParamsDto dto);

    /**
     * 首页学生
     * @return
     */
    List<MStudentHomeVo>  getHomeStudents();

    /**
     * 学生明细
     * @param dto
     * @return
     */
    public StudentDetailInfoVo getApplyStudentsDetail(MStudentParamsDetailDto dto);

    /**
     * 申请明细
     * @param dto
     * @return
     */
    public StudentOfferItemDetailVo getApplyDetail(MStudentParamsDetailDto dto);

    /**
     * 申请 学生 统计
     * @param dto
     * @return
     */
    StudentPlanSumVo getApplyStudentsPlanSum(MStudentParamsDto dto);

    /**
     * 月度 学生申请 统计
     * @param dto
     * @return
     */
    StudentMonthPlanSumVo getMonthStudentsApplySum(MStudentParamsDto dto);


    /**
     * 月度  新增统计
     * @return
     */
    StudentPlanSumMonthVo  getMonthNewSum();

    /**
     * 学生 -申请国家统计
     * @param dto
     * @return
     */
    StudentApplySumVo getApplyStudentsCountry(MStudentParamsDto dto);

    /**
     * 月度 学生申请数量
     * @param dto
     * @return
     */
    Map<String,List<StudentMonthOfferVo>> getMonthApplayTotal(MStudentParamsDto dto);

    /**
     * 院校 学生申请排名
     * @param dto
     * @return
     */
    List<StudentApplyRankingVo> getStudentsApplyInstitutionRanking(MStudentParamsDto dto);

    /**
     * 新增学生
     * @param dto
     * @return
     */
    Long addStudents(MStudentAddOrEditDto dto);

    /**
     * 修改学生
     * @param dto
     * @return
     */
    Long editStudents(MStudentAddOrEditDto dto);

    Map<String,String> getValidStudents(MStudentSubAndAddOrEditDto  dto);


    List<CountryBaseCombox> getCountryCombox(CountryStateCityParamsDto dto);


}
