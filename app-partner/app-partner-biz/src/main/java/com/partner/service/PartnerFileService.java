package com.partner.service;

import com.partner.vo.file.UploadFileParam;
import com.partner.vo.file.UploadFileVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;

/**
 * @Author:Oliver
 * @Date: 2025/7/18
 * @Version 1.0
 * @apiNote:文件服务-新上传方式
 */
public interface PartnerFileService {

    /**
     * 上传文件
     *
     * @param file      文件
     * @param isPrivate 是否私有
     * @return
     */
    UploadFileVo upload(MultipartFile file, Boolean isPrivate,String code,String customerFileName);

    /**
     * 获取文件-直接下载
     *
     * @param bucket    存储桶
     * @param fileName  文件名
     * @param response  响应
     * @param isPrivate 是否私有
     */
    void getFile(String bucket, String fileName, HttpServletResponse response, Boolean isPrivate);


    /**
     * 获取文件-流式下载
     *
     * @param bucket    存储桶
     * @param fileKey   文件key
     * @param isPrivate 是否私有
     * @return
     */
     InputStream getFileStream(String bucket, String fileKey, Boolean isPrivate);

     /**
     * 获取文件-base64
     *
     * @param bucket    存储桶
     * @param fileKey   文件key
     * @param isPrivate 是否私有
     * @return
     */
    String getFileBase64(String bucket, String fileKey, Boolean isPrivate);

    /**
     * 保存文件记录和媒体记录
     *
     * @param param
     */
    void saveFileAndMedia(UploadFileParam param,Boolean saveFileCenter);

}
