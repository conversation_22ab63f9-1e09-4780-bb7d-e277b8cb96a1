package com.partner.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.partner.entity.UExchangeRateEntity;
import com.partner.service.UExchangeRateService;
import com.partner.mapper.UExchangeRateMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【u_exchange_rate】的数据库操作Service实现
* @createDate 2025-01-20 14:14:23
*/
@Service
public class UExchangeRateServiceImpl extends ServiceImpl<UExchangeRateMapper, UExchangeRateEntity>
    implements UExchangeRateService{

}




