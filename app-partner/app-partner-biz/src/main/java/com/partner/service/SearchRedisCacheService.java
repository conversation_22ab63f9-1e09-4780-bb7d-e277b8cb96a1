package com.partner.service;

import com.partner.dto.TableNameDto;
import com.partner.dto.UserInfoCacheDto;
import com.partner.dto.base.UserInfoParams;

import java.util.List;
import java.util.Map;

public interface SearchRedisCacheService {

    UserInfoParams getUserInfoParams(UserInfoCacheDto params);


    List<Map<String,String>> getTableName(TableNameDto params);

    Object getRedisObject(TableNameDto params);
}
