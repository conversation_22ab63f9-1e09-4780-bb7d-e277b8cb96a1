package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.config.WechatApplet;
import com.partner.dto.MEventIncentiveParamsDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.MEventIncentiveEntity;
import com.partner.entity.REventIncentivePartnerUserReadEntity;
import com.partner.entity.REventPartnerUserReadEntity;
import com.partner.eunms.ConfigTypeEnum;
import com.partner.mapper.MEventIncentiveMapper;
import com.partner.mapper.REventIncentivePartnerUserReadMapper;
import com.partner.mapper.REventPartnerUserReadMapper;
import com.partner.service.MEventIncentiveService;
import com.partner.util.TencentCloudUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.MEventIncentiveVo;
import com.partner.vo.combox.CountryCombox;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class MEventIncentiveServiceImpl
        extends ServiceImpl<MEventIncentiveMapper,MEventIncentiveEntity> implements MEventIncentiveService {

        private final MEventIncentiveMapper mEventIncentiveMapper;

        private final REventPartnerUserReadMapper rEventPartnerUserReadMapper;

        private final REventIncentivePartnerUserReadMapper rEventIncentivePartnerUserReadMapper;

        private final RedisTemplate redisTemplate;
        private final TencentCloudUtils tencentCloudUtils;
        private final WechatApplet wechatApplet;

        @Override
        public IPage getMEventIncentivePage(Page page, MEventIncentiveParamsDto dto) {
                wechatApplet.getAppId();
                if(dto.getCompanyId()==null){
                        dto.setCompanyId(ConfigTypeEnum.M_COMPANY.key);
                }
                String baseurl=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
                dto.setMMageAddress(baseurl);

                IPage<MEventIncentiveVo> pageinfo=mEventIncentiveMapper.getMEventIncentivePage(page,dto);

                List<MEventIncentiveVo> resultlist=pageinfo.getRecords();
                List<Long> incentiveIdList=new ArrayList<>();//初始化redis不存在的数量

                for(MEventIncentiveVo vo:resultlist){
                        //查询redis数量
                        Object totals=redisTemplate.opsForHash().get(ConfigTypeEnum.M_EVENT_INCENTIVE_HASH.uNewsType,vo.getEventIncentiveId().toString());

                        if(totals!=null /*&& Integer.parseInt(totals.toString())!=0*/){
                                vo.setTotal(Integer.parseInt(totals.toString()));
                        }else{
                                incentiveIdList.add(vo.getEventIncentiveId());
                        }
                }
                List<REventIncentivePartnerUserReadEntity> tmpReadNullArr=null;
                if(ObjectUtil.isNotEmpty(incentiveIdList)){
                        tmpReadNullArr= rEventIncentivePartnerUserReadMapper.selectList(
                                new LambdaQueryWrapper<REventIncentivePartnerUserReadEntity>()
                                        .in(REventIncentivePartnerUserReadEntity::getFkEventIncentiveId,incentiveIdList)

                        );
                }
                if(ObjectUtil.isNotEmpty(tmpReadNullArr)){
                       Map<Long,List<REventIncentivePartnerUserReadEntity>> resultmap= tmpReadNullArr.stream().collect(Collectors.groupingBy(o->o.getFkEventIncentiveId()) );
                       for(Long incentiveId:incentiveIdList){
                               if(resultmap.containsKey(incentiveId)){
                                       int totaltmp=resultmap.get(incentiveId).size();
                                       redisTemplate.opsForHash().put(ConfigTypeEnum.M_EVENT_INCENTIVE_HASH.uNewsType,incentiveId.toString(),totaltmp);
                               }
                       }
                }else {
                        for(Long incentiveId:incentiveIdList){
                                redisTemplate.opsForHash().put(ConfigTypeEnum.M_EVENT_INCENTIVE_HASH.uNewsType,incentiveId.toString(),0);
                        }
                }


                pageinfo.setRecords(resultlist);

                return pageinfo;
        }

        @Override
        public List<CountryCombox> getCountryCombox(MEventIncentiveParamsDto dto) {
                if(dto.getCompanyId()==null){
                        dto.setCompanyId(ConfigTypeEnum.M_COMPANY.key);
                }
                List<CountryCombox> resultlist=mEventIncentiveMapper.getCountryCombox(dto);

                return resultlist;
        }

        @Override
        public MEventIncentiveVo getIncentiveDetail(Long eventIncentiveId) {

                String mMageAddress=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);


                MEventIncentiveVo result=mEventIncentiveMapper.getMeventDetail(eventIncentiveId,mMageAddress);
                try{
                        /*Long rNewsCount=rEventPartnerUserReadMapper.selectCount(new LambdaQueryWrapper<REventPartnerUserReadEntity>()
                                .eq(REventPartnerUserReadEntity::getFkEventId, eventIncentiveId)
                                );*/
                        //阅读信息 数量
                        Long rNewsCount=rEventIncentivePartnerUserReadMapper.selectCount(new LambdaQueryWrapper<REventIncentivePartnerUserReadEntity>()
                                .eq(REventIncentivePartnerUserReadEntity::getFkEventIncentiveId, eventIncentiveId)
                        );


                        Long total_tmp=rNewsCount;
                        FzhUser user=SecurityUtils.getUser();

                        if(ObjectUtil.isNotEmpty(user)){
                                UserInfoParams userInfoParams = UserInfoParamsUtils.getUserInfoParams(Long.parseLong(user.getFkTenantId()),
                                        user.getFkFromPlatformCode(), user.getId());
                                Long fkPartnerUserId=userInfoParams.getPartnerUserId();

                                REventIncentivePartnerUserReadEntity incentiveEntity=rEventIncentivePartnerUserReadMapper.selectOne(
                                        new LambdaQueryWrapper<REventIncentivePartnerUserReadEntity>()
                                                .eq(REventIncentivePartnerUserReadEntity::getFkEventIncentiveId, eventIncentiveId)
                                                .eq(REventIncentivePartnerUserReadEntity::getFkPartnerUserId, fkPartnerUserId)
                                );

                                if(ObjectUtil.isEmpty(incentiveEntity)){
                                        REventIncentivePartnerUserReadEntity savePO=new REventIncentivePartnerUserReadEntity();
                                        savePO.setFkEventIncentiveId(eventIncentiveId);
                                        savePO.setFkPartnerUserId(fkPartnerUserId);
                                        savePO.setGmtCreate(LocalDateTime.now());
                                        savePO.setGmtCreateUser(fkPartnerUserId.toString());
                                        //阅读信息保存
                                        rEventIncentivePartnerUserReadMapper.insert(savePO);

                                }
                        }
                        if(total_tmp.longValue()==0){
                                total_tmp=total_tmp+1;
                        }

                        //阅读信息 缓存保存
                        redisTemplate.opsForHash().put(ConfigTypeEnum.M_EVENT_INCENTIVE_HASH.uNewsType,eventIncentiveId.toString(),total_tmp);

                        result.setTotal(total_tmp.intValue());

                }catch (Exception e){
                        log.error(e.getMessage());
                }

                return result;
        }


}
