package com.partner.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.partner.dto.role.SavePartnerRoleDto;
import com.partner.dto.role.SaveUserDto;
import com.partner.entity.PartnerRole;
import com.partner.vo.TeamMemberTreeVo;
import com.partner.vo.TeamMemberVo;
import com.partner.vo.role.MenuTreeVo;
import com.partner.vo.role.PartnerUserDetailVo;
import com.partner.vo.role.TeamUserVo;
import com.partner.vo.role.UserRolePermission;

import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:
 */
public interface PartnerRoleService extends IService<PartnerRole> {

    /**
     * 获取角色列表
     *
     * @return
     */
    List<PartnerRole> roleList();

    /**
     * 保存角色
     *
     * @param roleDto
     */
    void saveRole(SavePartnerRoleDto roleDto);

    /**
     * 获取菜单树
     *
     * @return
     */
    List<MenuTreeVo> menuTree();


    /**
     * 获取用户权限
     *
     * @return
     */
    UserRolePermission getUserRolePermission();


    /**
     * 获取角色详情
     *
     * @param roleId
     * @return
     */
    PartnerRole roleDetail(Long roleId);

    /**
     * 获取伙伴用户详情
     *
     * @return
     */
    PartnerUserDetailVo getUserDetail(Long partnerUserId);


    /**
     * 添加团队成员/新增编辑伙伴用户
     *
     * @param saveUserDto
     */
    void savePartnerUser(SaveUserDto saveUserDto);

    /**
     * 获取团队成员列表
     *
     * @return
     */
    List<TeamMemberVo> teamMemberList(Integer year);

    /**
     * 删除角色
     * @param id
     */
    void delRole(Long id);

    /**
     * 获取上级列表
     * @return
     */
    List<TeamMemberVo> getSuperiorList();

    /**
     * 获取团队成员树
     * @param year
     * @return
     */
    List<TeamUserVo> teamMemberTree(Integer year);

}
