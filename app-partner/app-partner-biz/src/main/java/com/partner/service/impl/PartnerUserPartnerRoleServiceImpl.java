package com.partner.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.entity.PartnerUserPartnerRole;
import com.partner.mapper.PartnerUserPartnerRoleMapper;
import com.partner.service.PartnerUserPartnerRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/6/25
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class PartnerUserPartnerRoleServiceImpl extends ServiceImpl<PartnerUserPartnerRoleMapper, PartnerUserPartnerRole> implements PartnerUserPartnerRoleService {

    @Autowired
    private PartnerUserPartnerRoleMapper partnerUserPartnerRoleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePartnerUserPartnerRole(Long partnerUserId, List<Long> roleIds) {
        if (Objects.isNull(partnerUserId)) {
            return;
        }
        if (CollectionUtils.isEmpty(roleIds)) {
            this.remove(new LambdaQueryWrapper<PartnerUserPartnerRole>().eq(PartnerUserPartnerRole::getFkPartnerUserId, partnerUserId));
            return;
        }

        FzhUser user = SecurityUtils.getUser();
        //当前角色目前已有的角色ID
        List<Long> currentRoleIds = partnerUserPartnerRoleMapper.selectList(new LambdaQueryWrapper<PartnerUserPartnerRole>()
                        .eq(PartnerUserPartnerRole::getFkPartnerUserId, partnerUserId))
                .stream().map(PartnerUserPartnerRole::getFkPartnerRoleId).collect(Collectors.toList());

        //获取新增的菜单权限
        List<Long> addRoleIds = roleIds.stream()
                .filter(id -> !currentRoleIds.contains(id)).collect(Collectors.toList());

        //获取需要删除的菜单权限
        List<Long> delRoleIds = currentRoleIds.stream()
                .filter(id -> !roleIds.contains(id)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addRoleIds)) {
            List<PartnerUserPartnerRole> saveList = addRoleIds.stream()
                    .map(id -> {
                        PartnerUserPartnerRole userPartnerRole = new PartnerUserPartnerRole();
                        userPartnerRole.setFkPartnerRoleId(id);
                        userPartnerRole.setFkTenantId(Long.valueOf(user.getFkTenantId()));
                        userPartnerRole.setFkPartnerUserId(partnerUserId);
                        userPartnerRole.setGmtCreate(new Date());
                        userPartnerRole.setGmtModified(new Date());
                        userPartnerRole.setGmtCreateUser(user.getLoginId());
                        userPartnerRole.setGmtModifiedUser(user.getLoginId());
                        return userPartnerRole;
                    }).collect(Collectors.toList());
            this.saveBatch(saveList);
        }

        if (CollectionUtils.isNotEmpty(delRoleIds)) {
            this.remove(new LambdaQueryWrapper<PartnerUserPartnerRole>()
                    .eq(PartnerUserPartnerRole::getFkPartnerUserId, partnerUserId)
                    .in(PartnerUserPartnerRole::getFkPartnerRoleId, delRoleIds));
        }
    }
}
