package com.partner.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.partner.dto.MLiveParamsDto;
import com.partner.dto.MLivecalendarParamsDto;
import com.partner.dto.base.UserInfoParams;
import com.partner.entity.MLiveEntity;
import com.partner.entity.RLivePartnerUserAppointmentEntity;
import com.partner.eunms.ConfigTypeEnum;
import com.partner.mapper.MLiveMapper;
import com.partner.mapper.RLiveUserAppointmentMapper;
import com.partner.service.MLiveService;
import com.partner.util.TencentCloudUtils;
import com.partner.util.UserInfoParamsUtils;
import com.partner.vo.AppointmentInfo;
import com.partner.vo.MLivePageVo;
import com.partner.vo.MLivecalendarVo;
import com.partner.vo.jingang.UserAppointmentViewVo;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@AllArgsConstructor
public class MLiveServiceImpl extends ServiceImpl<MLiveMapper, MLiveEntity> implements MLiveService {
    private final  MLiveMapper mLiveMapper;
    private final RLiveUserAppointmentMapper rLiveUserAppointmentMapper;

    private final RedisTemplate redisTemplate;
    private final TencentCloudUtils tencentCloudUtils;

    @Override
    public IPage getMLiveListPage(Page page, MLiveParamsDto dto) {
        String baseurl=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        dto.setMMageAddress(baseurl);
        IPage<MLivePageVo> result=mLiveMapper.getMLiveListPage(page, dto);

        //查询预约用户和头像信息
        List<MLivePageVo> listResult=result.getRecords();

        /*if(dto.getLiveType()!=null && dto.getLiveType().intValue()==0 && ObjectUtils.isEmpty(listResult)){//最新直播没有 时取最近5条
            listResult=mLiveMapper.getMLiveFive(dto);

        }*/

        setCach(listResult,dto.getLoginType());

        result.setRecords(listResult);

        return result;
    }

    public void setCach(List<MLivePageVo> listResult, String loginType){
        if(ObjectUtils.isEmpty(listResult)){
            return;
        }
        UserInfoParams userinfo;
        if(loginType!=null && loginType.equals("1")){
            FzhUser fzhUser= SecurityUtils.getUser();
            if(ObjectUtils.isNotEmpty(fzhUser)){
                userinfo= UserInfoParamsUtils.getUserInfoParams(Long.parseLong(fzhUser.getFkTenantId()),fzhUser.getFkFromPlatformCode(),fzhUser.getId());
            } else {
                userinfo = null;
            }
        } else {
            userinfo = null;
        }

        listResult.stream().forEach(exe->{
            Long   liveid=exe.getId();
            String sliveid=liveid.toString();
            String appointmentZsetKey=ConfigTypeEnum.R_LIVE_USER_APPOINTMENT_ZSET.uNewsType+":"+sliveid;
            String appointmentHashKey= ConfigTypeEnum.R_LIVE_USER_APPOINTMENT_HASH.uNewsType+":"+sliveid;



            if(Boolean.TRUE.equals(redisTemplate.hasKey(appointmentZsetKey))){//键值存在
                Set<String> userArry= redisTemplate.opsForZSet().range(appointmentZsetKey,0,5);
                List<AppointmentInfo> tmpinfo= new ArrayList<>();
                userArry.stream().forEach(userid->{
                    AppointmentInfo redisappointment=(AppointmentInfo)redisTemplate.opsForHash().get(appointmentHashKey,userid);
                    tmpinfo.add(redisappointment);
                });
                exe.setUserinfo(tmpinfo);
                long total=redisTemplate.opsForHash().size(appointmentHashKey);
                exe.setTotaluser(total);
            }else {

                /*List<RLivePartnerUserAppointmentEntity> livelist=rLiveUserAppointmentMapper.selectList(new LambdaQueryWrapper<RLivePartnerUserAppointmentEntity>()
                        .eq(RLivePartnerUserAppointmentEntity::getFkLiveId,sliveid)
                        .orderByAsc(RLivePartnerUserAppointmentEntity::getGmtCreate)
                );*/

                List<UserAppointmentViewVo> livelist=rLiveUserAppointmentMapper.selectDetailList(liveid);
                if(ObjectUtils.isNotEmpty(livelist)){
                    List<AppointmentInfo> tmpinfo= new ArrayList<>();
                    List<AppointmentInfo> allInfo= new ArrayList<>();
                    for(int i=0;i<livelist.size();i++){
                        UserAppointmentViewVo tmpdbnum=livelist.get(i);
                        String spartnerUserid=tmpdbnum.getFkPartnerUserId().toString();

                        AppointmentInfo redisappointment=new AppointmentInfo();
                        redisappointment.setUserId(tmpdbnum.getFkPartnerUserId());
                        redisappointment.setUserNameChn(tmpdbnum.getName());
                        redisappointment.setFileKey(tmpdbnum.getWechatIconUrl());

                        if(livelist.size()>5 && i>=livelist.size()-5){
                            tmpinfo.add(redisappointment);
                            ZonedDateTime zonedDateTime = tmpdbnum.getGmtCreate().atZone(ZoneId.systemDefault());
                            redisTemplate.opsForZSet().add(appointmentZsetKey,spartnerUserid,zonedDateTime.toInstant().toEpochMilli());
                        }
                        if(livelist.size()<=5){
                            tmpinfo.add(redisappointment);
                            ZonedDateTime zonedDateTime = tmpdbnum.getGmtCreate().atZone(ZoneId.systemDefault());
                            redisTemplate.opsForZSet().add(appointmentZsetKey,spartnerUserid,zonedDateTime.toInstant().toEpochMilli());
                        }
                        allInfo.add(redisappointment);
                        redisTemplate.opsForHash().put(appointmentHashKey,spartnerUserid,redisappointment);

                    }
                    exe.setUserinfo(tmpinfo);
                    exe.setTotaluser(livelist.size());
                }

            }

            //判断当前用户是否已预约该直播状态
            if(ObjectUtils.isNotEmpty(userinfo) && userinfo.getPartnerUserId()!=null){
                //查询用户
                String searchPartnerUserId=userinfo.getPartnerUserId().toString();
                if(redisTemplate.opsForHash().get(appointmentHashKey,searchPartnerUserId)!=null){
                    exe.setAppointmentType(1);
                }
            }
        });
    }

    @Override
    public List<MLivePageVo> getHomeMLiveList(String loginType) {
        Page pageParams=new Page();
        pageParams.setSize(5);

        MLiveParamsDto params=new MLiveParamsDto();
        String baseurl=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        params.setMMageAddress(baseurl);
        params.setLiveType(0);

        IPage<MLivePageVo> result=mLiveMapper.getMLiveListPage(pageParams, params);

        List<MLivePageVo> livelist=result.getRecords();
        /*if(ObjectUtils.isEmpty(livelist)){
            livelist=mLiveMapper.getMLiveFive(params);
        }*/

        setCach(livelist,loginType);


        return livelist;
    }

    @Override
    public List<MLivecalendarVo> getStatus(MLivecalendarParamsDto params) {

        List<MLivecalendarVo> result=new ArrayList<>();

        String baseurl=tencentCloudUtils.getTencentCloudUrl(ConfigTypeEnum.M_MAGE_ADDRESS.uNewsType);
        params.setMMageAddress(baseurl);

        params.setLiveType(0);//先查直播
        List<MLivePageVo> live=mLiveMapper.getStatus(params);
        Map<LocalDate,List<MLivePageVo>> liveMap=new HashMap<>();
        if(ObjectUtils.isNotEmpty(live)){
            liveMap=live.stream().collect(Collectors.groupingBy(MLivePageVo::getLiveDate));
        }

        params.setLiveType(1);//再查直播
        List<MLivePageVo> recordedVideo=mLiveMapper.getStatus(params);
        Map<LocalDate,List<MLivePageVo>> recordedVideoMap=new HashMap<>();
        if(ObjectUtils.isNotEmpty(recordedVideo)){
            recordedVideoMap=recordedVideo.stream().collect(Collectors.groupingBy(MLivePageVo::getLiveDate));
        }

        Map<LocalDate, List<MLivePageVo>> mergeMap= mergeAndSortMaps(liveMap, recordedVideoMap);
        for(LocalDate localDate:mergeMap.keySet()){
            MLivecalendarVo tmpvo=new MLivecalendarVo();
            tmpvo.setLiveDate(localDate);
            tmpvo.setLiveList(mergeMap.get(localDate));
            result.add(tmpvo);
        }

        return result;
    }

    public  Map<LocalDate, List<MLivePageVo>> mergeAndSortMaps(
            Map<LocalDate, List<MLivePageVo>> liveMap,
            Map<LocalDate, List<MLivePageVo>> recordedVideoMap) {

        // 1. 合并两个Map的所有日期
        Set<LocalDate> allDates = Stream.concat(
                liveMap.keySet().stream(),
                recordedVideoMap.keySet().stream()
        ).collect(Collectors.toSet());

        // 2. 创建新的TreeMap，自动按键(LocalDate)排序
        Map<LocalDate, List<MLivePageVo>> resultMap = new TreeMap<>(Comparator.naturalOrder()); // 正序排序

        // 3. 合并每个日期的数据
        for (LocalDate date : allDates) {
            List<MLivePageVo> combinedList = new ArrayList<>();

            // 添加直播数据（如果有）
            if (liveMap.containsKey(date)) {
                combinedList.addAll(liveMap.get(date));
            }

            // 添加录播数据（如果有）
            if (recordedVideoMap.containsKey(date)) {
                combinedList.addAll(recordedVideoMap.get(date));
            }

            // 放入结果Map
            resultMap.put(date, combinedList);
        }

        return resultMap;
    }


}
