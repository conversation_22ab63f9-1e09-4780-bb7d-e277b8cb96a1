package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.RLivePartnerUserAppointmentEntity;
import com.partner.vo.AppointmentVo;
import com.partner.vo.jingang.UserAppointmentViewVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RLiveUserAppointmentMapper extends BaseMapper<RLivePartnerUserAppointmentEntity> {

    List<AppointmentVo> getAppointmentList(@Param("liveId")Long liveId);


    List<UserAppointmentViewVo>  selectDetailList(Long sliveid);

    UserAppointmentViewVo selectDetailUser(Long partnerUserId);

}
