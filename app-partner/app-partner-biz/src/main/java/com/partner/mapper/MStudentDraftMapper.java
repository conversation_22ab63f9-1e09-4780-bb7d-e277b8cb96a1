package com.partner.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.student.MStudentDraftParamsDto;
import com.partner.vo.student.MStudentDraftVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface MStudentDraftMapper {

    IPage<MStudentDraftVo> getPartnerStudents(Page page, @Param("query") MStudentDraftParamsDto params);

    IPage<MStudentDraftVo> getCheckStudentsPage(Page page, @Param("query") MStudentDraftParamsDto params);


    void getReadingCheck(MStudentDraftParamsDto params);

    int getNoReadingNum(MStudentDraftParamsDto params);

}
