package com.partner.mapper;

import com.partner.dto.base.CountryStateCityParamsDto;
import com.partner.dto.base.InstitutionCourseParamsDto;
import com.partner.dto.base.InstitutionSearchDto;
import com.partner.dto.student.AreaCountryDto;
import com.partner.vo.base.*;
import com.partner.vo.combox.AreaCountryVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CountryStateCityMapper {

     List<CountryBaseCombox> getCountryCombox(CountryStateCityParamsDto dto);

     List<CountryBaseCombox> getAllPubCountryCombox(CountryStateCityParamsDto dto);




     List<StateBaseCombox> getStateCombox(CountryStateCityParamsDto dto);

     List<CityBaseCombox> getCityCombox(CountryStateCityParamsDto dto);

     List<BaseCombox> getEducationCombox();

     List<BaseCombox> getInstitutionList(CountryStateCityParamsDto dto);

     List<CourseCombox> getInstitutionCourseList(InstitutionCourseParamsDto dto);


     List<BaseCombox> getInstitutionListSearch(InstitutionSearchDto dto);

     List<AreaCountryVo> getAreaCode(AreaCountryDto params);
}
