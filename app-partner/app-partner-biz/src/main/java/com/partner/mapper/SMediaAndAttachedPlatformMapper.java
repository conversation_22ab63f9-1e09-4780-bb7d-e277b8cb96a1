package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.dto.SMediaAndAttachedPublicDto;
import com.partner.entity.SMediaAndAttachedEntity;
import com.partner.vo.FileArray;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【s_media_and_attached】的数据库操作Mapper
 * @createDate 2025-01-08 17:33:34
 * @Entity com.partner.entity.SMediaAndAttached
 */
@Mapper
@DS("aisplatformdb")
public interface SMediaAndAttachedPlatformMapper extends BaseMapper<SMediaAndAttachedEntity> {

    List<FileArray> selectPublicPlatformFileArrays(SMediaAndAttachedPublicDto params);

    /**
     * 查询平台文件
     * @param tableName
     * @param typeKey
     * @param tableId
     * @return
     */
    List<FileArray> selectPlatformFileArrays(@Param("tableName") String tableName,
                                             @Param("typeKey") String typeKey,
                                             @Param("tableId") Long tableId);


}




