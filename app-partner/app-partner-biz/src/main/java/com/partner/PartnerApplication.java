package com.partner;

import com.common.security.annotation.EnableResourceServer;
import com.common.swagger.annotation.EnableDoc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR> auto
 * <p>
 * 项目启动类
 */
@EnableDoc("partner")
@EnableResourceServer
@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.apps.api.feign","com.admin.api.feign","com.pmp.feign"})
public class PartnerApplication {
    public static void main(String[] args) {
        SpringApplication.run(PartnerApplication.class, args);
    }
}
