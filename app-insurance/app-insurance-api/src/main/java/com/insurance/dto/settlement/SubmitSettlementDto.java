package com.insurance.dto.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/28
 * @Version 1.0
 * @apiNote:提交结算
 */
@Data
public class SubmitSettlementDto {

    @Schema(description = "订单结算ID集合")
    @NotNull(message = "订单结算Id集合不能为空")
    private List<Long> orderSettlementIds;

    @Schema(description = "代理账户ID")
    @NotNull(message = "代理账户ID不能为空")
    private Long agentAccountId;

    @Schema(description = "结算账户币种")
    @NotBlank(message = "结算账户币种不能为空")
    private String currencyTypeNum;

    @Schema(description = "签名文件-base64流")
    @NotBlank(message = "签名文件不能为空")
    private String signature;
}
