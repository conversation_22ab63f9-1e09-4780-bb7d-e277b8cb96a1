package com.insurance.vo.insurance.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @Author:Oliver
 * @Date: 2025/7/3
 * @Version 1.0
 * @apiNote: 加密卡号信息请求体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EncryptCreditCardVo {

    @Schema(description = "加密卡号")
    private String encryptedCardNumber;

    @Schema(description = "加密卡号后三位")
    private String encryptedCvc;

    @Schema(description = "加密密钥")
    private String secretKey;
}
