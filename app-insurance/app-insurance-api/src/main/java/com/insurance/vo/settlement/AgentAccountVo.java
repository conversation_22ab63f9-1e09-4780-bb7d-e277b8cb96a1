package com.insurance.vo.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:代理账户
 */
@Data
public class AgentAccountVo {

    @Schema(description = "账户ID")
    private Long accountId;

    @Schema(description = "代理名称")
    private String agentName;

    @Schema(description = "代理ID")
    private Long agentId;

    @Schema(description = "币种编号")
    private String currencyTypeNum;

    @Schema(description = "账户卡类型，枚举：借记卡1,存折2,信用卡3,准贷记卡4,预付卡费5,境外卡6")
    private Integer accountCardType;

    @Schema(description = "银行账户名称")
    private String bankAccount;

    @Schema(description = "银行账号")
    private String bankAccountNum;

    @Schema(description = "银行名称")
    private String bankName;

    @Schema(description = "银行支行名称")
    private String bankBranchName;

    @Schema(description = "银行地址国家Id")
    private Long fkAreaCountryId;

    @Schema(description = "银行地址州省Id")
    private Long fkAreaStateId;

    @Schema(description = "银行地址城市Id")
    private Long fkAreaCityId;

    @Schema(description = "银行地址城市区域Id")
    private Long fkAreaCityDivisionId;

    @Schema(description = "银行地址")
    private String bankAddress;

    @Schema(description = "银行编号类型：SwiftCode/BSB")
    private String bankCodeType;

    @Schema(description = "银行编号")
    private String bankCode;

    @Schema(description = "国家编码")
    private String areaCountryCode;

    @Schema(description = "是否默认首选：0否/1是")
    private Integer isDefault;

    @Schema(description = "是否激活：0否/1是")
    private Integer isActive;

    @Schema(description = "备注")
    private String remark;
}
