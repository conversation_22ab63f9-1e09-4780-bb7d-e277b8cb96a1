package com.insurance.feign;

import com.common.core.util.R;
import com.common.feign.annotation.NoToken;
import com.common.mybatis.tenant.FzhFeignTenantInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @Author:Oliver
 * @Date: 2025/1/15  16:44
 * @Version 1.0
 */
@FeignClient(contextId = "remoteInsuranceService", value = "app-insurance-biz", configuration = FzhFeignTenantInterceptor.class)
public interface RemoteInsuranceService {

    @NoToken
    @GetMapping("/feign/order/sendRequest")
    R<?> sendRequest();

}
