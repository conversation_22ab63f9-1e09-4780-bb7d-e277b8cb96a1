package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Data
@TableName("m_credit_card_statement")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreditCardStatement extends BaseEntity {

    @Schema(description ="信用卡Id")
    private Long fkCreditCardId;

    @Schema(description ="交易流水号")
    private String num;

    @Schema(description ="业务类型：0调整/1支出/2收取/3退款")
    private Integer businessType;

    @Schema(description ="关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @Schema(description ="关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

    @Schema(description ="交易币种")
    private String fkCurrencyTypeNum;

    @Schema(description ="交易金额")
    private BigDecimal amount;

    @Schema(description ="汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;

    @Schema(description ="交易金额（折合人民币）")
    private BigDecimal amountRmb;

    @Schema(description ="交易状态：0失败/1成功")
    private Integer status;

    @Schema(description ="备注")
    private String remark;
}
