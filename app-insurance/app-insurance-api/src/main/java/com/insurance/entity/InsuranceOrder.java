package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:保险订单
 */
@Data
@TableName("m_insurance_order")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InsuranceOrder extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "租户Id")
    private Long fkTenantId;

    @Schema(description = "公司Id")
    private Long fkCompanyId;

    @Schema(description = "学生代理Id")
    private Long fkAgentId;

    @Schema(description = "伙伴用户Id")
    private Long fkPartnerUserId;

    @Schema(description = "保险公司Id")
    private Long fkInsuranceCompanyId;

    @Schema(description = "保险产品类型Id")
    private Long fkProductTypeId;

    @Schema(description = "系统订单编号")
    private String orderNum;

    @Schema(description = "保险单号")
    private String insuranceNum;

    @Schema(description = "保险单类型：Single/Couple/Family")
    private String insuranceType;

    @Schema(description = "币种编号")
    private String fkCurrencyTypeNum;

    @Schema(description = "保单金额")
    private BigDecimal insuranceAmount;

    @Schema(description = "受保人姓名")
    private String insurantName;

    @Schema(description = "受保人姓（英/拼音）")
    private String insurantLastName;

    @Schema(description = "受保人名（英/拼音）")
    private String insurantFirstName;

    @Schema(description = "受保人性别")
    private String insurantGender;

    @Schema(description = "受保人国籍")
    private String insurantNationality;

    @Schema(description = "受保人生日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insurantBirthday;

    @Schema(description = "受保人护照号")
    private String insurantPassportNum;

    @Schema(description = "入学时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentTime;

    @Schema(description = "毕业时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date graduationTime;

    @Schema(description = "前往国家Id")
    private Long fkAreaCountryIdTo;

    @Schema(description = "信用卡Id")
    private Long fkCreditCardId;

    @Schema(description = "保单开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-d", timezone = "GMT+8")
    private Date insuranceStartTime;

    @Schema(description = "保单结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTime;

    @Schema(description = "订单明细json")
    private String orderJson;

    @Schema(description = "备注（后台用户备注信息）")
    private String remark;

    @Schema(description = "保单备注（用户下单时填写）")
    private String orderRemark;

    @Schema(description = "订单状态：等待下单0/下单中1/下单成功2/下单失败-2")
    private Integer orderStatus;

    @Schema(description = "订单信息（系统反馈记录）")
    private String orderMessage;

    @Schema(description = "受保人Email")
    private String insurantEmail;

    @Schema(description = "受保人移动电话")
    private String insurantMobile;

    @Schema(description = "受保人手机区号")
    private String insurantMobileAreaCode;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "成交时间（保险公司系统成功下单时间）")
    private Date orderTime;

    @Schema(description = "保险产品类型Key")
    @TableField(exist = false)
    private String productTypeKey;

    @Schema(description = "微信支付的用户openid，支付时生成，和订单一一对应，若支付失败，需要重新生成订单")
    private String mpPaymentOpenid;

    @Schema(description = "微信支付状态：未支付0/支付成功1/支付失败-1")
    private Integer mpPaymentStatus;

    @Schema(description = "支付类型：1信用卡/2微信支付")
    private Integer paymentType;
}

