package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:保险产品类型
 */
@Data
@TableName("u_product_type")
public class ProductType extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "保险公司Id")
    private Long fkInsuranceCompanyId;

    @Schema(description = "产品类型名称")
    private String typeName;

    @Schema(description = "产品类型_key")
    private String typeKey;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @Schema(description = "币种编号")
    private String fkCurrencyTypeNum;

    @Schema(description = "佣金比例")
    private Integer commissionRate;

    @Schema(description = "代理佣金比例")
    private Integer commissionRateAgent;
}

