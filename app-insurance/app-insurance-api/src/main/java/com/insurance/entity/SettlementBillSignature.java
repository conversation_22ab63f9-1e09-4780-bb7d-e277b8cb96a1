package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * 结算账单签名
 */
@Data
@TableName("r_settlement_bill_signature")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettlementBillSignature extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "结算账单Id")
    private Long fkSettlementBillId;

    @Schema(description = "签名文件")
    private String signature;
}
