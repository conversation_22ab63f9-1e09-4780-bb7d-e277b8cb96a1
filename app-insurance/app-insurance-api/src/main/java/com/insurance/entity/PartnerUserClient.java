package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.insurance.vo.insurance.client.ClientOrderVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:用户客户
 */
@Data
@TableName("m_partner_user_client")
public class PartnerUserClient extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "租户Id")
    private Long fkTenantId;

    @Schema(description = "伙伴用户Id")
    private Long fkPartnerUserId;

    @Schema(description = "客户类型（枚举：1学生）")
    private Integer clientType;

    @Schema(description = "客户名称")
    private String clientName;

    @Schema(description = "前往国家Id")
    private Long fkAreaCountryId;

    @Schema(description = "前往州省Id")
    private Long fkAreaStateId;

    @Schema(description = "保单开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceStartTime;

    @Schema(description = "保单结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insuranceEndTime;

    @Schema(description = "Email")
    private String email;

    @Schema(description = "手机区号")
    private String mobileAreaCode;

    @Schema(description = "移动电话")
    private String mobile;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "微信openid")
    private String wechatOpenid;

    @Schema(description = "最新进展:0-未购买(包含未购买和下单失败失败的);1-下单中;2-已购买;(1和2表示已购买)")
    @TableField(exist = false)
    private Integer progress;

    @Schema(description = "关联订单")
    @TableField(exist = false)
    private List<ClientOrderVo> orders;

}
