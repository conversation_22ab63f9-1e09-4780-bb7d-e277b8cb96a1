package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("m_credit_card_reminder_notifier")
public class CreditCardReminderNotifier extends BaseEntity {

    @Schema(description = "信用卡提醒配置Id")
    private Long fkCreditCardReminderId;

    @Schema(description = "通知人姓名")
    private String name;

    @Schema(description = "通知人手机")
    private String mobile;

    @Schema(description = "通知人邮件")
    private String email;

    @Schema(description = "信用卡ID")
    @TableField(exist = false)
    private Long creditCardId;
}