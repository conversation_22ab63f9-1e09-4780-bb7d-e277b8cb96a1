package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 结算账单明细
 */
@Data
@TableName("m_settlement_bill_item")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettlementBillItem extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "结算账单Id")
    private Long fkSettlementBillId;

    @Schema(description = "订单应付计划结算表Id")
    private Long fkInsuranceOrderSettlementId;

    @Schema(description = "实际支付币种")
    private String fkCurrencyTypeNumActual;

    @Schema(description = "实际支付金额")
    private BigDecimal amountActual;

    @Schema(description = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    @Schema(description = "兑换汇率")
    private BigDecimal exchangeRate;

    @Schema(description = "兑换支付币种")
    private String fkCurrencyTypeNumExchange;

    @Schema(description = "兑换支付金额")
    private BigDecimal amountExchange;

    @Schema(description = "兑换手续费金额")
    private BigDecimal serviceFeeExchange;

    @Schema(description = "结算订单结算状态：0待确认/1已确认/2代理确认/3财务确认/4结算完成")
    @TableField(exist = false)
    private Integer approvalStatus;
}
