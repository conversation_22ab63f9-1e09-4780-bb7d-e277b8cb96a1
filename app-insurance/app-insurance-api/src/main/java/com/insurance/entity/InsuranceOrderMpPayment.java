package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微信支付订单表实体类
 */
@Data
@TableName("m_insurance_order_mp_payment")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InsuranceOrderMpPayment implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "保险订单Id")
    private Long fkInsuranceOrderId;

    @Schema(description = "微信支付单号")
    private String mpPaymentOrderNum;

    @Schema(description = "支付数量（默认1）")
    private Integer mpPaymentCount;

    @Schema(description = "支付金额-分")
    private BigDecimal mpPaymentAmount;

    @Schema(description = "微信支付的用户openid，支付时生成，和订单一一对应，若支付失败，需要重新生成订单")
    private String mpPaymentOpenid;

    @Schema(description = "微信支付状态：未支付0/支付成功1/支付失败-1")
    private Integer mpPaymentStatus;

    @Schema(description = "备注")
    private String remark;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "创建人")
    private String gmtCreateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    private Date gmtCreate;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "更新人")
    private String gmtModifiedUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    private Date gmtModified;

    @Schema(description = "订单号")
    @TableField(exist = false)
    private String orderNo;

}
