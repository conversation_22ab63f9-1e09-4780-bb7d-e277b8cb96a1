package com.insurance.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:保险产品类型
 */
@Data
@TableName("m_insurance_order_settlement")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InsuranceOrderSettlement extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "保险订单Id")
    private Long fkInsuranceOrderId;

    @Schema(description = "应付计划Id")
    private Long fkPayablePlanId;

    @Schema(description = "付款单子项Id")
    private Long fkPaymentFormItemId;

    @Schema(description = "0待确认/1已确认/2代理确认/3财务确认/4结算完成")
    private Integer statusSettlement;

    @Schema (description = "代理提交结算批次编号")
    private String fkNumOptBatch;
}

