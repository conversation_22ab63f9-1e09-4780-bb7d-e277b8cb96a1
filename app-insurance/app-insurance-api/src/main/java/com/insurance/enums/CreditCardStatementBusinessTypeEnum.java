package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 信用卡流水业务类型状态枚举类
 */

@Getter
@AllArgsConstructor
public enum CreditCardStatementBusinessTypeEnum {

    ADJUST(0, "调整"),
    EXPENSES(1, "支出"),
    INCOME(2, "收取"),
    REFUND(3, "退款"),
    ;


    private Integer code;

    private String msg;


    public static CreditCardStatementBusinessTypeEnum getEnumByCode(Integer code) {
        for (CreditCardStatementBusinessTypeEnum value : CreditCardStatementBusinessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
