package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 信用卡流水关联类型表枚举类
 */

@Getter
@AllArgsConstructor
public enum CreditCardStatementRelateTypeEnum {

    INSURANCE_ORDER("m_insurance_order", "订单表"),
    ;


    private String code;

    private String msg;


    public static CreditCardStatementRelateTypeEnum getEnumByCode(String code) {
        for (CreditCardStatementRelateTypeEnum value : CreditCardStatementRelateTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
