package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 保险单类型举类
 */

@Getter
@AllArgsConstructor
public enum NotifyMessageTypeEnum {
    QUOTA_REMIND("QUOTA_REMIND", "额度不足提醒"),
    FAIL_REMIND("FAIL_REMIND", "支付失败提醒"),
    REPAYMENT_REMIND("REPAYMENT_REMIND", "出账还款提醒"),
    ;

    private String code;

    private String msg;


    public static NotifyMessageTypeEnum getEnumByCode(String code) {
        for (NotifyMessageTypeEnum value : NotifyMessageTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
