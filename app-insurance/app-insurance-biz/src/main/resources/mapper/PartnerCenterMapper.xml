<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.insurance.mapper.PartnerCenterMapper">

    <insert id="insertExchangeRate">
        INSERT INTO app_partner_center.u_exchange_rate (fk_currency_type_num_from,
                                     fk_currency_type_num_to,
                                     exchange_rate,
                                     get_date,
                                     gmt_create,
                                     gmt_create_user,
                                     gmt_modified,
                                     gmt_modified_user)
        VALUES (#{from},
                #{to},
                #{rate},
                CURRENT_DATE(),
                NOW(),
                'admin',
                NOW(),
                'admin')
    </insert>


    <select id="selectPartnerUserInfo" resultType="com.insurance.vo.partner.PartnerUserInfo">
        SELECT p.id            as partnerUserId,
               p.fk_company_id as companyId,
               p.fk_agent_id   as agentId,
               p.fk_user_id    as systemUserId
        FROM app_partner_center.m_partner_user p
        where fk_user_id = #{systemUserId}
        order by id desc
        limit 1;
    </select>

    <select id="getExchangeRate" resultType="java.math.BigDecimal">
        select exchange_rate
        from app_partner_center.u_exchange_rate
        where fk_currency_type_num_from = #{from}
          and fk_currency_type_num_to = #{to}
          and get_date = CURRENT_DATE()
        order by id desc
        limit 1;
    </select>
</mapper>