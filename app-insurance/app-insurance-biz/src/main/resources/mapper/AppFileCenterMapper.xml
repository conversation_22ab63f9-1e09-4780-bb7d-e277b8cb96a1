<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.insurance.mapper.AppFileCenterMapper">


    <insert id="insertAppFile">
        INSERT INTO app_file_center.m_file_insurance (file_guid,
                                                      file_type_orc,
                                                      file_name_orc,
                                                      file_name,
                                                      file_path,
                                                      file_key,
                                                      gmt_create,
                                                      gmt_create_user,
                                                      gmt_modified,
                                                      gmt_modified_user)
        VALUES (#{appFile.fileGuid},
                #{appFile.fileTypeOrc},
                #{appFile.fileNameOrc},
                #{appFile.fileName},
                #{appFile.filePath},
                #{appFile.fileKey},
                NOW(),
                #{loginId},
                NOW(),
                #{loginId})
    </insert>


    <select id="selectAppFileByGuid" resultType="com.insurance.dto.file.AppFileCenter">
        select *
        from app_file_center.m_file_insurance
        where file_guid = #{fileGuid}
    </select>
</mapper>