<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.insurance.mapper.SaleCenterMapper">

    <select id="getAgentAccountList" resultType="com.insurance.vo.settlement.AgentAccountVo">
        select id                       as accountId,
               fk_agent_id              as agentId,
               fk_currency_type_num     as currencyTypeNum,
               account_card_type        as accountCardType,
               bank_account             as bankAccount,
               bank_account_num         as bankAccountNum,
               bank_name                as bankName,
               bank_branch_name         as bankBranchName,
               fk_area_country_id       as fkAreaCountryId,
               fk_area_state_id         as fkAreaStateId,
               fk_area_city_id          as fkAreaCityId,
               fk_area_city_division_id as fkAreaCityDivisionId,
               bank_address             as bankAddress,
               bank_code_type           as bankCodeType,
               bank_code                as bankCode,
               area_country_code        as areaCountryCode,
               is_default               as isDefault,
               is_active                as isActive,
               remark
        from ais_sale_center.m_agent_contract_account
        where fk_agent_id = #{agentId}
          and is_active = 1
        order by is_default desc, gmt_create desc
    </select>
    <select id="getAgentAccount" resultType="com.insurance.vo.settlement.AgentAccountVo">
        select ca.id                       as accountId,
               ca.fk_agent_id              as agentId,
               ca.fk_currency_type_num     as currencyTypeNum,
               ca.account_card_type        as accountCardType,
               ca.bank_account             as bankAccount,
               ca.bank_account_num         as bankAccountNum,
               ca.bank_name                as bankName,
               ca.bank_branch_name         as bankBranchName,
               ca.fk_area_country_id       as fkAreaCountryId,
               ca.fk_area_state_id         as fkAreaStateId,
               ca.fk_area_city_id          as fkAreaCityId,
               ca.fk_area_city_division_id as fkAreaCityDivisionId,
               ca.bank_address             as bankAddress,
               ca.bank_code_type           as bankCodeType,
               ca.bank_code                as bankCode,
               ca.area_country_code        as areaCountryCode,
               ca.is_default               as isDefault,
               ca.is_active                as isActive,
               ca.remark                   as remark,
               ma.name                     as agentName
        from ais_sale_center.m_agent_contract_account ca
                 left join ais_sale_center.m_agent ma on ma.id = ca.fk_agent_id
        where ca.id = #{id}
    </select>
</mapper>