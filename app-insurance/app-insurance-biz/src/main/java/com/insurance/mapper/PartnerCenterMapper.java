package com.insurance.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.insurance.vo.partner.PartnerUserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@DS("partner")
@Mapper
public interface PartnerCenterMapper {

    /**
     * 根据系统用户Id查询伙伴用户信息
     *
     * @param systemUserId
     * @return
     */
    PartnerUserInfo selectPartnerUserInfo(Long systemUserId);

    /**
     * 添加汇率
     *
     * @param from
     * @param to
     * @param rate
     */
    void insertExchangeRate(@Param("from") String from,
                            @Param("to") String to,
                            @Param("rate") BigDecimal rate);

    /**
     * 获取汇率
     *
     * @param from
     * @param to
     * @return
     */
    BigDecimal getExchangeRate(@Param("from") String from, @Param("to") String to);
}
