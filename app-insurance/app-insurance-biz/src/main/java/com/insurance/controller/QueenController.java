
package com.insurance.controller;


import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.insurance.entity.InsuranceOrder;
import com.insurance.entity.ProductType;
import com.insurance.mapper.InsuranceOrderMapper;
import com.insurance.mapper.ProductTypeMapper;
import com.insurance.rocketmq.msg.InsurancePlanMsg;
import com.insurance.rocketmq.producer.MqProducer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Objects;

@RestController
@Slf4j
@Inner(false)
@RequestMapping("/queen")
@Tag(description = "队列管理-生产者", name = "队列管理-生产者")
public class QueenController {

    @Autowired
    private MqProducer producer;
    @Autowired
    private InsuranceOrderMapper orderMapper;
    @Autowired
    private ProductTypeMapper productTypeMapper;

    @Operation(summary = "发送请求中信息")
    @GetMapping("/sendProgressingMsg")
    public R sendProgressingMsg(String orderNo) {
        producer.sendProgressingMsg(orderNo);
        return R.ok(orderNo);
    }

    @Operation(summary = "发送下单成功信息")
    @GetMapping("/sendSuccessMsg")
    public R sendSuccessMsg(String orderNo) {
        producer.sendSuccessMsg(orderNo);
        return R.ok(orderNo);
    }

    @Operation(summary = "发送下单失败信息")
    @GetMapping("/sendFailMsg")
    public R sendFailMsg(String orderNo) {
        producer.sendFailMsg(orderNo);
        return R.ok(orderNo);
    }

    @Operation(summary = "发送测试队列信息")
    @Inner(false)
    @GetMapping("/sendQueenTestMsg")
    public R sendQueenTestMsg() {
        producer.sendQueenTestMsg();
        return R.ok();
    }

    @Operation(summary = "测试发送创建应收应付")
    @GetMapping("/sendCreatePayablePlanMsg")
    public R sendCreatePayablePlanMsg(String orderNo) {
        InsuranceOrder order = orderMapper.selectByOrderNum(orderNo);
        if (Objects.nonNull(order)) {
            ProductType productType = productTypeMapper.selectById(order.getFkProductTypeId());
            if (Objects.nonNull(productType)) {
                InsurancePlanMsg insurancePlanMsg = InsurancePlanMsg.builder()
                        .fkTypeKey("m_insurance_order")
                        .fkCurrencyTypeNum(productType.getFkCurrencyTypeNum())
                        .fkTypeTargetId(order.getId())
                        .tuitionAmount(order.getInsuranceAmount())
                        .payablePlanCommissionRate(new BigDecimal(productType.getCommissionRateAgent()))
                        .receivablePlanCommissionRate(new BigDecimal(productType.getCommissionRate()))
                        .build();
                producer.sendCreatePayablePlanMsg(insurancePlanMsg);
            }
        }
        return R.ok(orderNo);
    }

}
