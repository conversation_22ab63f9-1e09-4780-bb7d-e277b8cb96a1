
package com.insurance.controller;


import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.insurance.service.InsuranceOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@Inner(false)
@RequestMapping("/feign")
@Tag(description = "远程管理", name = "远程管理")
public class FeignController {

    @Autowired
    private InsuranceOrderService orderService;

    @Operation(summary = "发送下单请求")
    @Inner(false)
    @GetMapping("/order/sendRequest")
    public R sendRequest() {
        log.info("发送下单请求");
        orderService.sendRequest(null);
        return R.ok();
    }
}
