
package com.insurance.controller;


import com.common.core.util.R;
import com.insurance.dto.client.SaveClientDto;
import com.insurance.dto.common.IdDto;
import com.insurance.entity.PartnerUserClient;
import com.insurance.service.PartnerUserClientService;
import com.insurance.vo.insurance.client.ClientVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/client")
@Tag(description = "客户管理", name = "客户管理")
public class ClientController {

    @Autowired
    private PartnerUserClientService clientService;

    @Operation(summary = "客戶列表")
    @GetMapping("/getClientList")
    public R<List<ClientVo>> getClientList(@Parameter(description = "1-未购买;2-已购买;不传查询全部") Integer progress,
                                           @Parameter(description = "名称/邮箱/手机号") String keyword) {
        return R.ok(clientService.getClientList(progress,keyword));
    }

    @Operation(summary = "客戶详情")
    @GetMapping("/getClientList/{id}")
    public R<PartnerUserClient> getClient(@Parameter(description = "客户id") @PathVariable("id") Long id) {
        return R.ok(clientService.getClient(id));
    }

    @Operation(summary = "保存客户")
    @PostMapping("/saveClient")
    public R<String> saveClient(@RequestBody @Valid SaveClientDto saveClientDto) {
        clientService.saveClient(saveClientDto);
        return R.ok("保存成功");
    }

    @Operation(summary = "删除客户")
    @PostMapping("/deleteClient")
    public R<String> deleteClient(@RequestBody @Valid IdDto idDto) {
        clientService.deleteClient(idDto.getId());
        return R.ok("删除成功");
    }

}
