package com.insurance.rocketmq.consumer;

import com.alibaba.fastjson.JSON;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.service.InsuranceOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author:Oliver
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:订单下单成功消费者
 */
@Component
@Slf4j
@RocketMQMessageListener(
        topic = "insurance_order_success_topic",
        consumerGroup = "insurance_order_success_topic_consumer_group",
        maxReconsumeTimes = 3,
        consumeMode = ConsumeMode.CONCURRENTLY
)
public class OrderSuccessConsumer implements RocketMQListener<OrderMsg> {

    @Autowired
    private InsuranceOrderService orderService;

    @Override
    public void onMessage(OrderMsg orderMsg) {
        log.info("订单下单成功队列消费者收到消息:{}", JSON.toJSONString(orderMsg));
        orderService.handleSuccessOrder(orderMsg);
    }
}
