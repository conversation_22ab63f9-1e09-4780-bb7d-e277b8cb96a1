package com.insurance.rocketmq.producer;

import com.alibaba.fastjson.JSONObject;
import com.insurance.constant.RocketMqConstant;
import com.insurance.rocketmq.msg.InsurancePlanMsg;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.util.OrderNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Random;

/**
 * @Author:Oliver
 * @Date: 2025/4/30
 * @Version 1.0
 * @apiNote:
 */
@Slf4j
@Component
public class MqProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    public void sendProgressingMsg(String orderNo) {
        log.info("发送请求中信息-订单号:{}", orderNo);
        OrderMsg orderMsg = OrderMsg.builder().orderNo(orderNo).build();
        rocketMQTemplate.asyncSend(RocketMqConstant.ORDER_PROGRESSING_TOPIC, orderMsg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送请求中信息消息发送成功,orderMsg={}，消息ID={}", JSONObject.toJSONString(orderMsg), sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送请求中信息消息发送失败，orderMsg={}，异常信息={}", JSONObject.toJSONString(orderMsg), throwable.getMessage());
            }
        });
    }

    public void sendSuccessMsg(String orderNo) {
        log.info("发送下单成功信息-订单号:{}", orderNo);
        Random RANDOM = new Random();
        double randomValue = 0.01 + (1000.00 - 0.01) * RANDOM.nextDouble();
        OrderMsg orderMsg = OrderMsg.builder()
                .orderNo(orderNo)
                .insuranceNo(OrderNoUtil.generateInsuranceNo())
                .price(BigDecimal.valueOf(randomValue).setScale(2, RoundingMode.HALF_UP))
                .build();
        rocketMQTemplate.asyncSend(RocketMqConstant.ORDER_SUCCESS_TOPIC, orderMsg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送下单成功信息发送成功,orderMsg={}，消息ID={}", JSONObject.toJSONString(orderMsg), sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送下单成功消息发送失败，orderMsg={}，异常信息={}", JSONObject.toJSONString(orderMsg), throwable.getMessage());
            }
        });
    }

    public void sendFailMsg(String orderNo) {
        log.info("发送下单失败成功信息-订单号:{}", orderNo);
        OrderMsg orderMsg = OrderMsg.builder().orderNo(orderNo).build();
        rocketMQTemplate.asyncSend(RocketMqConstant.ORDER_FAIL_TOPIC, orderMsg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送下单失败信息发送成功,orderMsg={}，消息ID={}", JSONObject.toJSONString(orderMsg), sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送下单失败消息发送失败，orderMsg={}，异常信息={}", JSONObject.toJSONString(orderMsg), throwable.getMessage());
            }
        });
    }

    public void sendAutomaticOrderingMsg(String orderNo) {
        log.info("发送自动化下单信息-订单号:{}", orderNo);
        OrderMsg orderMsg = OrderMsg.builder().orderNo(orderNo).build();
        rocketMQTemplate.asyncSend(RocketMqConstant.AUTOMATIC_ORDERING_TOPIC, orderMsg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送自动化下单信息发送成功,orderMsg={}，消息ID={}", JSONObject.toJSONString(orderMsg), sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送自动化下单信息发送失败，orderMsg={}，异常信息={}", JSONObject.toJSONString(orderMsg), throwable.getMessage());
            }
        });
    }

    public void sendCreatePayablePlanMsg(InsurancePlanMsg insurancePlanMsg) {
        log.info("发送创建应收应付信息-消息体:{}", JSONObject.toJSONString(insurancePlanMsg));
        rocketMQTemplate.asyncSend(RocketMqConstant.RECEIVABLE_AND_PAYABLE_TOPIC, insurancePlanMsg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送创建应收应付信息发送成功,消息体={}，消息ID={}", JSONObject.toJSONString(insurancePlanMsg), sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送创建应收应付信息发送失败，消息体={}，异常信息={}", JSONObject.toJSONString(insurancePlanMsg), throwable.getMessage());
            }
        });
    }

    public void sendQueenTestMsg() {
        log.info("============================>测试发送测试队列信息");
        OrderMsg orderMsg = OrderMsg.builder().orderNo("123").build();
        rocketMQTemplate.asyncSend("insurance_order_test_topic", orderMsg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送测试信息发送成功,orderMsg={}，消息ID={}", JSONObject.toJSONString(orderMsg), sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送测试信息发送失败，orderMsg={}，异常信息={}", JSONObject.toJSONString(orderMsg), throwable.getMessage());
            }
        });
    }
}
