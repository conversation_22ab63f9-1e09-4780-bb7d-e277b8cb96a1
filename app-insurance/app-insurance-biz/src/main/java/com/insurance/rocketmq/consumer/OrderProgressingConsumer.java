package com.insurance.rocketmq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.service.InsuranceOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author:Oliver
 * @Date: 2025/4/29
 * @Version 1.0
 * @apiNote:订单请求中消费者
 */
@Component
@Slf4j
@RocketMQMessageListener(
        topic = "insurance_order_progressing_topic",
        consumerGroup = "insurance_order_progressing_topic_consumer_group",
        maxReconsumeTimes = 3,
        consumeMode = ConsumeMode.CONCURRENTLY
)
public class OrderProgressingConsumer implements RocketMQListener<OrderMsg> {

    @Autowired
    private InsuranceOrderService orderService;

    @Override
    public void onMessage(OrderMsg orderMsg) {
        log.info("订单请求中队列消费者收到消息：{}", JSONObject.toJSONString(orderMsg));
        orderService.handleProcessingOrder(orderMsg);
    }
}
