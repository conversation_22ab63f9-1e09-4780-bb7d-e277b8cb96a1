package com.insurance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.insurance.entity.CreditCard;
import com.insurance.entity.CreditCardStatement;
import com.insurance.entity.InsuranceOrder;
import com.insurance.enums.CreditCardPayStatusEnum;
import com.insurance.mapper.CreditCardStatementMapper;
import com.insurance.service.CreditCardStatementService;
import com.insurance.util.ExchangeRateUtils;
import com.insurance.util.OrderNoUtil;
import com.insurance.vo.settlement.RateDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.Date;

@Service
@Slf4j
public class CreditCardStatementServiceImpl extends ServiceImpl<CreditCardStatementMapper, CreditCardStatement> implements CreditCardStatementService {

    @Autowired
    private CreditCardStatementMapper creditCardStatementMapper;
    @Autowired
    private ExchangeRateUtils exchangeRateUtils;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCreditCardStatement(CreditCard creditCard, InsuranceOrder order, Integer businessType, String targetKey, Long targetId, Integer status) {
        RateDetail rateDetail = exchangeRateUtils.getRateDetail(order.getFkCurrencyTypeNum(), "CNY");
        CreditCardStatement cardStatement = CreditCardStatement.builder()
                .fkCreditCardId(creditCard.getId())
                .num(OrderNoUtil.generateOrderNo())
                .businessType(businessType)
                .relationTargetKey(targetKey)
                .relationTargetId(targetId)
                //下单时的币种
                .fkCurrencyTypeNum(order.getFkCurrencyTypeNum())
                //下单时币种金额
                .amount(order.getInsuranceAmount())
                .exchangeRateRmb(rateDetail.getRate())
                .amountRmb(order.getInsuranceAmount().multiply(rateDetail.getRate()).setScale(2, RoundingMode.HALF_UP))
                .status(status)
                .build();
        cardStatement.setGmtCreate(new Date());
        cardStatement.setGmtModified(new Date());
        cardStatement.setGmtCreateUser(order.getGmtCreateUser());
        cardStatement.setGmtModifiedUser(order.getGmtModifiedUser());
        creditCardStatementMapper.insert(cardStatement);
    }
}
