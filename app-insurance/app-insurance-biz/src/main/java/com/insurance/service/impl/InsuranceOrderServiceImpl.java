package com.insurance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.util.R;
import com.common.security.util.SecurityUtils;
import com.insurance.config.JsonRedisService;
import com.insurance.config.RedisService;
import com.insurance.config.WxConfig;
import com.insurance.constant.RedisConstant;
import com.insurance.constant.RocketMqConstant;
import com.insurance.dto.order.*;
import com.insurance.entity.InsuranceOrder;
import com.insurance.entity.InsuranceOrderSettlement;
import com.insurance.entity.ProductType;
import com.insurance.enums.OrderStatusEnum;
import com.insurance.enums.PaymentTypeEnum;
import com.insurance.enums.ProductTypeEnum;
import com.insurance.enums.SettlementStatusEnum;
import com.insurance.exception.InsuranceGlobalException;
import com.insurance.mapper.InsuranceOrderMapper;
import com.insurance.mapper.InsuranceOrderSettlementMapper;
import com.insurance.mapper.ProductTypeMapper;
import com.insurance.rocketmq.msg.InsurancePlanMsg;
import com.insurance.rocketmq.msg.OrderMsg;
import com.insurance.rocketmq.producer.MqProducer;
import com.insurance.service.CreditCardService;
import com.insurance.service.InsuranceOrderService;
import com.insurance.strategy.factory.AutomaticOrderingFactory;
import com.insurance.util.CardUtils;
import com.insurance.util.OrderNoUtil;
import com.insurance.util.PartnerUserUtils;
import com.insurance.util.SecureEncryptUtil;
import com.insurance.vo.insurance.order.EncryptCreditCardVo;
import com.insurance.vo.insurance.order.OrderDetailVo;
import com.insurance.vo.insurance.order.OrderStatisticsVo;
import com.insurance.vo.partner.PartnerUserInfo;
import com.payment.dto.common.PayRequest;
import com.payment.enums.PayStatusEnum;
import com.payment.enums.PayTypeEnum;
import com.payment.feign.RemotePaymentService;
import com.payment.vo.PayResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * @Author:Oliver
 * @Date: 2025/4/30
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class InsuranceOrderServiceImpl extends ServiceImpl<InsuranceOrderMapper, InsuranceOrder> implements InsuranceOrderService {

    @Value("${maxFailures:5}")
    private int MAX_FAILURES;
    @Autowired
    private RedisService redisService;
    @Autowired
    private AutomaticOrderingFactory orderingStrategy;
    @Autowired
    private MqProducer mqProducer;
    @Autowired
    private InsuranceOrderMapper orderMapper;
    @Autowired
    private ProductTypeMapper productTypeMapper;
    @Autowired
    private InsuranceOrderSettlementMapper orderSettlementMapper;
    @Autowired
    private WxConfig wxConfig;
    @Autowired
    private RemotePaymentService remotePaymentService;
    @Autowired
    private JsonRedisService jsonRedisService;
    @Autowired
    private CreditCardService creditCardService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public InsuranceOrder submitOrder(GeneralOrderRequest request) {
        InsuranceOrder order = initOrder(request);

        // 生成密钥 & 卡号
        String secretKey = request.getSecretKey();

        // 加密卡号 & 后三位
        String encryptedCardNumber = request.getEncryptedCardNumber();
        String cvc = request.getCvc();

        //填充订单信息
        order.setOrderJson(initOrderInfo(ProductTypeEnum.getEnumByCode(request.getProductTypeKey()),
                request.getFormData(),
                order.getOrderNum(),
                encryptedCardNumber,
                cvc));
        orderMapper.insert(order);

        // 构建 Redis Hash key 和数据
        CreditCardEncryptionData encryptionData = CreditCardEncryptionData.builder()
                .encryptedCardNumber(encryptedCardNumber)
                .endingNumber(cvc)
                .secretKey(secretKey)
                .build();

        // 存入 Redis-后续由自动化那边直接读取，可以不用存入redis,测试环境可保留
        redisService.setObjectAsJson(RedisConstant.ORDER_ENCRYPT_CARD + order.getOrderNum(), encryptionData, 1, TimeUnit.DAYS);

        //将订单信息放入缓存-key就是密钥+"-"+订单号
        jsonRedisService.setObjectAsJson(RedisConstant.ORDER_INFO + request.getSecretKey() + "-" + order.getOrderNum()
                , order.getOrderJson(), 1, TimeUnit.DAYS);


        //发送rocketmq消息,尝试下单-仅针对于信用卡直接支付,如果是微信支付,先不发送自动化下单消息,等支付成功后再发送
        if (Objects.isNull(request.getPayType()) || request.getPayType().equals(PaymentTypeEnum.CREDIT_CARD.getCode())) {
            mqProducer.sendAutomaticOrderingMsg(order.getOrderNum());
        }
        return order;
    }

    @Override
    public void sendRequest(String orderNo) {
        log.info("收到下单请求=======开始处理订单,订单号:{}", orderNo);
        String lockValue = UUID.randomUUID().toString();
        //这里使用redis的分布式锁,防止多个请求同时下单
        boolean lockAcquired = redisService.tryLock(RedisConstant.ORDER_LOCK_KEY, lockValue, RedisConstant.LOCK_EXPIRE_SECONDS);

        if (!lockAcquired) {
            log.info("已有订单正在下单中,本次请求放弃");
            return;
        }

        try {
            // 1. 优先从 Redis 中未请求的订单（ZSet 中不存在）
            InsuranceOrder orderToProcess = getOneAvailableOrder(orderNo);

            if (Objects.isNull(orderToProcess)) {
                log.info("无待请求订单,无可用订单可供处理");
                return;
            }
            log.info("开始请求订单:{},保险产品类型key:{}", orderToProcess.getOrderNum(), orderToProcess.getProductTypeKey());
            // 使用工厂获取具体的策略实例，并执行下单
            orderingStrategy.getOrderingStrategy(ProductTypeEnum.getEnumByCode(orderToProcess.getProductTypeKey())).sendOrderRequest(orderToProcess);
        } catch (Exception e) {
            log.error("触发自动化下单异常", e);
            log.error("释放锁失败或锁已过期,失败信息:{}", e.getMessage());
        } finally {
            redisService.releaseLock(RedisConstant.ORDER_LOCK_KEY, lockValue);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleProcessingOrder(OrderMsg orderMsg) {
        log.info("处理请求中订单：{}", JSONObject.toJSONString(orderMsg));
        withOrder(orderMsg.getOrderNo(), order -> {
            order.setOrderStatus(OrderStatusEnum.PROGRESSING.getCode());
            orderMapper.updateById(order);
            redisService.zAdd(RedisConstant.ORDER_PROGRESSING_KEY, orderMsg.getOrderNo(), System.currentTimeMillis(), 1200);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public void handleSuccessOrder(OrderMsg orderMsg) {
        Thread.sleep(500);
        log.info("处理下单成功订单：{}", JSONObject.toJSONString(orderMsg));
        withOrder(orderMsg.getOrderNo(), order -> {
            order.setInsuranceNum(orderMsg.getInsuranceNo());
            order.setInsuranceAmount(orderMsg.getPrice());
            order.setOrderStatus(OrderStatusEnum.SUCCESS.getCode());
            order.setOrderTime(new Date());
            orderMapper.updateById(order);
            redisService.zRemove(RedisConstant.ORDER_PROGRESSING_KEY, orderMsg.getOrderNo());
            redisService.deleteHashKey(RedisConstant.ORDER_ENCRYPT_CARD + orderMsg.getOrderNo());

            //创建结算记录
            InsuranceOrderSettlement settlement = InsuranceOrderSettlement.builder()
                    .fkInsuranceOrderId(order.getId())
                    .fkPayablePlanId(0L)
                    .fkPaymentFormItemId(0L)
                    .statusSettlement(SettlementStatusEnum.TO_BE_CONFIRM.getCode())
                    .build();
            settlement.setGmtCreate(new Date());
            settlement.setGmtModified(new Date());
            settlement.setGmtCreateUser(order.getGmtCreateUser());
            settlement.setGmtModifiedUser(order.getGmtModifiedUser());
            orderSettlementMapper.insert(settlement);

            //发送应收应付消息
            ProductType productType = productTypeMapper.selectById(order.getFkProductTypeId());
            if (Objects.nonNull(productType)) {
                InsurancePlanMsg insurancePlanMsg = InsurancePlanMsg.builder()
                        .fkTypeKey("m_insurance_order")
                        .fkCurrencyTypeNum(productType.getFkCurrencyTypeNum())
                        .fkTypeTargetId(order.getId())
                        .tuitionAmount(order.getInsuranceAmount())
                        .payablePlanCommissionRate(new BigDecimal(productType.getCommissionRateAgent()))
                        .receivablePlanCommissionRate(new BigDecimal(productType.getCommissionRate()))
                        .build();
                mqProducer.sendCreatePayablePlanMsg(insurancePlanMsg);
            }

            //如果是微信支付,创建信用卡流水记录
            if (Objects.nonNull(order.getPaymentType()) && order.getPaymentType().equals(PaymentTypeEnum.WX.getCode())) {
                creditCardService.CreditCardPaySuccess(order);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public void handleFailOrder(OrderMsg orderMsg, Integer type) {
        Thread.sleep(500);
        log.info("处理下单失败订单：{}", JSONObject.toJSONString(orderMsg));
        withOrder(orderMsg.getOrderNo(), order -> {
            Long l = redisService.zRemove(RedisConstant.ORDER_PROGRESSING_KEY, orderMsg.getOrderNo());
            if (l < 1) {
                log.info("订单 {} 已被处理,请勿重复处理", orderMsg.getOrderNo());
            }
            if (type.equals(2)) {
                //直接失败
                order.setOrderStatus(OrderStatusEnum.FAIL.getCode());
                orderMapper.updateById(order);
                redisService.deleteHashKey(RedisConstant.ORDER_ENCRYPT_CARD + orderMsg.getOrderNo());
                return;
            }
            long count = redisService.incr(RedisConstant.ORDER_FAILED_KEY + orderMsg.getOrderNo(), 1);
            log.info("订单 {} 当前失败次数：{}", orderMsg.getOrderNo(), count);
            if (count >= MAX_FAILURES) {
                log.error("订单 {} 失败次数已达上限，设置订单为下单失败", orderMsg.getOrderNo());
                redisService.del(RedisConstant.ORDER_FAILED_KEY + orderMsg.getOrderNo());
                redisService.deleteHashKey(RedisConstant.ORDER_ENCRYPT_CARD + orderMsg.getOrderNo());
                order.setOrderStatus(OrderStatusEnum.FAIL.getCode());
                order.setOrderMessage(orderMsg.getErrorMsg());
                orderMapper.updateById(order);
                //如果是微信支付,创建信用卡流水记录
                if (Objects.nonNull(order.getPaymentType()) && order.getPaymentType().equals(PaymentTypeEnum.WX.getCode())) {
                    creditCardService.CreditCardPayFail(order);
                }
            } else {
                order.setOrderStatus(OrderStatusEnum.PENDING.getCode());
                orderMapper.updateById(order);
            }
        });
    }

    @Override
    @SneakyThrows
    public EncryptCreditCardVo encryptCreditCard(EncryptCreditCardDto creditCardDto) {
        if (Objects.isNull(creditCardDto)) {
            throw new InsuranceGlobalException("机密信息不能为空");
        }
        String secretKey = CardUtils.getCardSecretKey();
        String encryptCardNumber = SecureEncryptUtil.encrypt(creditCardDto.getCardNumber(), secretKey);
        String encryptCvc = SecureEncryptUtil.encrypt(creditCardDto.getCvc(), secretKey);
        return EncryptCreditCardVo.builder()
                .encryptedCardNumber(encryptCardNumber)
                .encryptedCvc(encryptCvc)
                .secretKey(secretKey)
                .build();
    }

    @Override
    public List<InsuranceOrder> getOrderList(Integer orderStatus, String date) {
        //2-已完成;1-下单中;-2-下单失败
        return orderMapper.selectOrderList(PartnerUserUtils.getCurrentAgentId(), orderStatus, date);
    }

    @Override
    public OrderDetailVo getOrderDetail(Long orderId) {
        return orderMapper.selectOrderDetailById(orderId);
    }

    @Override
    public OrderStatisticsVo getOrderStatistics(String date) {
        OrderStatisticsVo orderStatistics = orderMapper.selectOrderCountByDate(PartnerUserUtils.getCurrentAgentId(), date);
        return orderStatistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayResponse submitWxPayOrder(GeneralOrderRequest request) {
        if (Objects.isNull(request.getAmount())) {
            log.error("提交微信订单-支付金额缺失:{}", JSONObject.toJSONString(request));
            throw new InsuranceGlobalException("支付金额缺失");
        }
        if (Objects.isNull(request.getOpenId())) {
            log.error("提交微信订单-OpenId缺失:{}", JSONObject.toJSONString(request));
            throw new InsuranceGlobalException("OpenId信息缺失");
        }
        //生成订单信息
        InsuranceOrder order = submitOrder(request);
        //调用支付服务获取支付参数
        PayRequest payRequest = PayRequest.builder()
                .appId(wxConfig.getAppId())
                .type(PayTypeEnum.WX_JSAPI.getCode())
                .orderNo(order.getOrderNum())
                .orderId(order.getId())
                //.amount(request.getAmount())
                //先用一分钱调试
                .amount(BigDecimal.ONE)
                .subject(order.getInsurantName() + "保单支付")
                .description(order.getInsurantName() + "保单支付")
                .openId(request.getOpenId())
                .notifyTopic(RocketMqConstant.AUTOMATIC_ORDERING_TOPIC)
                .build();
        log.info("提交微信支付订单:{}", JSONObject.toJSONString(payRequest));
        R<PayResponse> result = remotePaymentService.unifiedOrder(payRequest);
        if (!result.isSuccess() && Objects.isNull(result.getData())) {
            log.error("创建微信订单失败:{}", JSONObject.toJSONString(result));
            throw new InsuranceGlobalException(500, "创建微信订单失败");
        }
        log.info("创建微信订单成功===========>订单号:{}", order.getOrderNum());
        return result.getData();
    }

    private void withOrder(String orderNum, Consumer<InsuranceOrder> action) {
        if (StringUtils.isBlank(orderNum)) {
            log.error("订单号为空");
            return;
        }
        InsuranceOrder order = orderMapper.selectByOrderNum(orderNum);
        if (Objects.isNull(order)) {
            log.error("订单不存在,订单号:{}", orderNum);
            return;
        }
        action.accept(order);
    }

    /**
     * 获取一笔可用的未处理订单
     */
    private InsuranceOrder getOneAvailableOrder(String orderNo) {
        // 1:判断是否已有正在处理中的订单
        Long progressingCount = redisService.zCard(RedisConstant.ORDER_PROGRESSING_KEY);
        if (progressingCount != null && progressingCount > 0) {
            log.info("当前已有订单正在处理,请求中数量：{},跳过本次自动化下单", progressingCount);
            return null;
        }
        // 2:查数据库中状态是待请求和下单中的订单,时间升序查询
        List<InsuranceOrder> initOrders = new ArrayList<>();
        //如果有指定订单号,优先用指定订单号下单
        if (StringUtils.isNotBlank(orderNo)) {
            InsuranceOrder priorityOrder = orderMapper.selectByOrderNum(orderNo);
            if (Objects.nonNull(priorityOrder)) {
                initOrders.add(priorityOrder);
            }
        }
        //待请求和下单中的订单
        List<InsuranceOrder> insuranceOrders = orderMapper.selectPendingOrders(10);
        initOrders.addAll(insuranceOrders);
        if (CollectionUtils.isEmpty(initOrders)) {
            return null;
        }

        // 3:过滤掉Redis ZSet 中已有的“请求中”订单
        for (InsuranceOrder order : initOrders) {
            String currentOrderNum = order.getOrderNum();
            Double score = redisService.zScore(RedisConstant.ORDER_PROGRESSING_KEY, currentOrderNum);
            if (Objects.isNull(score)) {
                // Redis 中没有，说明这笔订单可以处理,这里不需要加入到redis,请求中队列会加入
                return order;
            }
        }
        log.info("全部待请求订单均处于处理中,放弃本次请求");
        return null;
    }

    private InsuranceOrder initOrder(GeneralOrderRequest request) {
        PartnerUserInfo partnerInfo = PartnerUserUtils.getPartnerInfo(SecurityUtils.getUser().getId());
        ProductType productType = productTypeMapper.selectByKey(request.getProductTypeKey());
        if (Objects.isNull(productType)) {
            throw new InsuranceGlobalException("产品类型不存在");
        }
        InsuranceOrder insuranceOrder = InsuranceOrder.builder()
                .orderNum(OrderNoUtil.generateOrderNo())
                .insuranceType(request.getInsuranceType())
                .insuranceStartTime(request.getInsuranceStartTime())
                .insuranceEndTime(request.getInsuranceEndTime())
                .insurantName(request.getInsurantName())
                .insurantLastName(request.getInsurantLastName())
                .insurantFirstName(request.getInsurantFirstName())
                .insurantPassportNum(request.getInsurantPassportNum())
                .orderRemark(request.getRemark())
                .fkTenantId(Long.parseLong(SecurityUtils.getUser().getFkTenantId()))
                .fkCompanyId(partnerInfo.getCompanyId())
                .fkAgentId(partnerInfo.getAgentId())
                .fkPartnerUserId(partnerInfo.getPartnerUserId())
                .orderStatus(OrderStatusEnum.PENDING.getCode())
                .insurantMobile(request.getMobile())
                .insurantEmail(request.getEmail())
                .fkInsuranceCompanyId(productType.getFkInsuranceCompanyId())
                .fkProductTypeId(productType.getId())
                .fkCurrencyTypeNum(productType.getFkCurrencyTypeNum())
                .insurantNationality(request.getInsurantNationality())
                .insurantGender(request.getInsurantGender())
                .enrollmentTime(request.getEnrollmentTime())
                .graduationTime(request.getGraduationTime())
                .insurantBirthday(request.getInsurantBirthday())
                //微信支付相关
                .mpPaymentOpenid(request.getOpenId())
                .paymentType(Objects.isNull(request.getPayType()) ? PaymentTypeEnum.CREDIT_CARD.getCode() : request.getPayType())
                .insuranceAmount(request.getAmount())
                .mpPaymentStatus(Objects.nonNull(request.getPayType())
                        && request.getPayType().equals(PaymentTypeEnum.WX.getCode()) ? PayStatusEnum.UN_PAID.getCode() : null)
                .build();
        insuranceOrder.setGmtCreate(new Date());
        insuranceOrder.setGmtModified(new Date());
        insuranceOrder.setGmtCreateUser(SecurityUtils.getUser().getLoginId());
        insuranceOrder.setGmtModifiedUser(SecurityUtils.getUser().getLoginId());
        return insuranceOrder;
    }

    private String initOrderInfo(ProductTypeEnum type, JSONObject formData, String... args) {
        switch (type) {
            case NIB:
                log.info("初始化NIB订单信息");
                NibFormData nibFormData = JSONObject.parseObject(formData.toJSONString(), NibFormData.class);
                nibFormData.setOrderNo(args[0]);
                nibFormData.getStep4().setCard_number(args[1]);
                nibFormData.getStep4().setCvv(args[2]);
                return JSONObject.toJSONString(nibFormData);
            case ALLIAN:
                log.info("初始化安联订单信息");
                AllianFormData allianFormData = JSONObject.parseObject(formData.toJSONString(), AllianFormData.class);
                allianFormData.setOrderNo(args[0]);
                allianFormData.getStep3().setCard_number(args[1]);
                allianFormData.getStep3().setCvc(args[2]);
                return JSONObject.toJSONString(allianFormData);
            case NIB_LOGIN:
                log.info("初始化NIB登录订单信息");
                NibLoginFormData nibLoginFormData = JSONObject.parseObject(formData.toJSONString(), NibLoginFormData.class);
                nibLoginFormData.setOrderNo(args[0]);
                nibLoginFormData.getStep4().setCard_number(args[1]);
                nibLoginFormData.getStep4().setCvv(args[2]);
                return JSONObject.toJSONString(nibLoginFormData);
            default:
                return null;
        }
    }

}
