package com.insurance.event;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.context.ApplicationEvent;

/**
 * @Author:<PERSON>
 * @Date: 2025/6/27
 * @Version 1.0
 */
public class SendNotifyMessageEvent extends ApplicationEvent {

    @Schema(description = "信用卡id")
    private Long creditCardId;

    @Schema(description = "消息类型:额度不足/支付失败/出账还款提醒")
    private String messageType;

    public SendNotifyMessageEvent(Object source, Long creditCardId, String messageType) {
        super(source);
        this.creditCardId = creditCardId;
        this.messageType = messageType;
    }

    public Long getCreditCardId() {
        return creditCardId;
    }

    public void setPartnerUserIds(Long creditCardId) {
        this.creditCardId = creditCardId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

}
