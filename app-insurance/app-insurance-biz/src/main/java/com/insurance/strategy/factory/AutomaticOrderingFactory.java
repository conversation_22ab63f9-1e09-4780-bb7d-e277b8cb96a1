package com.insurance.strategy.factory;

import com.insurance.enums.ProductTypeEnum;
import com.insurance.strategy.service.AutomaticOrderingStrategy;
import com.insurance.strategy.service.impl.AllianAutomaticOrderingStrategy;
import com.insurance.strategy.service.impl.NibAutomaticOrderingStrategy;
import com.insurance.strategy.service.impl.NibLoginAutomaticOrderingStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * @Author:Oliver
 * @Date: 2025/5/7
 * @Version 1.0
 * @apiNote:自动化下单策略工厂
 */
@Component
public class AutomaticOrderingFactory {

    private final ApplicationContext context;

    @Autowired
    public AutomaticOrderingFactory(ApplicationContext context) {
        this.context = context;
    }

    public AutomaticOrderingStrategy getOrderingStrategy(ProductTypeEnum companyEnum) {
        switch (companyEnum) {
            case ALLIAN:
                return context.getBean(AllianAutomaticOrderingStrategy.class);
            case NIB:
                return context.getBean(NibAutomaticOrderingStrategy.class);
            case NIB_LOGIN:
                return context.getBean(NibLoginAutomaticOrderingStrategy.class);
            default:
                throw new IllegalArgumentException("Unknown order type: " + companyEnum);
        }
    }
}
