package com.insurance.strategy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.insurance.config.RedisService;
import com.insurance.dto.order.CreditCardEncryptionData;
import com.insurance.dto.order.NibFormData;
import com.insurance.enums.ProductTypeEnum;
import com.insurance.service.InsuranceOrderService;
import com.insurance.strategy.service.ApiEndpointService;
import com.insurance.util.FastFireAndForgetHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Author:Oliver
 * @Date: 2025/5/7
 * @Version 1.0
 * @apiNote:nib自动下单策略
 */
@Service
@Slf4j
public class NibAutomaticOrderingStrategy extends AbstractAutomaticOrderingStrategy {

    public NibAutomaticOrderingStrategy(RedisService redisService,
                                        ApiEndpointService apiEndpointService,
                                        InsuranceOrderService orderService) {
        super(redisService, apiEndpointService, orderService);
    }

    @Override
    protected String buildRequestJson(String orderInfo, String decryptCard, CreditCardEncryptionData data) {
        NibFormData formData = JSONObject.parseObject(orderInfo, NibFormData.class);
        formData.getStep4().setCard_number(decryptCard);
        formData.getStep4().setCvv(data.getEndingNumber());
        return JSONObject.toJSONString(formData);
    }

    @Override
    protected void sendAsyncRequest(String url, String requestBody) {
        log.info("NIB下单参数:{}", requestBody);
        FastFireAndForgetHttpClient.postJson(url, requestBody);
    }

    @Override
    public String getCompanyCode() {
        log.info("获取NIB公司编码");
        return ProductTypeEnum.NIB.getCode();
    }
}
