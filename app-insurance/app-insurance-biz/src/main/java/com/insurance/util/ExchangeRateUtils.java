package com.insurance.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insurance.config.RedisService;
import com.insurance.constant.RedisConstant;
import com.insurance.exception.InsuranceGlobalException;
import com.insurance.mapper.PartnerCenterMapper;
import com.insurance.vo.settlement.RateDetail;
import com.insurance.vo.settlement.RateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@Slf4j
public class ExchangeRateUtils {

    @Autowired
    private RedisService redisService;
    @Autowired
    private PartnerCenterMapper partnerCenterMapper;

    /**
     * 云市场分配的密钥Id
     */
    @Value("${exchangeRate.secretId:AKID2xfUvZm4f3pHw0GZxY1iFzsdFix3bCr8clgY}")
    private String secretId;

    /**
     * 云市场分配的密钥Key
     */
    @Value("${exchangeRate.secretKey:gNf9H71Kb93z7X5Q0G9K2jRusUR93T4O9aPB6WFw}")
    private String secretKey;

    /**
     * 接口地址
     */
    @Value("${exchangeRate.apiHost:https://service-p90qcght-1257101137.ap-shanghai.apigateway.myqcloud.com/release/exchange/convert}")
    private String apiHost;

    private static String calcAuthorization(String source, String secretId, String secretKey, String datetime)
            throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
        String signStr = "x-date: " + datetime + "\n" + "x-source: " + source;
        Mac mac = Mac.getInstance("HmacSHA1");
        Key sKey = new SecretKeySpec(secretKey.getBytes("UTF-8"), mac.getAlgorithm());
        mac.init(sKey);
        byte[] hash = mac.doFinal(signStr.getBytes("UTF-8"));
        String sig = new BASE64Encoder().encode(hash);

        String auth = "hmac id=\"" + secretId + "\", algorithm=\"hmac-sha1\", headers=\"x-date x-source\", signature=\"" + sig + "\"";
        return auth;
    }

    private static String urlEncode(Map<?, ?> map) throws UnsupportedEncodingException {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(String.format("%s=%s",
                    URLEncoder.encode(entry.getKey().toString(), "UTF-8"),
                    URLEncoder.encode(entry.getValue().toString(), "UTF-8")
            ));
        }
        return sb.toString();
    }

    public String getExchangeRate(String fromCurrency, String toCurrency) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
        String source = "market";
        Calendar cd = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String datetime = sdf.format(cd.getTime());
        // 签名
        String auth = calcAuthorization(source, secretId, secretKey, datetime);
        // 请求方法
        String method = "GET";
        // 请求头
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-Source", source);
        headers.put("X-Date", datetime);
        headers.put("Authorization", auth);

        // 查询参数
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put("amount", "1");
        queryParams.put("from", fromCurrency);
        queryParams.put("to", toCurrency);
        // body参数
        Map<String, String> bodyParams = new HashMap<String, String>();

        // url参数拼接
        String url = apiHost;
        if (!queryParams.isEmpty()) {
            url += "?" + urlEncode(queryParams);
        }

        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setRequestMethod(method);

            // request headers
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }

            // request body
            Map<String, Boolean> methods = new HashMap<>();
            methods.put("POST", true);
            methods.put("PUT", true);
            methods.put("PATCH", true);
            Boolean hasBody = methods.get(method);
            if (hasBody != null) {
                conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

                conn.setDoOutput(true);
                DataOutputStream out = new DataOutputStream(conn.getOutputStream());
                out.writeBytes(urlEncode(bodyParams));
                out.flush();
                out.close();
            }

            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;

            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println(e);
            e.printStackTrace();
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }


    /**
     * 获取汇率
     *
     * @param from
     * @param to
     * @return
     */
    public RateDetail getRateDetail(String from, String to) {
        if ("FCY".equals(to)) {
            to = "CNY";
        }
        log.info("获取汇率:{}->{}", from, to);

        //币种相同，返回1
        if (from.equals(to)) {
            return RateDetail.builder()
                    .from(from)
                    .to(to)
                    .updatetime(new Date())
                    .rate(BigDecimal.ONE)
                    .camount(BigDecimal.ONE)
                    .build();
        }
        //先查缓存
        String key = RedisConstant.RATE + String.format("%s-%s", from.toLowerCase(), to.toLowerCase());
        RateDetail cachedRate = redisService.get(key, RateDetail.class);
        if (Objects.nonNull(cachedRate)) {
            return cachedRate;
        }

        //查数据库
        BigDecimal rate = partnerCenterMapper.getExchangeRate(from, to);
        if (Objects.nonNull(rate)) {
            RateDetail rateDetail = RateDetail.builder()
                    .from(from)
                    .to(to)
                    .updatetime(new Date())
                    .rate(rate)
                    .camount(rate)
                    .build();
            redisService.setWithTodayExpire(key, rateDetail);
            return rateDetail;
        }
        //重新调用api接口-放入缓存-落库
        try {
            String result = getExchangeRate(from, to);
            log.info("获取汇率结果:{}", result);
            RateResult rateDetail = JSON.parseObject(result, RateResult.class);
            if (!rateDetail.getStatus().equals(0)) {
                log.error("获取汇率异常:{}", rateDetail.getMsg());
                throw new InsuranceGlobalException("获取汇率失败" + (StringUtils.isNotBlank(rateDetail.getMsg()) ? ":" + rateDetail.getMsg() : ""));
            }
            if (Objects.isNull(rateDetail.getResult()) || Objects.isNull(rateDetail.getResult().getRate())) {
                log.error("获取汇率异常,汇率结果为空:{}", JSONObject.toJSONString(rateDetail));
                throw new InsuranceGlobalException("获取汇率失败");
            }
            //落库
            partnerCenterMapper.insertExchangeRate(from, to, rateDetail.getResult().getRate());
            //放缓存
            redisService.setWithTodayExpire(key, rateDetail.getResult());
            return rateDetail.getResult();

        } catch (Exception e) {
            log.error("获取汇率失败:{}", e.getMessage());
            throw new InsuranceGlobalException("获取汇率失败");
        }
    }
}
