package com.insurance.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

public class OrderNoUtil {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    private static final Random RANDOM = new Random();

    /**
     * 生成订单号：格式为 yyyyMMddHHmmssSSS + 6位随机数
     * 示例：2025042915321278804162
     */
    public static String generateOrderNo() {
        String timestamp = DATE_FORMAT.format(new Date());

        // 生成6位随机数字，不足补0
        int randomNumber = RANDOM.nextInt(999999);
        String randomStr = String.format("%06d", randomNumber);

        return timestamp + randomStr;
    }

    /**
     * 生成结算批次号：格式为 yyyyMMddHHmmssSSS + 8位随机数
     * @return
     */
    public static String generateSettlementBatchNo() {
        String timestamp = DATE_FORMAT.format(new Date());

        // 生成6位随机数字，不足补0
        int randomNumber = RANDOM.nextInt(99999999);
        String randomStr = String.format("%06d", randomNumber);

        return timestamp + randomStr;
    }

        public static String generateInsuranceNo() {
            return UUID.randomUUID()
                    .toString()
                    .replaceAll("-", "")   // 移除短横线
                    .toUpperCase()         // 转成大写
                    .substring(0, 20);     // 截取前20位
    }
}
