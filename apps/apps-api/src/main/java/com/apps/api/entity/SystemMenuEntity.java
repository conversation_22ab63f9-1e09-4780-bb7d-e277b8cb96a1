package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_menu")
@Schema(description = "菜单")
public class SystemMenuEntity extends Model<SystemMenuEntity> {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @Schema(description="菜单Id")
    private Long id;

    @Schema(description = "平台应用Id")
    private Long fkPlatformId;

    @Schema(description = "平台应用CODE")
    private String fkPlatformCode;

    @Schema(description = "父菜单Id")
    private Long fkParentMenuId;

    @Schema(description = "菜单类型：0目录，1菜单，2按钮，3自定义")
    private Integer menuType;

    @Schema(description = "权限标识")
    private String permissionKey;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单名称（英文）")
    private String nameEn;

    @Schema(description = "路由路径")
    private String path;

    @Schema(description = "是否可见，0隐藏，1显示")
    private Integer isVisible;

    @Schema(description = "是否缓存，0否，1是")
    private Integer isKeepAlive;

    @Schema(description = "是否内嵌，0否，1是")
    private Integer isEmbedded;

    @Schema(description = "删除标志，0未删除，1已删除")
    private Integer isDelFlag;

    @Schema(description = "排序值，越小越靠前")
    private Integer viewOrder;

    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;


    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}