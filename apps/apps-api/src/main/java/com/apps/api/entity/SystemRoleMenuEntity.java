package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_role_menu")
@Schema(description = "角色菜单")
public class SystemRoleMenuEntity extends Model<SystemRoleMenuEntity> {
    private static final long serialVersionUID = 1L;


    /**
     * 角色和菜单权限关系Id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "角色和菜单权限关系Id")
    private Long id;

    /**
     * 角色Id
     */
    @Schema(description = "角色Id")
    private Long fkRoleId;

    /**
     * 菜单Id
     */
    @Schema(description = "菜单Id")
    private Long fkMenuId;

    /**
     * 权限：0禁止/1允许，禁止权限大于允许权限
     */
    @Schema(description = "权限：0禁止/1允许，禁止权限大于允许权限")
    private Integer permission;


    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;


    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}