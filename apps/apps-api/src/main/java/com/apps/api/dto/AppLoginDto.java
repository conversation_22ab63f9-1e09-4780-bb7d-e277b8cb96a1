package com.apps.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * app登录DTO
 */
@Data
public class AppLoginDto {

    @Schema(description = "小程序登录code")
    private String code;

    @Schema(description = "验证码/密码-根据登录方式区分")
    @NotBlank(message = "验证码/密码不能为空")
    private String certificate;

    @Schema(description = "用户名/手机号-根据登录方式区分")
    @NotBlank(message = "用户名/手机号不能为空")
    private String account;

    @Schema(description = "用户注册平台应用CODE")
    @NotBlank(message = "用户注册平台应用CODE不能为空")
    private String formPlatformCode;

    @Schema(description = "用户注册平台应用Id")
    @NotNull(message = "用户注册平台应用Id不能为空")
    private Long formPlatformId;

    @Schema(description = "登录方式：微信登录-MP;账号密码登录:ACCOUNT;验证码:VCODE")
    @NotBlank(message = "登录方式不能为空")
    private String loginType;


}
