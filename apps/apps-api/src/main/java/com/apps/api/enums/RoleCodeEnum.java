package com.apps.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 角色code枚举
 */

@Getter
@AllArgsConstructor
public enum RoleCodeEnum {

    ADMIN("ADMIN", "管理员", "PARTNER"),
    COUNSELOR("COUNSELOR", "顾问", "PARTNER"),
    FINANCE("FINANCE", "佣金结算负责人", "PARTNER"),
    COPYWRINTING("COPYWRINTING", "文案专员", "PARTNER"),
    DIRECTOR("DIRECTOR", "企业负责人", "PARTNER"),
    STUDENT("STUDENT", "学生", "COUPON"),
    STAFF("STAFF", "员工", "COUPON"),
    AGENT("AGENT", "代理", "COUPON"),
    ;

    private String code;
    private String msg;
    private String platform;

    public static String getEnumByCode(String code) {
        for (RoleCodeEnum value : RoleCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }

    private final static Map<String, RoleCodeEnum> ROLE_CODE_ENUM_MAP = new HashMap<>();

    static {
        for (RoleCodeEnum value : RoleCodeEnum.values()) {
            ROLE_CODE_ENUM_MAP.put(value.getCode(), value);
        }
    }

    public static RoleCodeEnum getRoleCodeEnumByCode(String code) {
        return ROLE_CODE_ENUM_MAP.get(code);
    }

    public static boolean isCounselorOrCopywriter(String roleCode) {
        return RoleCodeEnum.COUNSELOR.getCode().equals(roleCode) || RoleCodeEnum.COPYWRINTING.getCode().equals(roleCode);
    }


}
