package com.apps.api.dto.partner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/1/16  10:10
 * @Version 1.0
 * partner用户保存
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SavePartnerUserDto {

    @Schema(description = "平台/应用Code")
    @NotBlank(message = "平台/应用Code不能为空")
    private String platformCode;

    @Schema(description = "平台/应用Id")
    @NotNull(message = "平台/应用Id不能为空")
    private Long platformId;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "电子邮箱")
    @NotBlank(message = "电子邮箱不能为空")
    @Email(message = "电子邮箱格式不正确")
    private String email;

    @Schema(description = "角色ID")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @Schema(description = "创建人LoginId")
    @NotBlank(message = "创建人LoginId不能为空")
    private String createUser;
}
