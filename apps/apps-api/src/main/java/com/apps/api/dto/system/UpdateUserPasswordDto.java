package com.apps.api.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/3/11
 * @Version 1.0
 * @apiNote: 修改密码
 */
@Data
public class UpdateUserPasswordDto {

    @Schema(description = "旧密码-加密后")
    private String oldPassword;

    @Schema(description = "新密码-加密后")
    private String newPassword;

    @Schema(description = "新密码二次确认-加密后")
    private String confirmNewPassword;
}
