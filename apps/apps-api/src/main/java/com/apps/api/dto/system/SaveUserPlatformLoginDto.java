package com.apps.api.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/1/9  11:14
 * @Version 1.0
 */
@Data
public class SaveUserPlatformLoginDto {

    @Schema(description="租户Id")
    private Long fkTenantId;

    @Schema(description="用户Id")
    private Long fkUserId;

    @Schema(description="平台应用Id")
    private Long fkPlatformId;

    @Schema(description="平台应用CODE")
    private String fkPlatformCode;

    @Schema(description="登陆用户Id")
    private String loginId;

    @Schema(description="登陆用户密码")
    private String loginPs;

    @Schema(description="盐值")
    private String salt;

    @Schema(description="mp_openid")
    private String miniProgramOpenid;

    @Schema(description="mp_uid")
    private String miniProgramUid;

}
