package com.apps.api.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/4/22
 * @Version 1.0
 * @apiNote:更新用户信息DTO
 */
@Data
public class UpdateUserInfoDto {

    @Schema(description = "用户注册平台应用Id")
    @NotNull(message = "用户注册平台应用Id不能为空")
    private Long fromPlatformId;

    @Schema(description = "用户注册平台应用CODE")
    @NotBlank(message = "用户注册平台应用CODE不能为空")
    private String fromPlatformCode;

    @Schema(description = "性别：0女/1男")
    private Integer gender;

    @Schema(description = "身份证号")
    private String identityCard;

    @Schema(description = "手机区号")
    private String mobileAreaCode;

    @Schema(description = "移动电话")
    private String mobile;

    @Schema(description = "电话区号")
    private String telAreaCode;

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "微信昵称")
    private String wechatNickname;

    @Schema(description = "微信头像URL")
    private String wechatIconUrl;
}
