package com.apps.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 角色code枚举
 */

@Getter
@AllArgsConstructor
public enum PlatformCodeEnum {

    PARTNER("PARTNER", "伙伴"),
    COUPON("COUPON", "优惠券"),
    PMP("PMP", "PMP"),
    ;

    private String code;

    private String msg;

    public static String getEnumByCode(String code) {
        for (PlatformCodeEnum value : PlatformCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }

}
