package com.apps.api.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * 短信响应类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsResp {
    @JsonProperty("SendStatusSet")
    private List<SendStatus> sendStatusSet;
    @JsonProperty("RequestId")
    private String requestId;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SendStatus {
        @JsonProperty("SerialNo")
        private String serialNo;
        @JsonProperty("PhoneNumber")
        private String phoneNumber;
        @JsonProperty("Fee")
        private int fee;
        @JsonProperty("SessionContext")
        private String sessionContext;
        @JsonProperty("Code")
        private String code;
        @JsonProperty("Message")
        private String message;
        @JsonProperty("IsoCode")
        private String isoCode;

    }

}
