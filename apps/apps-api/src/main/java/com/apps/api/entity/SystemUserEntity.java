package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_user")
@Schema(description = "系统用户")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SystemUserEntity extends Model<SystemUserEntity> {
    private static final long serialVersionUID = 1L;


    /**
     * 用户Id
     */
    @Schema(description="用户Id")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @Schema(description="租户Id")
    private Long fkTenantId;

    /**
     * 用户注册平台应用Id
     */
    @Schema(description="用户注册平台应用Id")
    private Long fkFromPlatformId;

    /**
     * 用户注册平台应用CODE
     */
    @Schema(description="用户注册平台应用CODE")
    private String fkFromPlatformCode;

    /**
     * 用户编号，U00100000001，3位平台Id8位用户Id
     */
    @Schema(description="用户编号，U00100000001，3位平台Id8位用户Id")
    private String num;

    /**
     * 姓名（中文）
     */
    @Schema(description=" 姓名（中文）")
    private String name;

    /**
     * 姓名（英文）
     */
    @Schema(description="姓名（英文）")
    private String nameEn;

    /**
     * 姓（拼音）
     */
    @Schema(description="姓（拼音）")
    private String lastNamePy;

    /**
     * 名（拼音）
     */
    @Schema(description="名（拼音）")
    private String firstNamePy;

    /**
     * 昵称
     */
    @Schema(description="昵称")
    private String nickname;

    /**
     * 性别：0女/1男
     */
    @Schema(description="性别：0女/1男")
    private Integer gender;

    /**
     * 生日
     */
    @Schema(description="生日")
    private LocalDateTime birthday;

    /**
     * 身份证号
     */
    @Schema(description="身份证号")
    private String identityCard;

    /**
     * 手机区号
     */
    @Schema(description="手机区号")
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @Schema(description="移动电话")
    private String mobile;

    /**
     * 电话区号
     */
    @Schema(description="电话区号")
    private String telAreaCode;

    /**
     * 电话
     */
    @Schema(description="电话")
    private String tel;

    /**
     * 邮箱地址
     */
    @Schema(description="邮箱地址")
    private String email;

    /**
     * 公司名称
     */
    @Schema(description="公司名称")
    private String company;

    /**
     * 部门
     */
    @Schema(description="部门")
    private String department;

    /**
     * 职位
     */
    @Schema(description="职位")
    private String position;

    /**
     * QQ号
     */
    @Schema(description="QQ号")
    private String qq;

    /**
     * whatsapp号
     */
    @Schema(description="whatsapp号")
    private String whatsapp;

    /**
     * 微信号
     */
    @Schema(description="微信号")
    private String wechat;

    /**
     * 微信昵称
     */
    @Schema(description="微信昵称")
    private String wechatNickname;

    /**
     * 微信头像URL
     */
    @Schema(description="微信头像URL")
    private String wechatIconUrl;

    /**
     * 锁定标记，0未锁定，1已锁定，锁定后不能登录
     */
    @Schema(description="锁定标记，0未锁定，1已锁定，锁定后不能登录")
    private Integer isLockFlag;

    /**
     * 删除标记，0未删除，1已删除
     */
    @Schema(description="删除标记，0未删除，1已删除")
    private Integer isDelFlag;


    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;

    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}