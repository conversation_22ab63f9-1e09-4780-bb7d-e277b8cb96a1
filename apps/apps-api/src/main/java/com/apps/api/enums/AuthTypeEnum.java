package com.apps.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 登录认证类型-Auth
 */

@Getter
@AllArgsConstructor
public enum AuthTypeEnum {

    PASSWORD("password", "密码"),
    SMS("sms", "验证码"),
    ;

    private String code;

    private String msg;

    public static String getEnumByCode(String code) {
        for (AuthTypeEnum value : AuthTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }

}
