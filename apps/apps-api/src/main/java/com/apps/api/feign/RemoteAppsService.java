package com.apps.api.feign;

import com.apps.api.dto.AppLoginDto;
import com.apps.api.dto.coupon.SaveCouponUserDto;
import com.apps.api.dto.partner.SavePartnerUserDto;
import com.apps.api.dto.partner.UpdatePartnerLockDto;
import com.apps.api.dto.partner.UpdateUserRoleDto;
import com.apps.api.dto.system.ResetPasswordDto;
import com.apps.api.vo.system.UserPermissionVo;
import com.common.core.util.R;
import com.common.feign.annotation.NoToken;
import com.common.mybatis.tenant.FzhFeignTenantInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/1/15  16:44
 * @Version 1.0
 */
@FeignClient(contextId = "remoteAppsService", value = "apps-biz", configuration = FzhFeignTenantInterceptor.class)
public interface RemoteAppsService {

    @NoToken
    @PostMapping("/verify/verifyLoginUser")
    R<UserPermissionVo> verifyLoginUser(@RequestBody AppLoginDto loginDto);

    @NoToken
    @PostMapping("/user/registryUser")
    R<Long> registryUser(@RequestBody SavePartnerUserDto partnerUserDto);

    @NoToken
    @PostMapping("/user/saveCouponUser")
    R<Long> saveCouponUser(@RequestBody SaveCouponUserDto couponUserDto);

    @NoToken
    @GetMapping("/user/getUserRoleNameList")
    R<Map<Long, Map<String, String>>> getUserRoleNameList(@RequestParam("userIdList") List<Long> userIdList);

    @NoToken
    @PostMapping("/user/lockUser")
    R lockUser(@RequestBody UpdatePartnerLockDto lockDto);

    @NoToken
    @GetMapping("/user/getPlatformUserByRole")
    R<List<Long>> getPlatformUserByRole(@RequestParam("platformId") Long platformId,
                                        @RequestParam("platformCode") String platformCode,
                                        @RequestParam("roleCodes") List<String> roleCodes);

    @NoToken
    @GetMapping("/user/getRoleList")
    R<List<Map<String, Object>>> getRoleList(@RequestParam("platformId") Long platformId, @RequestParam("platformCode") String platformCode);

    @NoToken
    @PostMapping("/user/resetPartnerUserPassword")
    R<Boolean> resetPartnerUserPassword(@RequestBody ResetPasswordDto resetPasswordDto);

    /**
     * 修改用户角色
     */
    @NoToken
    @PostMapping("/user/updateUserRole")
    R updateUserRole(@RequestBody UpdateUserRoleDto updateUserRoleDto);

    /**
     * 删除用户缓存并下线
     */
    @NoToken
    @DeleteMapping("/user/delUserCache")
    R delUserCache(@RequestParam("userId") Long userId);

    /**
     * 根据用户获取角色Id
     */
    @NoToken
    @PostMapping("/user/getRoleIdByUserId")
    R<Long> getRoleIdByUserId(@RequestParam("userId") Long userId);


    /**
     * 批量下线用户
     *
     * @param loginIds
     * @return
     */
    @NoToken
    @GetMapping("/user/userOfferLine")
    R userOfferLine(@RequestParam("loginIds") List<String> loginIds);


    /**
     * 获取openId
     *
     * @param code
     * @param appId
     * @param secret
     * @return
     */
    @NoToken
    @GetMapping("/user/getOpenId")
    R<String> getOpenId(@RequestParam("code") String code, @RequestParam("appId") String appId, @RequestParam("secret") String secret);
}
