package com.apps.api.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/1/14  11:22
 * @Version 1.0
 */
@Data
public class SaveRoleDto {

    @Schema(description = "角色Id")
    private Long id;

    @Schema(description = "平台应用Id")
    @NotNull(message = "平台应用Id不能为空")
    private Long fkPlatformId;

    @Schema(description = "平台应用CODE")
    @NotBlank(message = "平台应用CODE不能为空")
    private String fkPlatformCode;

    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    @Schema(description = "角色名称（英文）")
    private String roleNameEn;

    @Schema(description = "角色CODE")
    @NotBlank(message = "角色CODE不能为空")
    private String roleCode;

    @Schema(description = "角色描述")
    private String roleDesc;
}
