package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_role")
@Schema(description = "角色")
public class SystemRoleEntity extends Model<SystemRoleEntity> {
    private static final long serialVersionUID = 1L;


    /**
     * 角色Id
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "角色Id")
    private Long id;

    /**
     * 平台应用Id
     */
    @Schema(description = "平台应用Id")
    private Long fkPlatformId;

    /**
     * 平台应用CODE
     */
    @Schema(description = "平台应用CODE")
    private String fkPlatformCode;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 角色名称（英文）
     */
    @Schema(description = "角色名称（英文）")
    private String roleNameEn;

    /**
     * 角色CODE
     */
    @Schema(description = "角色CODE")
    private String roleCode;

    /**
     * 角色描述
     */
    @Schema(description = "角色描述")
    private String roleDesc;

    /**
     * 删除标记，0未删除，1已删除
     */
    @Schema(description = "删除标记，0未删除，1已删除")
    private Integer isDelFlag;

    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;


    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}