package com.apps.api.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @Author:Oliver
 * @Date: 2025/4/24
 * @Version 1.0
 * @apiNote:重置密码DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResetPasswordDto {

    @Schema(description = "登录账号")
    @NotBlank(message = "登录账号不能为空")
    private String loginId;
}
