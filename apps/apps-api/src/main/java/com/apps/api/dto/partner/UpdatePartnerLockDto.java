package com.apps.api.dto.partner;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:<PERSON>
 * @Date: 2025/1/16  17:46
 * @Version 1.0
 * 更新Partner用户锁定状态DTO
 */
@Data
public class UpdatePartnerLockDto {

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "0启用 1停用")
    @NotNull(message = "状态不能为空")
    private Integer lockFlag;
}
