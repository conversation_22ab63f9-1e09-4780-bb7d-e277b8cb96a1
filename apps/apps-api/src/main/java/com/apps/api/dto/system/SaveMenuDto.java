package com.apps.api.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/1/14  10:37
 * @Version 1.0
 * 保存菜单dto
 */
@Data
public class SaveMenuDto {

    @Schema(description = "菜单Id")
    private Long id;

    @Schema(description = "平台应用Id")
    @NotNull(message = "平台应用Id不能为空")
    private Long fkPlatformId;

    @Schema(description = "平台应用CODE")
    @NotBlank(message = "平台应用CODE不能为空")
    private String fkPlatformCode;

    @Schema(description = "父菜单Id")
    private Long fkParentMenuId;

    @Schema(description = "菜单类型：0目录，1菜单，2按钮，3自定义")
    private Integer menuType;

    @Schema(description = "权限标识")
    private String permissionKey;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单名称（英文）")
    private String nameEn;

    @Schema(description = "路由路径")
    private String path;

    @Schema(description = "是否可见，0隐藏，1显示")
    private Integer isVisible;

    @Schema(description = "是否缓存，0否，1是")
    private Integer isKeepAlive;

    @Schema(description = "是否内嵌，0否，1是")
    private Integer isEmbedded;

    @Schema(description = "排序值，越小越靠前")
    private Integer viewOrder;
}
