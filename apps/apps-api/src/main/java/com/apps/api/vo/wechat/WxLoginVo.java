package com.apps.api.vo.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * 微信授权登录返回参数
 */
@Data
public class WxLoginVo {

    @Schema(description = "会话密钥")
    @JsonProperty("session_key")
    private String sessionKey;

    @Schema(description = "用户在开放平台的唯一标识符")
    private String unionid;

    @Schema(description = "错误信息")
    private String errmsg;

    @Schema(description = "用户唯一标识")
    private String openid;

    @Schema(description = "错误码")
    private Integer errcode;
}
