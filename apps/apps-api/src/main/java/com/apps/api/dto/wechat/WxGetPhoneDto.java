package com.apps.api.dto.wechat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * app登录DTO
 */
@Data
public class WxGetPhoneDto {

    @Schema(description = "小程序 appId")
    @NotBlank(message = "小程序appId不能为空")
    private String appId;

    @Schema(description = "code")
    @NotBlank(message = "code不能为空")
    private String code;

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

}
