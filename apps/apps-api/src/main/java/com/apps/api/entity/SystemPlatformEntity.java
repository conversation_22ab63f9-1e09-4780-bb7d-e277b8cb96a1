package com.apps.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("system_platform")
@Schema(description = "平台应用")
public class SystemPlatformEntity extends Model<SystemPlatformEntity> {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @Schema(description="菜单Id")
    private Long id;

    /**
     * 平台应用名称
     */
    @Schema(description="平台应用名称")
    private String name;

    /**
     * 平台应用名称（英文）
     */
    @Schema(description="平台应用名称（英文）")
    private String nameEn;

    /**
     * 平台应用CODE
     */
    @Schema(description="平台应用CODE")
    private String code;

    @Schema(description="登录方式Key：MP-微信小程序/ACCOUNT-账号密码/VCODE-验证码(手机或邮件)")
    private String loginTypeKey;

    @Schema(description="登录时需要获取的业务数据方法")
    private String loginFunction;

    /**
     * 状态，0已停用，1启用中
     */
    @Schema(description="状态，0已停用，1启用中")
    private Integer status;

    @Schema(description="创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description="创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description="修改时间")
    private LocalDateTime gmtModified;


    @Schema(description="修改用户(登录账号)")
    private String gmtModifiedUser;




}