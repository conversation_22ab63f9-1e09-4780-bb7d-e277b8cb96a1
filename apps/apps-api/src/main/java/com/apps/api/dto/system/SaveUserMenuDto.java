package com.apps.api.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/1/14  11:03
 * @Version 1.0
 */
@Data
public class SaveUserMenuDto {

    @Schema(description = "用户Id")
    @NotNull(message = "用户IdId不能为空")
    private Long userId;

    @Schema(description = "菜单Id集合-传空表示删除全部权限")
    private List<Long> menuIds;
}
