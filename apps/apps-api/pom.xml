<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2024 fzh Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fzh</groupId>
        <artifactId>apps</artifactId>
        <version>3.8.1</version>
    </parent>

    <artifactId>apps-api</artifactId>
    <packaging>jar</packaging>

    <description>fzh</description>


    <dependencies>
        <!--core 工具类-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-core</artifactId>
            <version>3.8.3</version>
        </dependency>
        <!--feign 注解依赖-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <!--mybatis 依赖-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>
    </dependencies>
</project>
