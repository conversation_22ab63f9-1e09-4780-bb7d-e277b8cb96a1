package com.apps.service;

import com.apps.api.entity.SystemUserRoleEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SystemUserRoleService extends IService<SystemUserRoleEntity> {

    /**
     * 批量获取用户角色名称
     * @param userIdList
     * @return
     */
    Map<Long,Map<String,String>> getUserRoleNameList(List<Long> userIdList);


    /**
     * 根据用户获取角色id
     *
     * @param userId
     * @return
     */
    Long getRoleIdByUserId(Long userId);

}
