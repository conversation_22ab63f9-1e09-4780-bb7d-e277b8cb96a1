package com.apps.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.apps.api.entity.SystemRoleEntity;
import com.apps.api.entity.SystemUserEntity;
import com.apps.api.entity.SystemUserRoleEntity;
import com.apps.api.enums.GlobExceptionEnum;
import com.apps.exception.AppsGlobalException;
import com.apps.mapper.SystemRoleMapper;
import com.apps.mapper.SystemUserMapper;
import com.apps.mapper.SystemUserRoleMapper;
import com.apps.service.SystemUserRoleService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SystemUserRoleServiceImpl extends ServiceImpl<SystemUserRoleMapper, SystemUserRoleEntity> implements SystemUserRoleService {

    private final SystemUserRoleMapper systemUserRoleMapper;
    private final SystemRoleMapper roleMapper;
    private final SystemUserMapper userMapper;

    @Override
    public Map<Long, Map<String, String>> getUserRoleNameList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> userLockMap = userMapper.selectList(new LambdaQueryWrapper<SystemUserEntity>().in(SystemUserEntity::getId, userIdList))
                .stream()
                .collect(Collectors.toMap(SystemUserEntity::getId, SystemUserEntity::getIsLockFlag));

        Map<Long, List<SystemUserRoleEntity>> userRoleMap = systemUserRoleMapper.selectList(new LambdaQueryWrapper<SystemUserRoleEntity>()
                        .in(SystemUserRoleEntity::getFkUserId, userIdList))
                .stream().collect(Collectors.groupingBy(SystemUserRoleEntity::getFkUserId));

        Map<Long, Map<String, String>> userRoleNameMap = new HashMap<>();
        //循环分组数据
        userRoleMap.forEach((userId, userRoles) -> {
            // 提取所有的角色ID
            List<Long> roleIds = userRoles.stream()
                    .map(SystemUserRoleEntity::getFkRoleId)
                    .collect(Collectors.toList());

            // 查询角色表，获取对应的角色名称
            List<SystemRoleEntity> roleList = roleMapper.selectList(new LambdaQueryWrapper<SystemRoleEntity>()
                    .in(SystemRoleEntity::getId, roleIds));
            // 将角色名称转换为列表
            List<String> roleNames = roleList.stream().map(SystemRoleEntity::getRoleName).collect(Collectors.toList());
            List<String> roleCodes = roleList.stream().map(SystemRoleEntity::getRoleCode).collect(Collectors.toList());
            // 拼接角色名称，用逗号分隔
            String roleNameStr = String.join(",", roleNames);
            String roleCodeStr = String.join(",", roleCodes);
            // 将用户ID与角色名称映射存入返回的Map
            Map<String, String> roleMap = new HashMap<>();
            roleMap.put("roleName", roleNameStr);
            roleMap.put("roleCode", roleCodeStr);
            roleMap.put("lockFlag", Objects.nonNull(userLockMap.get(userId))?userLockMap.get(userId).toString():"0");
            userRoleNameMap.put(userId, roleMap);
        });
        return userRoleNameMap;
    }

    /**
     * 根据用户获取角色id
     *
     * @param userId
     * @return
     */
    @Override
    public Long getRoleIdByUserId(Long userId) {
        LambdaQueryWrapper<SystemUserRoleEntity> systemRoleEntityLambdaQueryWrapper = new LambdaQueryWrapper<SystemUserRoleEntity>()
                .eq(SystemUserRoleEntity :: getFkUserId, userId);
        SystemUserRoleEntity systemUserRoleEntity = this.systemUserRoleMapper.selectOne(systemRoleEntityLambdaQueryWrapper);
        return Optional.ofNullable(systemUserRoleEntity)
                .map(SystemUserRoleEntity :: getFkRoleId)
                .orElseThrow(() ->new AppsGlobalException(GlobExceptionEnum.ROLE_NOT_EXIST));
    }

}
