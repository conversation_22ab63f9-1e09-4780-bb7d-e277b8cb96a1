package com.apps.controller.system;

import com.apps.service.SystemMenuService;
import com.apps.service.SystemRoleMenuService;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author:Oliver
 * @Date: 2025/1/13  18:46
 * @Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping("/system/menu")
@Tag(description = "系统菜单管理", name = "系统菜单管理")
@Inner(value = false)
public class SystemMenuController {

    private final SystemRoleMenuService roleMenuService;
    private final SystemMenuService menuService;

    @Operation(summary = "获取角色对应的菜单权限", description = "获取角色对应的菜单权限")
    @GetMapping("/getRoleMenu/{roleId}")
    public R getRoleMenu(@PathVariable("roleId") Long roleId) {
        return R.ok(roleMenuService.getRoleMenu(roleId));
    }

    @Operation(summary = "获取用户对应的菜单权限", description = "获取用户对应的菜单权限")
    @GetMapping("/getUserMenu/{userId}")
    public R getUserMenu(@PathVariable("userId") Long userId) {
        return R.ok(roleMenuService.getUserMenu(userId));
    }
}
