<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 fzh Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.fzh</groupId>
		<artifactId>apps</artifactId>
		<version>3.8.1</version>
	</parent>

    <artifactId>apps-biz</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>upms-api</artifactId>
			<version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>apps-api</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.fzh</groupId>
            <artifactId>app-partner-api</artifactId>
            <version>3.8.1</version>
        </dependency>
        <!--文件管理-->
        <dependency>
			<groupId>com.fzh</groupId>
            <artifactId>common-oss</artifactId>
        </dependency>
        <!--feign 调用-->
        <dependency>
			<groupId>com.fzh</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
			<groupId>com.fzh</groupId>
            <artifactId>common-security</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
			<groupId>com.fzh</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <!--接口文档-->
        <dependency>
			<groupId>com.fzh</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>
        <!-- orm 模块-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- 阿里云短信下发 -->
        <dependency>
            <groupId>io.springboot.sms</groupId>
            <artifactId>aliyun-sms-spring-boot-starter</artifactId>
        </dependency>
        <!--xss 过滤-->
        <dependency>
			<groupId>com.fzh</groupId>
            <artifactId>common-xss</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--腾讯云短信下发-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.1000</version>
        </dependency>
        <!-- 邮件发送 -->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.1</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>boot</id>
        </profile>
        <profile>
            <id>cloud</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.xlsx</exclude>
                    <exclude>**/*.xls</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>

                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>

                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.apps.AppsApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
