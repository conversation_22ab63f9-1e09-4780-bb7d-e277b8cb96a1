<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, <PERSON><PERSON> All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the fzh developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: <PERSON><PERSON> (<EMAIL>)
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.admin.mapper.SysRoleMapper">
	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.admin.api.entity.SysRole">
		<id column="role_id" property="roleId"/>
		<result column="role_name" property="roleName"/>
		<result column="role_code" property="roleCode"/>
		<result column="role_desc" property="roleDesc"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
		<result column="del_flag" property="delFlag"/>
	</resultMap>

	<!-- 通过用户ID，查询角色信息-->
	<select id="listRolesByUserId" resultMap="BaseResultMap">
		SELECT sys_role.role_id,
			   sys_role.role_name,
			   sys_role.role_code,
			   sys_role.role_desc,
			   sys_role.create_time,
			   sys_role.update_time,
			   sys_role.del_flag
		FROM sys_role,
			 sys_user_role
		WHERE sys_role.role_id = sys_user_role.role_id
		  AND sys_role.del_flag = '0'
		  and sys_user_role.user_id = #{userId}
	</select>
</mapper>
