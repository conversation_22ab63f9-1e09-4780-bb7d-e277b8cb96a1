server:
  port: 4000

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:************}:${NACOS_PORT:8852}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
  main:
    allow-bean-definition-overriding: true

#配置日志
logging:
  config: classpath:log/<EMAIL>@.xml
