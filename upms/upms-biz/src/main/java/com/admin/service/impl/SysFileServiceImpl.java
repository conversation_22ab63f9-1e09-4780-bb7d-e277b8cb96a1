/*
 *    Copyright (c) 2018-2025, <PERSON><PERSON> All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the fzh developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package com.admin.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.amazonaws.services.s3.model.S3Object;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.admin.api.entity.SysFile;
import com.admin.mapper.SysFileMapper;
import com.admin.service.SysFileService;
import com.common.core.util.R;
import com.common.file.core.FileProperties;
import com.common.file.core.FileTemplate;
import javax.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @date 2024-06-18 17:18:42
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysFileServiceImpl extends ServiceImpl<SysFileMapper, SysFile> implements SysFileService {

	private final FileTemplate fileTemplate;

	private final FileProperties properties;

	/**
	 * 上传文件
	 * @param file
	 * @param isPrivate 是否私有桶
	 * @return
	 */
	@Override
	public R uploadFile(MultipartFile file, Boolean isPrivate) {
		String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
		String bucketName = properties.getPublicBucketName();
		String savePath = "test/" + fileName;
		Map<String, String> resultMap = new HashMap<>(4);
		if(isPrivate){
			bucketName = properties.getPrivateBucketName();
			resultMap.put("bucketName", properties.getPrivateBucketName());
		}else{
			resultMap.put("bucketName", properties.getBucketName());
		}
		resultMap.put("fileName", fileName);
		resultMap.put("pathUrl", String.format("/admin/%s/%s", bucketName, fileName));

		try (InputStream inputStream = file.getInputStream()) {
			fileTemplate.putObject(bucketName, savePath, inputStream, file.getContentType());
		}
		catch (Exception e) {
			log.error("上传失败", e);
			return R.failed(e.getLocalizedMessage());
		}
		return R.ok(resultMap);
	}

	/**
	 * 读取文件
	 * @param bucket
	 * @param fileName
	 * @param isPrivate 是否私有桶
	 * @param response
	 */
	@Override
	public void getFile(String bucket, String fileName, HttpServletResponse response,Boolean isPrivate) {
		if(StrUtil.isBlank(bucket)){
			if(isPrivate){
				bucket = properties.getPrivateBucketName();
			}else{
				bucket = properties.getPublicBucketName();
			}
		}
		try (S3Object s3Object = fileTemplate.getObject(bucket, fileName)) {
			response.setContentType("application/octet-stream; charset=UTF-8");
			IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
		}
		catch (Exception e) {
			log.error("文件读取异常: {}", e.getLocalizedMessage());
		}
	}
	@Override
	public void getFile(String bucket, String fileName, HttpServletResponse response) {
		try (S3Object s3Object = fileTemplate.getObject(bucket, fileName)) {
			response.setContentType("application/octet-stream; charset=UTF-8");
			IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
		}
		catch (Exception e) {
			log.error("文件读取异常: {}", e.getLocalizedMessage());
		}
	}

	/**
	 * 删除文件
	 * @param id
	 * @param isPrivate 是否私有桶
	 * @return
	 */
	@Override
	@SneakyThrows
	@Transactional(rollbackFor = Exception.class)
	public Boolean deleteFile(Long id, Boolean isPrivate) {
		String bucketName = properties.getPublicBucketName();
		if(isPrivate){
			bucketName = properties.getPrivateBucketName();
		}
		SysFile file = this.getById(id);
		if (Objects.isNull(file)) {
			return Boolean.FALSE;
		}
		fileTemplate.removeObject(bucketName, file.getFileName());
		return this.removeById(id);
	}

	@Override
	@SneakyThrows
	@Transactional(rollbackFor = Exception.class)
	public Boolean deleteFile(Long id) {
		String bucketName = properties.getPublicBucketName();
		SysFile file = this.getById(id);
		if (Objects.isNull(file)) {
			return Boolean.FALSE;
		}
		fileTemplate.removeObject(bucketName, file.getFileName());
		return this.removeById(id);
	}

}
