package com.auth.rocketmq.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.auth.rocketmq.msg.UserOfflineDto;
import com.common.core.constant.CacheConstants;
import com.common.security.service.FzhToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/3/20
 * @Version 1.0
 * @apiNote:
 */
@Slf4j
@RocketMQMessageListener(
        topic = "user_offline_topic",
        consumerGroup = "user_offline_topic_group",
        maxReconsumeTimes = 3,
        consumeMode = ConsumeMode.CONCURRENTLY
)
@Component
public class UserOfflineConsumer implements RocketMQListener<UserOfflineDto> {

    @Autowired
    private OAuth2AuthorizationService authorizationService;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void onMessage(UserOfflineDto offlineDto) {
        log.info("收到用户下线消息，offlineDto={}", offlineDto);
        try {
            List<String> loginIds = offlineDto.getLoginIds();
            log.info("用户下线处理,用户信息:{},数量:{}", loginIds, loginIds.size());
            for (String loginId : loginIds) {

                Boolean isClear = Boolean.TRUE;
                String formPlatformId = offlineDto.getPlatformId().toString();

                Cache tokenCache = cacheManager.getCache(formPlatformId + ":" + CacheConstants.USER_DETAILS);
                if (tokenCache != null) {
                    Cache.ValueWrapper cacheObj = tokenCache.get(formPlatformId + ":" + loginId);
                    if (Objects.isNull(cacheObj) || Objects.isNull(cacheObj.get())) {
                        log.info("用户下线处理,token为空,无需处理,用户信息:{}", loginId);
                        isClear = Boolean.FALSE;
                    }

                    if (Objects.nonNull(cacheObj) && Objects.nonNull(cacheObj.get())) {
                        //扩展用户信息的token
                        Cache.ValueWrapper valueWrapper = tokenCache.get("2:" + formPlatformId + ":" + loginId + ":token");
                        if (valueWrapper != null) {
                            Object o = valueWrapper.get();
                            FzhToken fzhToken = JSONObject.parseObject(JSONObject.toJSONString(o), FzhToken.class);
                            if (StringUtils.isBlank(fzhToken.getToken())) {
                                log.info("用户下线处理失败,token为空,用户信息:{}", loginId);
                                isClear = Boolean.FALSE;
                            }

                            String token = StringUtils.isNoneBlank(fzhToken.getToken()) ? fzhToken.getToken() : "";
                            OAuth2Authorization authorization = authorizationService.findByToken(token, OAuth2TokenType.ACCESS_TOKEN);
                            if (Objects.isNull(authorization)) {
                                log.info("用户下线处理失败,OAuth2Authorization为空,用户信息:{}", loginId);
                                isClear = Boolean.FALSE;
                            }else {
                                OAuth2Authorization.Token<OAuth2AccessToken> accessToken = authorization.getAccessToken();
                                if (accessToken == null || StrUtil.isBlank(accessToken.getToken().getTokenValue())) {
                                    log.info("用户下线处理失败,token不存在,用户信息:{}", loginId);
                                    isClear = Boolean.FALSE;
                                }
                                // 清空用户信息（立即删除）
                                Cache cache = cacheManager.getCache(formPlatformId + ":" + CacheConstants.USER_DETAILS);
                                if (cache != null) {
                                    String key = formPlatformId + ":" + authorization.getPrincipalName();
                                    cache.evictIfPresent(key);
                                    log.info("已删除用户缓存:key:{},用户ID={}", key, loginId);
                                }
                                // 清空access token
                                authorizationService.remove(authorization);
                                log.info("用户下线处理成功，用户信息={}", loginId);
                            }

                        } else {
                            isClear = Boolean.FALSE;
                        }
                    }

                } else {
                    isClear = Boolean.FALSE;
                }

                //如果删除不成功,直接删除缓存-兜底
                if (!isClear) {
                    log.info("开始兜底处理,用户信息:{}", loginId);
                    Object object = redisTemplate.opsForValue().get("2:user_details::" + formPlatformId + ":" + loginId);
                    if (Objects.nonNull(object)) {
                        log.info("直接删除查询到缓存:{},key:{}", loginId, "2:user_details::" + formPlatformId + ":" + loginId);
                        Boolean delete = redisTemplate.delete("2:user_details::2:" + loginId);
                        log.info("直接删除缓存是否成功:{}", delete);
                    } else {
                        log.info("直接删除查询不到缓存:用户:{},key:{}", loginId, "2:user_details::" + formPlatformId + ":" + loginId);
                    }
                }

            }
        } catch (Exception e) {
            log.error("用户下线消费异常，用户信息={}，错误={}", offlineDto, e.getMessage());
            throw new RuntimeException("用户下线消费失败，触发重试机制", e);
        }
    }

}
